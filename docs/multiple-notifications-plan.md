# Multiple Time Notifications Implementation Plan

## Overview
Implement a notification system that allows users to set multiple specific times for habit reminders using iOS 17+ UserNotifications framework with SwiftUI integration.

## Research Summary

Based on Perplexity research, implementing multiple specific time notifications on iOS 17+ with SwiftUI requires a comprehensive approach that balances user experience, system limitations, and technical implementation. Key findings:

- iOS allows up to 64 pending local notifications per app
- UserNotifications framework requires explicit user consent
- UNCalendarNotificationTrigger provides precise time-based scheduling
- SwiftUI 6 @Observable protocol for reactive state management
- Notification actions enable interactive responses

## Key Components to Implement

### 1. NotificationManager Service
- Create `NotificationManager` class with `@Observable` protocol for SwiftUI 6 compatibility
- Handle authorization status and permission requests
- Manage notification scheduling, updating, and cancellation
- Implement delegate for handling notification responses

### 2. Notification Permission Flow
- Check current authorization status on app launch
- Request permissions with appropriate options (.alert, .badge, .sound)
- Handle different permission states (authorized, denied, notDetermined)
- Provide settings redirect for denied permissions

### 3. Multiple Time Scheduling System
- Use `UNCalendarNotificationTrigger` for precise time-based notifications
- Support up to 64 pending notifications (iOS limit)
- Implement efficient scheduling/rescheduling algorithms
- Handle notification cleanup for expired or cancelled habits

### 4. Habit Integration
- Extend existing Habit model to include notification times
- Add notification scheduling when habits are created/updated
- Remove notifications when habits are deleted or archived
- Support per-habit notification customization

### 5. UI Components
- Add time picker interface for setting multiple notification times
- Show notification status and permission states
- Provide notification management in habit settings
- Display scheduled notification preview

### 6. Notification Actions
- Implement "Mark Complete" and "Snooze" actions
- Handle notification responses in app delegate
- Update habit records when actions are taken
- Provide deep linking to specific habits

## Implementation Steps

1. **Create NotificationManager service** - Core notification handling logic
2. **Add permission management** - Authorization flow and status tracking  
3. **Implement scheduling system** - Multiple time notification scheduling
4. **Extend Habit model** - Add notification times support
5. **Create UI components** - Time picker and notification settings
6. **Add notification actions** - Interactive notification responses
7. **Integrate with existing app** - Connect to habit creation/editing flows
8. **Test and optimize** - Verify scheduling limits and performance

## Technical Considerations

### iOS System Limitations
- Work within iOS 64-notification limit per app
- Handle background processing restrictions
- Manage battery and performance impact

### CloudKit Compatibility
- Use CloudKit-compatible data models for notification times
- Ensure proper synchronization across devices
- Handle offline scenarios

### User Experience
- Implement efficient cleanup for expired notifications
- Handle app state transitions and background scheduling
- Provide fallback for users who deny permissions
- Prevent notification fatigue with smart scheduling

## Code Examples from Research

### Basic NotificationManager Structure
```swift
import UserNotifications
import SwiftUI

@Observable
class NotificationManager {
    var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    func requestAuthorization() async {
        let center = UNUserNotificationCenter.current()
        
        do {
            let granted = try await center.requestAuthorization(options: [.alert, .badge, .sound])
            await MainActor.run {
                self.authorizationStatus = granted ? .authorized : .denied
            }
        } catch {
            print("Authorization request failed: \(error)")
        }
    }
    
    func scheduleMultipleDailyNotifications(times: [DateComponents]) async {
        let center = UNUserNotificationCenter.current()
        
        // Remove existing notifications to avoid duplicates
        center.removeAllPendingNotificationRequests()
        
        for (index, time) in times.enumerated() {
            let content = UNMutableNotificationContent()
            content.title = "Daily Reminder \(index + 1)"
            content.body = "Time for your scheduled activity"
            content.sound = .default
            content.badge = NSNumber(value: index + 1)
            
            let trigger = UNCalendarNotificationTrigger(
                dateMatching: time,
                repeats: true
            )
            
            let request = UNNotificationRequest(
                identifier: "daily-notification-\(index)",
                content: content,
                trigger: trigger
            )
            
            do {
                try await center.add(request)
            } catch {
                print("Failed to schedule notification \(index): \(error)")
            }
        }
    }
}
```

### Notification Actions Setup
```swift
func setupNotificationCategories() {
    let center = UNUserNotificationCenter.current()
    
    let completeAction = UNNotificationAction(
        identifier: "COMPLETE_ACTION",
        title: "Mark Complete",
        options: [.foreground]
    )
    
    let snoozeAction = UNNotificationAction(
        identifier: "SNOOZE_ACTION",
        title: "Snooze 5 min",
        options: []
    )
    
    let category = UNNotificationCategory(
        identifier: "TASK_CATEGORY",
        actions: [completeAction, snoozeAction],
        intentIdentifiers: [],
        options: []
    )
    
    center.setNotificationCategories([category])
}
```

This plan focuses on MVP implementation while ensuring scalability and good user experience according to iOS 17+ best practices and system limitations.