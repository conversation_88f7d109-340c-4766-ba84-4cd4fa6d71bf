# Progressive Check Button Data Flow Analysis

## Overview
The "Progressive Check Button" is the circular button in each habit card that allows users to incrementally track habit completion throughout the day. It supports progressive completion for habits that need to be done multiple times per day.

## Complete Data Flow

### 1. UI Layer (HabitRowView.swift:68-148)
**Location**: `/Components/HabitRowView.swift:68-148`

- **Button Tap**: User taps the circular progress button
- **Visual States**: 
  - Empty state (0 completion)
  - Progress state (partial completion with number)
  - Complete state (checkmark when at/above target)
- **Progressive Visual Feedback**: Ring fills progressively, color opacity changes based on completion ratio

### 2. Event Handler (DashboardView.swift:362)
**Location**: `/Views/DashboardView.swift:362`

- **Callback Binding**: `onToggle: viewModel.toggleHabit`
- **Passes**: Habit object to ViewModel

### 3. Business Logic (DashboardViewModel.swift:75-82)
**Location**: `/ViewModels/DashboardViewModel.swift:75-82`

```swift
func toggleHabit(_ habit: Habit) {
    do {
        try repository.toggleHabitCompletion(habit)
        // SwiftData @Model objects automatically update - no need to reload
    } catch {
        errorMessage = "Failed to update habit: \(error.localizedDescription)"
    }
}
```

### 4. Data Persistence (HabitRepository.swift:138-162)
**Location**: `/Repositories/HabitRepository.swift:138-162`

**Progressive Logic**:
- Finds today's `HabitRecord` for the habit
- **If record exists**: 
  - If `currentLevel >= timesPerDay`: Reset to 0 (cycle back)
  - If `currentLevel < timesPerDay`: Increment by 1
- **If no record**: Create new `HabitRecord` with `completionLevel = 1`
- Saves to SwiftData context

### 5. Data Model Updates (Habit.swift:72-79)
**Location**: `/Models/Habit.swift:72-79`

**Computed Properties Auto-Update**:
- `todayCompletionLevel`: Returns current completion level for today
- `isCompletedToday`: Returns true when `completionLevel >= timesPerDay`
- `subtitle`: Updates to show remaining count or completion status

### 6. SwiftData Auto-Sync
- SwiftData's `@Observable` and `@Model` automatically propagate changes
- UI updates reactively without manual refresh
- CloudKit sync happens automatically in background

## Key Technical Details

### Progressive Completion Logic
- **Single-time habits** (timesPerDay = 1): Toggle between 0 → 1 → 0
- **Multi-time habits** (timesPerDay > 1): Cycle through 0 → 1 → 2 → ... → max → 0

### Visual Feedback System
- **Progress Ring**: Fills based on `completionLevel / timesPerDay` ratio  
- **Color Opacity**: Uses same ratio for progressive color intensity
- **Icon State**: Number (partial) → Checkmark (complete) → Empty (reset)

### Data Persistence
- Each tap creates/updates a `HabitRecord` for today
- `HabitRecord.completionLevel` stores the current progress
- All changes immediately saved to SwiftData with CloudKit sync

## Flow Summary
1. **User taps** → Progressive check button in HabitRowView
2. **Event flows** → DashboardView.onToggle → DashboardViewModel.toggleHabit
3. **Business logic** → HabitRepository.toggleHabitCompletion
4. **Data updates** → Creates/updates HabitRecord with incremented completionLevel
5. **UI updates** → SwiftData @Observable auto-updates UI with new completion state
6. **Sync** → CloudKit automatically syncs changes across devices