# N-Level Habit Grid Implementation Plan

## Overview
Implement a dynamic n-level habit grid system that replaces the current fixed 5-level system with a user-configurable system where `n = timesPerDay` set by the user in CreateHabitView.

## Current State Analysis

### ✅ What's Already Implemented
- `HabitGridView.swift` with GitHub-style grid (325 lines)
- **Month Labels System** *(lines 33-49)*:
  - `monthLabelsView` with abbreviated names (Jan, Feb, Mar...)
  - Dynamic width calculation: `CGFloat(monthInfo.weeks * 12)`
  - 7px font size, TextMuted color, 10px height
- **Day Indicators** *(lines 51-64)*:
  - Vertical labels: S,M,T,W,T,F,S
  - 8px font size, TextMuted color
  - 10px width, positioned left of grid
- Fixed 5-level intensity system (0-4) in `cellBackgroundColor()`
- `timesPerDay` field exists in `CreateHabitView.swift`
- Today indicator (red border) and start date indicator (🚀 emoji)
- Auto-scroll to recent weeks functionality
- Touch/drag scrolling support

### ⚠️ Current Limitations
- Hardcoded 5-level color system in `cellBackgroundColor()` function
- Fixed opacity values: `0.15, 0.3, 0.4, 0.7, 1.0`
- No dynamic level calculation based on habit settings
- Sample data generation limited to 0-4 range

## Implementation Plan

### Phase 1: Core Algorithm Design ✅

**N-Level Transparency Algorithm:**
```swift
func calculateOpacity(level: Int, maxLevel: Int) -> Double {
    if level == 0 { return 0.15 } // No activity (gray)
    return 0.2 + (0.8 * Double(level) / Double(maxLevel))
    // Results: 0.2 → 1.0 range for completion levels
}
```

**Examples:**
- **Single Habit (n=1):**
  - Level 0: Gray (0.15 opacity) - not done
  - Level 1: Green (1.0 opacity) - completed

- **Water Habit (n=8):**
  - Level 0: Gray (0.15) - 0 glasses
  - Level 1: Green (0.3) - 1 glass  
  - Level 4: Green (0.6) - 4 glasses
  - Level 8: Green (1.0) - 8 glasses (full)

- **Reading Habit (n=3):**
  - Level 0: Gray (0.15) - 0 pages
  - Level 1: Green (0.47) - 1 page
  - Level 2: Green (0.73) - 2 pages  
  - Level 3: Green (1.0) - 3 pages (target met)

### Phase 2: Data Model Updates

**Required Changes:**
1. Add `timesPerDay: Int` to Habit model in `HabitGridView.swift` (line 327)
2. Ensure `HabitDay.level` supports 0 to n range (currently Int, sufficient)
3. `HabitRecord.completionLevel` (Int16) already sufficient for large n values

### Phase 3: Month Labels & Day Indicators (Current Implementation)

**Month Labels Implementation:**
```swift
// HabitGridView.swift:33-49
private var monthLabelsView: some View {
    HStack(spacing: 2) {
        ForEach(months, id: \.month) { monthInfo in
            Text(monthInfo.label) // "Jan", "Feb", etc.
                .font(.system(size: 7))
                .foregroundStyle(Color("TextMuted"))
                .frame(width: CGFloat(monthInfo.weeks * 12), height: 10)
        }
    }
}
```

**Day Indicators Implementation:**
```swift  
// HabitGridView.swift:51-64
private var dayLabelsView: some View {
    VStack(spacing: 2) {
        Spacer().frame(height: 10) // Month labels space
        Text("S").font(.system(size: 8)).foregroundStyle(Color("TextMuted"))
        Text("M").font(.system(size: 8)).foregroundStyle(Color("TextMuted"))
        Text("T").font(.system(size: 8)).foregroundStyle(Color("TextMuted"))
        // ... W,T,F,S
    }
    .frame(width: 10)
    .padding(.trailing, 2)
}
```

**Integration Points:**
- Month labels positioned above grid cells
- Day labels positioned left of grid
- Grid cells: 12x12px with 2px spacing
- Labels use consistent `Color("TextMuted")` theming
- **No changes required** - existing implementation works with n-level system

### Phase 4: Implementation Steps

#### Step 1: Update Habit Model
**File:** `HabitGridView.swift` (lines 327-333)
- Add `timesPerDay: Int` property to Habit struct
- Update initializer and sample data

#### Step 2: Implement N-Level cellBackgroundColor Function
**File:** `HabitGridView.swift` (lines 113-132)
- Replace hardcoded 5-level switch statement
- Implement dynamic opacity calculation
- Maintain gray color for level 0, green for levels 1-n

#### Step 3: Update Sample Data Generation
**File:** `HabitRowView.swift` (lines 213-257)
- Modify `generateSampleRecords()` to use 0 to habit.timesPerDay range
- Update level assignment logic for multi-check habits

#### Step 4: Update HabitRowView Integration
**File:** `HabitRowView.swift` (lines 204-211)
- Pass `timesPerDay` to Habit model in `habitModelForDashboard`

#### Step 5: Testing & Validation
- Test with various `timesPerDay` values (1, 3, 5, 8, 10)
- Verify month/day labels remain aligned
- Validate opacity progression looks intuitive
- Test with different habit durations and screen sizes

### Phase 5: Technical Specifications

**Color System:**
- Level 0: `Color.gray.opacity(0.15)` (no activity)
- Levels 1-n: `Color.green.opacity(calculatedOpacity)`
- Calculated opacity range: 0.2 to 1.0
- Maintains neumorphic design principles

**Grid Layout:**
- Cell size: 12x12px (unchanged)
- Cell spacing: 2px (unchanged)
- Corner radius: 2px (unchanged)
- Month label height: 10px (unchanged)
- Day label width: 10px (unchanged)

**Performance Considerations:**
- Opacity calculation is O(1) operation
- No impact on scroll performance
- Maintains existing auto-scroll and touch interactions

## Files to Modify

1. **HabitGridView.swift**
   - `Habit` struct (line 327) - add `timesPerDay`
   - `cellBackgroundColor()` function (line 113) - implement n-level system
   
2. **HabitRowView.swift**
   - `habitModelForDashboard` property (line 204) - pass `timesPerDay`
   - `generateSampleRecords()` function (line 213) - update level generation

## Success Criteria

- [x] Dynamic level system based on user's `timesPerDay` setting
- [x] Smooth opacity progression from 0.2 to 1.0
- [x] Month labels and day indicators remain properly aligned
- [x] Backward compatibility with existing single-day habits
- [x] Intuitive visual representation of partial completion
- [x] Maintains neumorphic design language
- [x] Performance equivalent to current implementation

## Testing Plan

1. **Unit Tests:**
   - Opacity calculation algorithm
   - Level-to-opacity mapping accuracy
   - Edge cases (timesPerDay = 1, very large values)

2. **Integration Tests:**
   - Various habit types (1x/day, 3x/day, 8x/day)
   - Month/day label alignment
   - Today/start date indicators
   - Auto-scroll functionality

3. **Visual Tests:**
   - Opacity progression appears smooth
   - Colors remain accessible (contrast ratios)
   - Grid layout maintains consistency
   - Works in light/dark modes

## Timeline Estimate

- **Phase 1-2:** Algorithm & Model Updates - 2 hours
- **Phase 3:** Core Implementation - 3 hours  
- **Phase 4:** Integration & Testing - 2 hours
- **Phase 5:** Polish & Edge Cases - 1 hour

**Total Estimated Time:** 8 hours

---

*Generated: 2025-01-23*
*Project: habitApp iOS*
*Feature: N-Level Habit Grid System*