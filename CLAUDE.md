## Plan & Review

### Before starting work
- Always in plan mode to make a plan
- After get the plan, make sure you Write the plan to .claude/tasks/TASK_NAME.md.
- The plan should be a detailed implementation plan and the reasoning behind them, as well as tasks broken down.
- If the task require external knowledge or certain package, also research to get latest knowledge (Use Task tool for research)
- Don't over plan it, always think MVP.
- Once you write the plan, firstly ask me to review it. Do not continue until I approve the plan.

### While implementing
- You should update the plan as you work.
- After you complete tasks in the plan, you should update and append detailed descriptions of the changes you made, so following tasks can be easily hand over to other engineers.


Use xcodebuildmcp to capture and analyze any compile errors, warnings, or issues. 

### Required Development Environment
- **Xcode 16.3+** with iOS 18.4 SDK
- **iOS Simulator** with iPhone 16 (iOS 18.4)
- **Swift 6** with strict concurrency enabled
- **iOS Deployment Target**: 17.0+

### Mandatory Technologies
- **Swift 6** features only (no Swift 5 syntax)
- **SwiftUI 6** exclusively (no UIKit components)
- **SwiftData** for persistence (no Core Data or Realm)
- **CloudKit** for synchronization
- **UserNotifications** for reminders
- **WidgetKit** for home screen widgets

### CloudKit Compatibility Requirements
All SwiftData models must be CloudKit-compatible:
- No `@Attribute(.unique)` annotations
- All properties must have default values
- Use optional relationships (`var records: [HabitRecord]? = nil`)
- Proper UUID handling for unique identification

### Neumorphic Design Language
The app implements a custom neumorphic design system based on HTML/CSS prototypes in the `prototype/` folder:

- **Shadow System**: Inner/outer shadows for depth, custom blur radius and offsets
- **Color Palette**: Base background (#F0F0F0), subtle variations for highlights/shadows
- **Typography**: SF Pro font system with consistent hierarchy (.rounded for UI, .default for body)
- **Spacing**: 8pt grid system with consistent margins and padding
- **Components**: Cards, buttons, and interactive elements with subtle depth effects 

## Architecture Overview

### Core Architecture Pattern
- **MVVM (Model-View-ViewModel)** with SwiftUI 6 and @Observable protocol
- **SwiftData** for data persistence (iOS 17+ required)
- **CloudKit** integration for cross-device synchronization
- **Repository Pattern** for data access abstraction

### Current Project Structure

```
habitApp/
├── Assets.xcassets/          # App assets and color definitions
│   ├── AccentColor.colorset
│   ├── AppIcon.appiconset
│   ├── BackgroundPrimary.colorset
│   ├── NeumorphicShadowDark.colorset
│   ├── NeumorphicShadowLight.colorset
│   ├── Success.colorset
│   ├── TextPrimary.colorset
│   ├── TextSecondary.colorset
│   └── [other color assets]
├── Components/               # Reusable UI components
│   ├── ArchiveConfirmationDialog.swift
│   ├── CalendarDayCell.swift
│   ├── ColorPickerView.swift
│   ├── CustomCategoryModal.swift
│   ├── HabitRowView.swift
│   ├── IconPickerView.swift
│   ├── NeumorphicButton.swift
│   └── NeumorphicCard.swift
├── Models/                   # SwiftData models
│   ├── CustomCategory.swift
│   ├── Habit.swift
│   ├── IconCategory.swift
│   └── Item.swift
├── Repositories/             # Data access layer
│   └── HabitRepository.swift
├── ViewModels/               # Business logic layer
│   ├── AppearanceManager.swift
│   ├── ArchiveViewModel.swift
│   ├── CalendarRecordsViewModel.swift
│   ├── CreateHabitViewModel.swift
│   ├── DashboardViewModel.swift
│   └── EditHabitViewModel.swift
├── Views/                    # SwiftUI views
│   ├── Components/
│   │   └── TabBarView.swift
│   ├── AppearanceModeModal.swift
│   ├── ArchiveView.swift
│   ├── CalendarRecordsView.swift
│   ├── CircularProgressView.swift
│   ├── ColorExtensions.swift
│   ├── ContentView.swift
│   ├── CreateHabitView.swift
│   ├── DashboardView.swift
│   ├── EditHabitView.swift
│   ├── HabitGridView.swift
│   ├── SettingsView.swift
│   └── StyleGuideView.swift
├── habitApp.entitlements     # CloudKit entitlements
├── habitAppApp.swift         # App entry point
└── Info.plist               # App configuration

prototype/                    # HTML/CSS/JS design references
├── dashboard.html
├── create-habit.html
├── progress.html
├── habit-grid.js
├── appearance-mode.html
├── archive.html
├── edit-habit.html
├── settings.html
└── [other prototypes]
```

### Reference Prototypes
When implementing UI components, copy exact styling from:
- `prototype/dashboard.html` - Main interface design
- `prototype/create-habit.html` - Form layouts and validation
- `prototype/progress.html` - Analytics and charts
- `prototype/habit-grid.js` - GitHub-style progress grid logic

### Development Best Practices
- Make sure the app compile error free after each task
- Use xcodebuildmcp (build_run_sim_id_proj) to open the app in simulator
- Do not try to test the functionality with xcodebuildmcp, manual testing will be performed