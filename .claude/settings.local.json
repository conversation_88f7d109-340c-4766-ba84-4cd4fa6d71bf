{"permissions": {"allow": ["Task", "Bash(git log:*)", "Glob", "Grep", "LS", "Read", "Edit", "MultiEdit", "Write", "WebFetch", "WebSearch", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(find:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "WebFetch(domain:localhost)", "Bash(npm install)", "Bash(node:*)", "Bash(node:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm test:*)", "Bash(xcodebuild:*)", "mcp__graphiti-memory__search_memory_nodes", "mcp__XcodeBuildMCP__discover_projs", "mcp__XcodeBuildMCP__list_schems_proj", "mcp__XcodeBuildMCP__list_sims", "mcp__XcodeBuildMCP__build_sim_id_proj", "mcp__XcodeBuildMCP__get_sim_app_path_id_proj", "mcp__XcodeBuildMCP__get_app_bundle_id", "mcp__XcodeBuildMCP__install_app_sim", "mcp__XcodeBuildMCP__launch_app_sim", "mcp__XcodeBuildMCP__test_sim_id_proj", "mcp__XcodeBuildMCP__screenshot", "mcp__graphiti-memory__search_memory_facts", "mcp__graphiti-memory__add_memory", "mcp__XcodeBuildMCP__show_build_set_proj", "mcp__XcodeBuildMCP__build_sim_name_proj", "<PERSON><PERSON>(true)", "Bash(rm:*)", "mcp__XcodeBuildMCP__build_mac_proj", "mcp__XcodeBuildMCP__build_run_sim_name_proj", "<PERSON><PERSON>(swiftc:*)", "mcp__XcodeBuildMCP__clean_proj", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/safe-build.sh:*)", "<PERSON><PERSON>(swift:*)", "mcp__sequential-thinking__sequentialthinking_tools", "Bash(git rm:*)", "mcp__basic-memory__write_note", "mcp__basic-memory__build_context", "mcp__XcodeBuildMCP__start_sim_log_cap", "mcp__XcodeBuildMCP__stop_sim_log_cap", "mcp__XcodeBuildMCP__build_mac_ws", "mcp__XcodeBuildMCP__get_sim_app_path_name_proj", "mcp__XcodeBuildMCP__boot_sim", "mcp__XcodeBuildMCP__open_sim", "mcp__XcodeBuildMCP__describe_ui", "mcp__XcodeBuildMCP__tap", "mcp__XcodeBuildMCP__swipe", "Bash(git checkout:*)", "Bash(xcrun simctl launch:*)", "mcp__XcodeBuildMCP__swift_package_run", "mcp__XcodeBuildMCP__type_text", "mcp__XcodeBuildMCP__build_run_sim_id_proj", "mcp__sequential-thinking__process_thought", "mcp__perplexity__reason", "Bash(git symbolic-ref:*)", "mcp__apple-doc-mcp__search_symbols", "mcp__apple-doc-mcp__list_technologies", "mcp__apple-doc-mcp__get_documentation", "mcp__perplexity__search", "mcp__perplexity__deep_research"], "deny": []}}