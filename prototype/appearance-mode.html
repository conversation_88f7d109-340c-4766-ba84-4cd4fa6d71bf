<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitApp - Appearance Mode</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        :root.dark-theme {
            --bg-primary: #1a1a1a;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --text-muted: #6c7b7f;
            --success: #30d158;
            --warning: #ff9230;
            --info: #0091ff;
            --wellness: #00dac3;
            --mindfulness: #db34f2;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .neumorphic-card {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .neumorphic-card {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            user-select: none;
        }

        .neumorphic-button:active,
        .neumorphic-button.pressed {
            /* Only apply shadow and scale, do not change background color */
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
            transform: scale(0.98);
            transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }

            :root.system-theme .neumorphic-button:active,
            :root.system-theme .neumorphic-button.pressed {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
                transform: scale(0.98);
                transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }

        :root.dark-theme .neumorphic-button {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        :root.dark-theme .neumorphic-button:active,
        :root.dark-theme .neumorphic-button.pressed {
            /* Only apply shadow and scale, do not change background color */
            transform: scale(0.98);
            box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.9), inset -4px -4px 8px rgba(51, 51, 51, 0.4) !important;
            transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        :root.dark-theme .status-bar {
            background: rgba(26, 26, 26, 0.95);
        }

        .mode-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-radius: 15px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            background: var(--bg-primary);
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            cursor: pointer;
            user-select: none;
        }

        .mode-option:active,
        .mode-option.pressed {
            /* Only apply shadow and scale, do not change background color */
            transform: scale(0.98);
            box-shadow: inset 4px 4px 8px rgba(163, 177, 198, 0.7), inset -4px -4px 8px rgba(255, 255, 255, 0.6) !important;
            transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .mode-option {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }

            :root.system-theme .mode-option:active,
            :root.system-theme .mode-option.pressed {
                /* Only apply shadow and scale, do not change background color */
                transform: scale(0.98);
                box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.9), inset -4px -4px 8px rgba(51, 51, 51, 0.4) !important;
                transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }

        :root.dark-theme .mode-option {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        :root.dark-theme .mode-option:active,
        :root.dark-theme .mode-option.pressed {
            /* Only apply shadow and scale, do not change background color */
            transform: scale(0.98);
            box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.9), inset -4px -4px 8px rgba(51, 51, 51, 0.4) !important;
            transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mode-option.selected {
            background: linear-gradient(135deg, var(--mindfulness), var(--info));
            box-shadow: none;
        }

        .mode-option.selected .mode-name,
        .mode-option.selected .mode-description {
            color: white;
        }

        .mode-check {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mode-option.selected .mode-check {
            border-color: white;
            background-color: white;
        }

        .mode-option.selected .mode-check i {
            color: var(--mindfulness);
        }

        .mode-option:not(.selected) .mode-check i {
            display: none;
        }

        .mode-preview {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .light-preview {
            background-color: #e0e5ec;
            box-shadow: inset 2px 2px 5px rgba(163, 177, 198, 0.6), inset -2px -2px 5px rgba(255, 255, 255, 0.5);
        }

        .dark-preview {
            background-color: #1a1a1a;
            box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.8), inset -2px -2px 5px rgba(51, 51, 51, 0.3);
        }

        .system-preview {
            background: linear-gradient(to right, #e0e5ec 50%, #1a1a1a 50%);
            box-shadow: inset 2px 2px 5px rgba(163, 177, 198, 0.6), inset -2px -2px 5px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>

<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">

            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">

                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center" id="back-button">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">Appearance</h1>
                        <p class="text-sm" style="color: var(--text-secondary);">Choose your theme</p>
                    </div>
                    <div class="w-10 h-10"></div> <!-- Empty div for spacing -->
                </div>

                <!-- Mode Options -->
                <div class="neumorphic-card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Theme Mode</h3>

                    <div class="space-y-4">
                        <!-- Light Mode Option -->
                        <div class="mode-option" data-mode="light">
                            <div class="flex items-center space-x-4">
                                <div class="mode-preview light-preview">
                                    <i class="fas fa-sun text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="mode-name font-medium" style="color: var(--text-primary);">Light</h4>
                                    <p class="mode-description text-sm" style="color: var(--text-secondary);">Bright
                                        interface</p>
                                </div>
                            </div>
                            <div class="mode-check">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>

                        <!-- Dark Mode Option -->
                        <div class="mode-option" data-mode="dark">
                            <div class="flex items-center space-x-4">
                                <div class="mode-preview dark-preview">
                                    <i class="fas fa-moon text-sm" style="color: var(--info);"></i>
                                </div>
                                <div>
                                    <h4 class="mode-name font-medium" style="color: var(--text-primary);">Dark</h4>
                                    <p class="mode-description text-sm" style="color: var(--text-secondary);">Easier on
                                        the eyes</p>
                                </div>
                            </div>
                            <div class="mode-check">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>

                        <!-- System Mode Option -->
                        <div class="mode-option" data-mode="system">
                            <div class="flex items-center space-x-4">
                                <div class="mode-preview system-preview">
                                    <i class="fas fa-adjust text-sm" style="color: var(--mindfulness);"></i>
                                </div>
                                <div>
                                    <h4 class="mode-name font-medium" style="color: var(--text-primary);">System</h4>
                                    <p class="mode-description text-sm" style="color: var(--text-secondary);">Follows
                                        device settings</p>
                                </div>
                            </div>
                            <div class="mode-check">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Card -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="flex items-start space-x-3">
                        <div class="neumorphic-button w-8 h-8 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-info-circle text-sm" style="color: var(--info);"></i>
                        </div>
                        <p class="text-sm" style="color: var(--text-secondary);">
                            System mode automatically switches between light and dark based on your device settings.
                            You can override this by selecting Light or Dark mode.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get the current theme from localStorage or default to light
        const currentTheme = localStorage.getItem('HabitAppTheme') || 'light';

        // Set the initial theme
        document.documentElement.className = currentTheme === 'dark' ? 'dark-theme' :
            currentTheme === 'light' ? 'light-theme' : 'system-theme';

        function updateSelection(selectedMode) {
            document.querySelectorAll('.mode-option').forEach(option => {
                if (option.getAttribute('data-mode') === selectedMode) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        // Select the current theme option on load
        updateSelection(currentTheme);

        // Add click event listeners to mode options
        document.querySelectorAll('.mode-option').forEach(option => {
            // Handle press effect
            option.addEventListener('mousedown', () => {
                option.classList.add('pressed');
            });

            option.addEventListener('mouseup', () => {
                option.classList.remove('pressed');
            });

            option.addEventListener('mouseleave', () => {
                option.classList.remove('pressed');
            });

            // Handle touch events for mobile
            option.addEventListener('touchstart', () => {
                option.classList.add('pressed');
            });

            option.addEventListener('touchend', () => {
                option.classList.remove('pressed');
            });

            option.addEventListener('touchcancel', () => {
                option.classList.remove('pressed');
            });

            option.addEventListener('click', () => {
                const mode = option.getAttribute('data-mode');

                // Update localStorage
                localStorage.setItem('HabitAppTheme', mode);

                // Update the root class
                document.documentElement.className = mode === 'dark' ? 'dark-theme' :
                    mode === 'light' ? 'light-theme' : 'system-theme';

                // Update the selected state
                updateSelection(mode);
            });
        });

        // Back button functionality
        const backButton = document.getElementById('back-button');

        // Handle press effect for back button
        backButton.addEventListener('mousedown', () => {
            backButton.classList.add('pressed');
        });

        backButton.addEventListener('mouseup', () => {
            backButton.classList.remove('pressed');
        });

        backButton.addEventListener('mouseleave', () => {
            backButton.classList.remove('pressed');
        });

        // Handle touch events for mobile
        backButton.addEventListener('touchstart', () => {
            backButton.classList.add('pressed');
        });

        backButton.addEventListener('touchend', () => {
            backButton.classList.remove('pressed');
        });

        backButton.addEventListener('touchcancel', () => {
            backButton.classList.remove('pressed');
        });

        backButton.addEventListener('click', () => {
            window.location.href = 'settings.html';
        });
    </script>
</body>

</html>