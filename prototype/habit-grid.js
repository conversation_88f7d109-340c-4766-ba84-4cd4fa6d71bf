/**
 * Habit Grid Component
 * A reusable habit tracking grid component with GitHub-style visualization
 * 
 * This file contains both CSS styles and JavaScript functionality.
 * Simply include this file and the styles will be automatically injected.
 */

// Auto-inject CSS styles when this script loads
(function () {
    if (typeof document !== 'undefined' && !document.getElementById('habit-grid-styles')) {
        const style = document.createElement('style');
        style.id = 'habit-grid-styles';
        style.textContent = `
/* Habit Grid Component Styles */

/* Main grid container and wrapper styles */
.habit-grid-container {
    width: 100%;
    margin-top: 12px;
    position: relative;
    overflow: hidden;
}

.habit-grid-wrapper {
    display: flex;
    align-items: flex-start;
}

.habit-grid-scrollable {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    flex: 1;
    -webkit-overflow-scrolling: touch;
    max-width: calc(100% - 10px);
}

.habit-grid-scrollable::-webkit-scrollbar {
    display: none;
}

.habit-grid-content {
    display: flex;
    flex-direction: column;
}

.habit-grid-months {
    display: flex;
    gap: 2px;
    margin-bottom: 2px;
}

.habit-grid-month {
    min-width: 12px;
    font-size: 7px;
    color: var(--text-muted);
    text-align: center;
    line-height: 10px;
    height: 10px;
}

.habit-grid {
    display: flex;
    gap: 2px;
    padding: 0;
    transition: transform 0.3s ease;
    touch-action: pan-x;
}

.habit-grid-week {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 12px;
}

.habit-grid-cell {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    transition: all 0.2s ease;
}

/* Grid cell levels - representing different completion states */

/* No activity - neutral background, no shadow */
.habit-grid-cell-0 {
    background: var(--bg-primary);
}

/* Unchecked/incomplete - same as background color with concave styling */
.habit-grid-cell-1 {
    background: var(--bg-primary);
    box-shadow: inset 1px 1px 2px rgba(163, 177, 198, 0.6), inset -1px -1px 2px rgba(255, 255, 255, 0.4);
}

/* Low progress - light green with slight convex styling */
.habit-grid-cell-2 {
    background: #c6e48b;
    box-shadow: 1px 1px 2px rgba(163, 177, 198, 0.4), -1px -1px 2px rgba(255, 255, 255, 0.3);
}

/* Medium progress - medium green with convex styling */
.habit-grid-cell-3 {
    background: #7bc96f;
    box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
}

/* Full completion - dark green with strong convex styling */
.habit-grid-cell-4 {
    background: #2d5a37;
    box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
}

/* Today's cell - red border */
.habit-grid-cell-today {
    border: 1px solid #FF3B30 !important;
    box-sizing: border-box;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
    /* Unchecked/incomplete - concave styling for dark mode */
    .habit-grid-cell-1 {
        box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.7), inset -1px -1px 2px rgba(51, 51, 51, 0.3);
    }

    /* Low progress - light green for dark mode */
    .habit-grid-cell-2 {
        background: #9ccc65;
        box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6), -1px -1px 2px rgba(51, 51, 51, 0.2);
    }

    /* Medium progress - medium green for dark mode */
    .habit-grid-cell-3 {
        background: #66bb6a;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.4);
    }

    /* Full completion - dark green for dark mode */
    .habit-grid-cell-4 {
        background: #2e7d32;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.4);
    }

    /* Today's cell - red border for dark mode */
    .habit-grid-cell-today {
        border: 1px solid #FF453A !important;
    }
}

.habit-grid-labels {
    display: flex;
    flex-direction: column;
    gap: 2px;
    height: 76px;
    width: 10px;
    margin-top: 12px;
    margin-left: 0px;
    flex-shrink: 0;
}

.habit-grid-label {
    font-size: 8px;
    color: var(--text-muted);
    line-height: 10px;
    text-align: center;
    height: 10px;
}

/* Starting date cell with icon */
.habit-grid-cell-start {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.habit-grid-cell-start::before {
    content: "🚀";
    font-size: 6px;
    position: absolute;
    z-index: 2;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.8), 0 0 1px rgba(0, 0, 0, 0.8);
    filter: brightness(1.2);
}

.habit-grid-cell-start.habit-grid-cell-0::before {
    content: "▶️";
    font-size: 5px;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.9), 0 0 1px rgba(0, 0, 0, 0.7);
}

.habit-grid-cell-start.habit-grid-cell-1::before,
.habit-grid-cell-start.habit-grid-cell-2::before {
    content: "🚀";
    font-size: 6px;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.9), 0 0 1px rgba(0, 0, 0, 0.7);
}

.habit-grid-cell-start.habit-grid-cell-3::before,
.habit-grid-cell-start.habit-grid-cell-4::before {
    content: "🚀";
    font-size: 6px;
    text-shadow: 0 0 2px rgba(255, 255, 255, 1), 0 0 1px rgba(0, 0, 0, 0.9);
    filter: brightness(1.3) contrast(1.1);
}

/* Legacy grid styles for backward compatibility */
.grid-cell {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    margin: 0.5px;
}

.grid-cell-0 {
    background: var(--bg-primary);
    box-shadow: inset 1px 1px 2px rgba(163, 177, 198, 0.4), inset -1px -1px 2px rgba(255, 255, 255, 0.3);
}

@media (prefers-color-scheme: dark) {
    .grid-cell-0 {
        box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.6), inset -1px -1px 2px rgba(51, 51, 51, 0.2);
    }
}

.grid-cell-1 {
    background: #c6e48b;
}

.grid-cell-2 {
    background: #7bc96f;
}

.grid-cell-3 {
    background: #4c9f50;
}

.grid-cell-4 {
    background: #2d5a37;
}
        `;
        document.head.appendChild(style);
    }
})();

class HabitGrid {
    constructor() {
        this.dayLabels = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
        this.monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    }

    /**
     * Get today's date (configurable for testing)
     */
    getTodayDate() {
        // Return the actual current date
        return new Date();
    }

    /**
     * Generate habit history data for a given habit
     * @param {number} streakDays - Current streak length
     * @param {number} completionRate - Overall completion rate (0-1)
     * @param {number} startDaysAgo - Days ago when habit started
     * @returns {Array} Array of history objects
     */
    generateHabitHistory(streakDays, completionRate, startDaysAgo = null) {
        const history = [];
        const today = this.getTodayDate();

        // Calculate the start of the grid based on habit start date or default
        const defaultTotalWeeks = 60;
        const todayDayOfWeek = today.getDay(); // Saturday = 6
        const daysFromMonday = todayDayOfWeek === 0 ? 6 : todayDayOfWeek - 1; // Saturday = 5 days from Monday

        // Find the Monday of this week
        const thisWeekMonday = new Date(today);
        thisWeekMonday.setDate(today.getDate() - daysFromMonday);

        // Calculate habit start date
        const habitStartDaysAgo = startDaysAgo !== null ? startDaysAgo : (defaultTotalWeeks * 7);
        const habitStartDate = new Date(today);
        habitStartDate.setDate(today.getDate() - habitStartDaysAgo);

        // Find the Monday of the habit start week
        const habitStartDayOfWeek = habitStartDate.getDay();
        const habitStartDaysFromMonday = habitStartDayOfWeek === 0 ? 6 : habitStartDayOfWeek - 1;
        const habitStartWeekMonday = new Date(habitStartDate);
        habitStartWeekMonday.setDate(habitStartDate.getDate() - habitStartDaysFromMonday);

        // Generate data from habit start week Monday to end of this week (Sunday)
        const endDate = new Date(thisWeekMonday);
        endDate.setDate(thisWeekMonday.getDate() + 6); // Sunday of this week

        for (let date = new Date(habitStartWeekMonday); date <= endDate; date.setDate(date.getDate() + 1)) {
            let level = 0;

            // Check if this date is in the future
            const isFuture = date > today;

            // If this date is in the future OR before the habit start date, show no activity
            if (isFuture || date < habitStartDate) {
                level = 0; // No activity - flat background
            } else {
                // Generate completion level based on whether it was completed or not
                const wasCompleted = Math.random() < completionRate;

                if (wasCompleted) {
                    // Completed - use convex styling (levels 3-4)
                    level = Math.random() < 0.7 ? 3 : 4;
                } else {
                    // Not completed but attempted - use concave styling (levels 1-2)
                    level = Math.random() < 0.6 ? 1 : 2;
                }

                // Adjust for recent streak period
                const daysFromToday = Math.floor((today - date) / (1000 * 60 * 60 * 24));
                if (daysFromToday < streakDays && wasCompleted) {
                    level = Math.random() < 0.8 ? 4 : 3; // Higher levels for recent streak
                }
            }

            history.push({
                date: date.toISOString().split('T')[0],
                level: level,
                completed: level >= 3, // Track completion status
                isFuture: isFuture
            });
        }

        return history;
    }

    /**
     * Generate and render habit grid for a specific habit
     * @param {string} containerId - ID of the container element
     * @param {Object} habit - Habit object with history data
     * @param {Object} options - Optional configuration
     */
    generateHabitGrid(containerId, habit, options = {}) {
        const container = document.getElementById(containerId);
        if (!container || !habit) return;

        const history = habit.history || [];
        const { showLabels = true, autoScroll = true } = options;

        // Group history into weeks (already aligned to Monday-Sunday) and track months
        const weeks = [];
        const monthLabels = [];
        let currentMonth = -1;

        for (let i = 0; i < history.length; i += 7) {
            const week = history.slice(i, i + 7);
            weeks.push(week);

            // Check if this is the first week of a new month
            if (week.length > 0) {
                const firstDayOfWeek = new Date(week[0].date);
                const weekMonth = firstDayOfWeek.getMonth();

                if (weekMonth !== currentMonth) {
                    const yearLabel = weekMonth === 0 ? "'" + firstDayOfWeek.getFullYear().toString().slice(-2) : '';
                    monthLabels.push(this.monthNames[weekMonth] + yearLabel);
                    currentMonth = weekMonth;
                } else {
                    monthLabels.push(''); // Empty label for continuation weeks
                }
            }
        }

        const scrollableId = `${containerId}Scrollable`;
        const innerGridId = `${containerId}Inner`;

        container.innerHTML = `
            <div class="habit-grid-wrapper">
                <div class="habit-grid-scrollable" id="${scrollableId}">
                    <div class="habit-grid-content">
                        <div class="habit-grid-months">
                            ${weeks.map((week, index) => `
                                <div class="habit-grid-month">${monthLabels[index] || ''}</div>
                            `).join('')}
                        </div>
                        <div class="habit-grid" id="${innerGridId}">
                            ${weeks.map(week => `
                                <div class="habit-grid-week">
                                    ${week.map(day => this.renderGridCell(day, habit)).join('')}
                                    ${week.length < 7 ? Array(7 - week.length).fill('<div class="habit-grid-cell habit-grid-cell-0"></div>').join('') : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                ${showLabels ? `
                    <div class="habit-grid-labels">
                        ${this.dayLabels.map(label => `
                            <div class="habit-grid-label">${label}</div>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        // Initialize scroll position and drag functionality
        if (autoScroll) {
            const scrollableElement = document.getElementById(scrollableId);
            if (scrollableElement) {
                setTimeout(() => {
                    // Always scroll to show current week (today's column) at the far right
                    scrollableElement.scrollLeft = scrollableElement.scrollWidth - scrollableElement.clientWidth;
                }, 100);
                this.enableDragScroll(scrollableElement);
            }
        }
    }

    /**
     * Render a single grid cell
     * @param {Object} day - Day data object
     * @param {Object} habit - Habit object
     * @returns {string} HTML string for the cell
     */
    renderGridCell(day, habit) {
        let status = '';
        if (day.isFuture) {
            status = 'Future date';
        } else if (day.level === 0) {
            status = 'No activity';
        } else if (habit.timesPerDay && habit.timesPerDay > 1) {
            // Multi-check habit status
            if (day.level === 2) {
                status = `Low progress (${Math.round(habit.timesPerDay * 0.33)} of ${habit.timesPerDay})`;
            } else if (day.level === 3) {
                status = `Medium progress (${Math.round(habit.timesPerDay * 0.66)} of ${habit.timesPerDay})`;
            } else if (day.level === 4) {
                status = `Completed ✓ (${habit.timesPerDay} of ${habit.timesPerDay})`;
            } else {
                status = 'Started';
            }
        } else {
            // Single-check habit status
            if (day.level >= 3) {
                status = 'Completed ✓';
            } else {
                status = 'Attempted';
            }
        }

        // Check if this is today's date
        const today = this.getTodayDate().toISOString().split('T')[0];
        const isToday = day.date === today;
        const todayClass = isToday ? ' habit-grid-cell-today' : '';

        // Check if this is the habit start date
        const habitStartDate = new Date(this.getTodayDate());
        habitStartDate.setDate(habitStartDate.getDate() - (habit.startDaysAgo || 0));
        const isStartDate = day.date === habitStartDate.toISOString().split('T')[0];
        const startClass = isStartDate ? ' habit-grid-cell-start' : '';

        const startStatus = isStartDate ? ' (Start Date 🚀)' : '';

        return `<div class="habit-grid-cell habit-grid-cell-${day.level}${todayClass}${startClass}" 
                     title="${day.date}: ${status}${isToday ? ' (Today)' : ''}${startStatus}"></div>`;
    }

    /**
     * Enable drag-to-scroll for better swipe experience
     * @param {HTMLElement} element - Element to enable drag scroll on
     */
    enableDragScroll(element) {
        let isDown = false;
        let startX;
        let scrollLeft;

        // Mouse events
        element.addEventListener('mousedown', (e) => {
            isDown = true;
            startX = e.pageX - element.offsetLeft;
            scrollLeft = element.scrollLeft;
        });

        document.addEventListener('mouseup', () => {
            isDown = false;
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - element.offsetLeft;
            const walk = (x - startX) * 1; // scroll-fastness factor
            element.scrollLeft = scrollLeft - walk;
        });

        // Touch events
        element.addEventListener('touchstart', (e) => {
            startX = e.touches[0].pageX;
            scrollLeft = element.scrollLeft;
        }, { passive: true });

        element.addEventListener('touchmove', (e) => {
            const x = e.touches[0].pageX;
            const walk = (x - startX) * 1;
            element.scrollLeft = scrollLeft - walk;
        }, { passive: true });
    }

    /**
     * Update habit history when toggling completion
     * @param {Object} habit - Habit object to update
     * @param {boolean} completed - New completion status
     */
    updateHabitHistory(habit, completed) {
        if (!habit) return;

        const today = this.getTodayDate().toISOString().split('T')[0];
        const todayIndex = habit.history.findIndex(h => h.date === today);

        let newLevel;

        // Handle multi-check habits differently
        if (habit.timesPerDay && habit.timesPerDay > 1) {
            const currentCount = habit.todayCount || 0;
            const maxCount = habit.timesPerDay;

            if (currentCount === 0) {
                newLevel = 0; // No activity yet
            } else if (currentCount === maxCount) {
                newLevel = 4; // Fully completed (max intensity)
            } else {
                // Map partial progress to intermediate levels
                const progressRatio = currentCount / maxCount;
                if (progressRatio <= 0.33) {
                    newLevel = 2; // Low intensity
                } else if (progressRatio <= 0.66) {
                    newLevel = 3; // Medium intensity  
                } else {
                    newLevel = 4; // High intensity (near completion)
                }
            }
        } else {
            // Single-check habit logic
            if (completed) {
                // Completed - use convex styling (levels 3-4)
                newLevel = Math.random() < 0.7 ? 4 : 3;
            } else {
                // Not completed but attempted - use concave styling (levels 1-2)
                newLevel = Math.random() < 0.6 ? 1 : 2;
            }
        }

        if (todayIndex !== -1) {
            // Update today's level and completion status
            habit.history[todayIndex].level = newLevel;
            habit.history[todayIndex].completed = completed;
        } else {
            // Add today's entry
            habit.history.push({
                date: today,
                level: newLevel,
                completed: completed,
                isFuture: false
            });
        }
    }

    /**
     * Create a simple demo grid (useful for onboarding or previews)
     * @param {string} containerId - ID of container element
     * @param {number} weeks - Number of weeks to show
     * @param {number} completionRate - Completion rate (0-1)
     */
    createDemoGrid(containerId, weeks = 4, completionRate = 0.7) {
        const demoHabit = {
            id: 'demo',
            name: 'Demo Habit',
            timesPerDay: 1,
            startDaysAgo: weeks * 7,
            history: this.generateHabitHistory(Math.floor(weeks * 5), completionRate, weeks * 7)
        };

        this.generateHabitGrid(containerId, demoHabit, { showLabels: true, autoScroll: false });
    }
}

// Global instance for easy access
window.HabitGrid = HabitGrid;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HabitGrid;
} 