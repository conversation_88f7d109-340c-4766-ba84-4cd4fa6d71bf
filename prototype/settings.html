<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitApp - Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        :root.dark-theme {
            --bg-primary: #1a1a1a;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --text-muted: #6c7b7f;
            --success: #30d158;
            --warning: #ff9230;
            --info: #0091ff;
            --wellness: #00dac3;
            --mindfulness: #db34f2;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .neumorphic-card {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .neumorphic-card {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .neumorphic-button {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        .toggle-switch {
            width: 48px;
            height: 24px;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: inset 4px 4px 8px rgba(163, 177, 198, 0.6), inset -4px -4px 8px rgba(255, 255, 255, 0.5);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .toggle-switch {
                box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.8), inset -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .toggle-switch {
            box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.8), inset -4px -4px 8px rgba(51, 51, 51, 0.3);
        }

        .toggle-switch.active {
            background: linear-gradient(135deg, var(--success), var(--wellness));
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .toggle-switch.active {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .toggle-switch.active {
            box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
        }

        .toggle-knob {
            width: 20px;
            height: 20px;
            background: var(--bg-primary);
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .toggle-knob {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .toggle-knob {
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.3);
        }

        .toggle-switch.active .toggle-knob {
            left: 26px;
            background: white;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }

        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }

            :root.system-theme .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        :root.dark-theme .status-bar {
            background: rgba(26, 26, 26, 0.95);
        }

        :root.dark-theme .tab-bar {
            background: rgba(26, 26, 26, 0.95);
        }

        .privacy-banner {
            background: linear-gradient(135deg, var(--success), var(--wellness));
            border-radius: 20px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .privacy-banner {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .privacy-banner {
            box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            margin-bottom: 12px;
            border-radius: 15px;
            background: var(--bg-primary);
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .setting-item {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .setting-item {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .setting-item {
                border-color: rgba(255, 255, 255, 0.1);
            }
        }

        :root.dark-theme .setting-item {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        /* Modal and Feedback Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
            transform: scale(0.8);
            transition: all 0.3s ease;
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .modal-content {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .modal-content {
            box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
        }

        .modal-overlay.active .modal-content {
            transform: scale(1);
        }

        .feedback-message {
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-primary);
            border-radius: 15px;
            padding: 16px 20px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            z-index: 1001;
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
            transition: all 0.3s ease;
            max-width: 280px;
            text-align: center;
        }

        @media (prefers-color-scheme: dark) {
            :root.system-theme .feedback-message {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        :root.dark-theme .feedback-message {
            box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
        }

        .feedback-message.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        .feedback-message.success {
            border-left: 4px solid var(--success);
        }

        .feedback-message.error {
            border-left: 4px solid var(--warning);
        }

        .hidden-file-input {
            display: none;
        }
    </style>
</head>

<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">

            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">

                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-arrow-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">Settings</h1>
                        <p class="text-sm" style="color: var(--text-secondary);">Privacy & Preferences</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center">
                        <i class="fas fa-info-circle text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>

                <!-- Privacy First Banner -->
                <div class="privacy-banner p-4 mb-6 text-white">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-sm"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Privacy First</h3>
                            <p class="text-sm text-white text-opacity-90">No accounts, no tracking</p>
                        </div>
                    </div>
                    <p class="text-sm text-white text-opacity-80">
                        Your data stays on your device. Optional iCloud sync for backup only.
                    </p>
                </div>

                <!-- Data & Sync -->
                <div class="neumorphic-button p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Data & Sync</h3>

                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-cloud text-sm" style="color: var(--info);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">iCloud Sync</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Sync habits across devices
                                    </p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item" id="import-data-btn">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-file-import text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Import Data</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Import habits from backup
                                    </p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="setting-item" id="export-data-btn">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-file-export text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Export Data</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Export habits for backup
                                    </p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="setting-item" id="archives-btn">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-archive text-sm" style="color: var(--mindfulness);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Archives</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">View archived habits</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="neumorphic-button p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Notifications</h3>

                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-bell text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Daily Reminders</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Gentle habit reminders</p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-fire text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Streaks</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Celebrate streak milestones
                                    </p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance -->
                <div class="neumorphic-button p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Appearance</h3>

                    <div class="space-y-4">
                        <div class="setting-item" id="mode-setting">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-adjust text-sm" style="color: var(--mindfulness);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Mode</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Light, Dark, or System</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-th text-sm" style="color: var(--wellness);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Compact Grid</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Smaller contribution
                                        squares</p>
                                </div>
                            </div>
                            <div class="toggle-switch">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-palette text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Color Intensity</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Vivid contribution colors
                                    </p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- App Preferences -->
                <div class="neumorphic-button p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">App Preferences</h3>

                    <div class="space-y-4">
                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-lightbulb text-sm" style="color: var(--info);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Smart Tips</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Helpful habit suggestions
                                    </p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-seedling text-sm" style="color: var(--success);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Growth Insights</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Weekly progress analysis
                                    </p>
                                </div>
                            </div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="neumorphic-button w-8 h-8 flex items-center justify-center">
                                    <i class="fas fa-heart text-sm" style="color: var(--warning);"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium" style="color: var(--text-primary);">Motivational Quotes</h4>
                                    <p class="text-sm" style="color: var(--text-secondary);">Daily inspiration</p>
                                </div>
                            </div>
                            <div class="toggle-switch">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support & Info -->
                <div class="neumorphic-button p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Support & Info</h3>

                    <div class="space-y-3">
                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-question-circle text-lg" style="color: var(--info);"></i>
                                <span style="color: var(--text-primary);">Help & FAQ</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-envelope text-lg" style="color: var(--wellness);"></i>
                                <span style="color: var(--text-primary);">Contact Support</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-star text-lg" style="color: var(--warning);"></i>
                                <span style="color: var(--text-primary);">Rate HabitApp</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-share text-lg" style="color: var(--success);"></i>
                                <span style="color: var(--text-primary);">Share with Friends</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>

                        <div class="neumorphic-button p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-shield-alt text-lg" style="color: var(--mindfulness);"></i>
                                <span style="color: var(--text-primary);">Privacy Policy</span>
                            </div>
                            <i class="fas fa-chevron-right text-sm" style="color: var(--text-secondary);"></i>
                        </div>
                    </div>
                </div>

                <!-- App Version -->
                <div class="neumorphic-button p-4 mb-6">
                    <div class="text-center">
                        <div class="neumorphic-button w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                            <i class="fas fa-leaf text-2xl" style="color: var(--wellness);"></i>
                        </div>
                        <h3 class="font-semibold" style="color: var(--text-primary);">HabitApp</h3>
                        <p class="text-sm" style="color: var(--text-secondary);">Version 1.0.0</p>
                        <p class="text-xs mt-2" style="color: var(--text-muted);">Built with ❤️ for sustainable habits
                        </p>
                    </div>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Home</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-line text-lg" style="color: var(--text-muted);"></i>
                        <span class="text-xs mt-1" style="color: var(--text-muted);">Progress</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-cog text-lg" style="color: var(--info);"></i>
                        <span class="text-xs mt-1" style="color: var(--info);">Settings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input for import -->
    <input type="file" id="import-file-input" class="hidden-file-input" accept=".json" />

    <!-- Import/Export Modal -->
    <div id="data-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="text-center">
                <div class="neumorphic-button w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i id="modal-icon" class="fas fa-spinner fa-spin text-2xl" style="color: var(--info);"></i>
                </div>
                <h3 id="modal-title" class="font-semibold mb-2" style="color: var(--text-primary);">Processing...</h3>
                <p id="modal-message" class="text-sm mb-4" style="color: var(--text-secondary);">Please wait while we
                    process your request.</p>
                <div id="modal-buttons" class="flex space-x-3">
                    <button id="modal-cancel" class="neumorphic-button flex-1 py-2 px-4 text-sm font-medium"
                        style="color: var(--text-secondary);">Cancel</button>
                    <button id="modal-confirm" class="neumorphic-button flex-1 py-2 px-4 text-sm font-medium"
                        style="color: var(--info);">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle switch functionality
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });

        // Mode setting navigation
        document.getElementById('mode-setting').addEventListener('click', () => {
            window.location.href = 'appearance-mode.html';
        });

        // Initialize theme based on localStorage
        const currentTheme = localStorage.getItem('HabitAppTheme') || 'system';
        if (currentTheme === 'dark') {
            document.documentElement.classList.add('dark-theme');
        } else if (currentTheme === 'light') {
            document.documentElement.classList.add('light-theme');
        } else {
            document.documentElement.classList.add('system-theme');
        }

        // Data Management System
        class HabitDataManager {
            constructor() {
                this.storageKey = 'HabitAppData';
                this.modal = document.getElementById('data-modal');
                this.modalIcon = document.getElementById('modal-icon');
                this.modalTitle = document.getElementById('modal-title');
                this.modalMessage = document.getElementById('modal-message');
                this.modalButtons = document.getElementById('modal-buttons');
                this.modalCancel = document.getElementById('modal-cancel');
                this.modalConfirm = document.getElementById('modal-confirm');
                this.fileInput = document.getElementById('import-file-input');

                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // Export button
                document.getElementById('export-data-btn').addEventListener('click', () => {
                    this.handleExport();
                });

                // Import button
                document.getElementById('import-data-btn').addEventListener('click', () => {
                    this.handleImport();
                });

                // Archives button
                document.getElementById('archives-btn').addEventListener('click', () => {
                    window.location.href = 'Archive.html';
                });

                // File input change
                this.fileInput.addEventListener('change', (e) => {
                    this.processImportFile(e.target.files[0]);
                });

                // Modal buttons
                this.modalCancel.addEventListener('click', () => {
                    this.hideModal();
                });

                this.modalConfirm.addEventListener('click', () => {
                    this.handleModalConfirm();
                });

                // Close modal on background click
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.hideModal();
                    }
                });
            }

            // Generate sample habit data for export
            generateSampleData() {
                const sampleData = {
                    appVersion: "1.0.0",
                    exportDate: new Date().toISOString(),
                    habits: [
                        {
                            id: 1,
                            name: "Morning Walk",
                            timesPerDay: 1,
                            startDaysAgo: 30,
                            todayCount: 0,
                            history: this.generateSampleHistory(30, 0.8)
                        },
                        {
                            id: 2,
                            name: "Read 20 minutes",
                            timesPerDay: 1,
                            startDaysAgo: 45,
                            todayCount: 1,
                            history: this.generateSampleHistory(45, 0.7)
                        },
                        {
                            id: 3,
                            name: "Drink Water",
                            timesPerDay: 8,
                            startDaysAgo: 60,
                            todayCount: 3,
                            history: this.generateSampleHistory(60, 0.6)
                        }
                    ],
                    settings: {
                        theme: currentTheme,
                        notifications: true,
                        compactGrid: false,
                        colorIntensity: true
                    }
                };

                return sampleData;
            }

            generateSampleHistory(daysAgo, completionRate) {
                const history = [];
                const today = new Date();

                for (let i = daysAgo; i >= 0; i--) {
                    const date = new Date(today);
                    date.setDate(today.getDate() - i);

                    const isFuture = date > today;
                    let level = 0;

                    if (!isFuture) {
                        const wasCompleted = Math.random() < completionRate;
                        if (wasCompleted) {
                            level = Math.random() < 0.7 ? 3 : 4;
                        } else {
                            level = Math.random() < 0.6 ? 1 : 2;
                        }
                    }

                    history.push({
                        date: date.toISOString().split('T')[0],
                        level: level,
                        completed: level >= 3,
                        isFuture: isFuture
                    });
                }

                return history;
            }

            // Load data from localStorage or return sample data
            loadHabitData() {
                const stored = localStorage.getItem(this.storageKey);
                if (stored) {
                    try {
                        return JSON.parse(stored);
                    } catch (e) {
                        console.warn('Invalid stored data, using sample data:', e);
                        return this.generateSampleData();
                    }
                }
                return this.generateSampleData();
            }

            // Save data to localStorage
            saveHabitData(data) {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(data));
                    return true;
                } catch (e) {
                    console.error('Failed to save data:', e);
                    return false;
                }
            }

            // Handle export functionality
            handleExport() {
                this.showModal({
                    icon: 'fa-file-export',
                    iconColor: 'var(--warning)',
                    title: 'Export Habit Data',
                    message: 'Export all your habit data and settings as a JSON backup file.',
                    showButtons: true,
                    confirmText: 'Export',
                    onConfirm: () => this.performExport()
                });
            }

            performExport() {
                try {
                    const data = this.loadHabitData();
                    data.exportDate = new Date().toISOString();

                    const jsonString = JSON.stringify(data, null, 2);
                    const blob = new Blob([jsonString], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const timestamp = new Date().toISOString().split('T')[0];
                    const filename = `habitest-backup-${timestamp}.json`;

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    URL.revokeObjectURL(url);

                    this.hideModal();
                    this.showFeedback('Export successful! Your backup file has been downloaded.', 'success');
                } catch (error) {
                    this.hideModal();
                    this.showFeedback('Export failed. Please try again.', 'error');
                    console.error('Export error:', error);
                }
            }

            // Handle import functionality
            handleImport() {
                this.showModal({
                    icon: 'fa-file-import',
                    iconColor: 'var(--success)',
                    title: 'Import Habit Data',
                    message: 'Import habit data from a backup file. This will replace your current data.',
                    showButtons: true,
                    confirmText: 'Choose File',
                    onConfirm: () => this.fileInput.click()
                });
            }

            processImportFile(file) {
                if (!file) return;

                this.showModal({
                    icon: 'fa-spinner fa-spin',
                    iconColor: 'var(--info)',
                    title: 'Processing Import',
                    message: 'Reading and validating your backup file...',
                    showButtons: false
                });

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        this.validateAndImportData(data);
                    } catch (error) {
                        this.hideModal();
                        this.showFeedback('Invalid file format. Please select a valid HabitApp backup file.', 'error');
                        console.error('Import parsing error:', error);
                    }
                };

                reader.onerror = () => {
                    this.hideModal();
                    this.showFeedback('Failed to read file. Please try again.', 'error');
                };

                reader.readAsText(file);
            }

            validateAndImportData(data) {
                // Basic validation
                if (!data.habits || !Array.isArray(data.habits)) {
                    this.hideModal();
                    this.showFeedback('Invalid backup file: Missing habits data.', 'error');
                    return;
                }

                const validHabits = data.habits.filter(habit => {
                    return habit.id && habit.name && Array.isArray(habit.history);
                });

                if (validHabits.length === 0) {
                    this.hideModal();
                    this.showFeedback('No valid habits found in backup file.', 'error');
                    return;
                }

                const importInfo = {
                    totalHabits: validHabits.length,
                    exportDate: data.exportDate ? new Date(data.exportDate).toLocaleDateString() : 'Unknown'
                };

                this.showModal({
                    icon: 'fa-check-circle',
                    iconColor: 'var(--success)',
                    title: 'Confirm Import',
                    message: `Found ${importInfo.totalHabits} habits from backup created on ${importInfo.exportDate}. Import now?`,
                    showButtons: true,
                    confirmText: 'Import',
                    onConfirm: () => this.performImport(data)
                });
            }

            performImport(data) {
                try {
                    if (this.saveHabitData(data)) {
                        this.hideModal();
                        this.showFeedback('Import successful! Your habit data has been restored.', 'success');

                        // Reset file input
                        this.fileInput.value = '';

                        setTimeout(() => {
                            // Could refresh page or navigate to dashboard
                            // window.location.reload();
                        }, 2000);
                    } else {
                        throw new Error('Storage failed');
                    }
                } catch (error) {
                    this.hideModal();
                    this.showFeedback('Import failed. Please try again.', 'error');
                    console.error('Import error:', error);
                }
            }

            showModal({ icon, iconColor, title, message, showButtons, confirmText = 'Confirm', onConfirm }) {
                this.modalIcon.className = `fas ${icon} text-2xl`;
                this.modalIcon.style.color = iconColor;
                this.modalTitle.textContent = title;
                this.modalMessage.textContent = message;

                if (showButtons) {
                    this.modalButtons.style.display = 'flex';
                    this.modalConfirm.textContent = confirmText;
                    this.currentConfirmAction = onConfirm;
                } else {
                    this.modalButtons.style.display = 'none';
                    this.currentConfirmAction = null;
                }

                this.modal.classList.add('active');
            }

            hideModal() {
                this.modal.classList.remove('active');
                this.currentConfirmAction = null;
            }

            handleModalConfirm() {
                if (this.currentConfirmAction) {
                    this.currentConfirmAction();
                }
            }

            showFeedback(message, type = 'success') {
                // Remove existing feedback
                const existing = document.querySelector('.feedback-message');
                if (existing) {
                    existing.remove();
                }

                const feedback = document.createElement('div');
                feedback.className = `feedback-message ${type}`;
                feedback.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'}" style="color: var(--${type === 'success' ? 'success' : 'warning'});"></i>
                        <span class="text-sm font-medium" style="color: var(--text-primary);">${message}</span>
                    </div>
                `;

                document.body.appendChild(feedback);

                // Show feedback
                setTimeout(() => feedback.classList.add('show'), 100);

                // Hide feedback after 4 seconds
                setTimeout(() => {
                    feedback.classList.remove('show');
                    setTimeout(() => feedback.remove(), 300);
                }, 4000);
            }
        }

        // Initialize data manager when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new HabitDataManager();
        });
    </script>
</body>

</html>
</body>

</html>