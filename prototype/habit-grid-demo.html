<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Habit Grid Component Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Note: CSS is now included automatically in the JS file -->
    
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
            }
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }

        .demo-section {
            margin-bottom: 2rem;
        }

        .code-block {
            background: #f4f4f4;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        @media (prefers-color-scheme: dark) {
            .code-block {
                background: #2d2d2d;
                color: #f8f8f2;
            }
        }
    </style>
</head>

<body class="min-h-screen p-6">
    <div class="max-w-4xl mx-auto">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold mb-2" style="color: var(--text-primary);">
                <i class="fas fa-grid-horizontal mr-2" style="color: var(--success);"></i>
                Habit Grid Component Demo
            </h1>
            <p class="text-lg" style="color: var(--text-secondary);">
                Reusable GitHub-style habit tracking visualization
            </p>
        </header>

        <!-- Basic Grid Demo -->
        <div class="demo-section">
            <div class="neumorphic-card p-6 mb-4">
                <h2 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                    <i class="fas fa-chart-line mr-2" style="color: var(--info);"></i>
                    Basic Habit Grid
                </h2>
                <p class="text-sm mb-4" style="color: var(--text-secondary);">
                    A simple habit with 14 days of history and 80% completion rate
                </p>
                
                <div class="habit-grid-container" id="basicGrid"></div>
                
                <div class="code-block mt-4">
const habitGrid = new HabitGrid();
const habit = {
    id: 1,
    name: "Morning Walk",
    timesPerDay: 1,
    startDaysAgo: 14,
    history: habitGrid.generateHabitHistory(10, 0.8, 14)
};
habitGrid.generateHabitGrid('basicGrid', habit);
                </div>
            </div>
        </div>

        <!-- Multi-Check Habit Demo -->
        <div class="demo-section">
            <div class="neumorphic-card p-6 mb-4">
                <h2 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                    <i class="fas fa-tint mr-2" style="color: var(--info);"></i>
                    Multi-Check Habit Grid
                </h2>
                <p class="text-sm mb-4" style="color: var(--text-secondary);">
                    A habit with multiple times per day (8 glasses of water)
                </p>
                
                <div class="habit-grid-container" id="multiGrid"></div>
                
                <div class="code-block mt-4">
const habit = {
    id: 2,
    name: "Drink Water",
    timesPerDay: 8,
    todayCount: 5,
    startDaysAgo: 21,
    history: habitGrid.generateHabitHistory(15, 0.7, 21)
};
habitGrid.generateHabitGrid('multiGrid', habit);
                </div>
            </div>
        </div>

        <!-- Long History Demo -->
        <div class="demo-section">
            <div class="neumorphic-card p-6 mb-4">
                <h2 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                    <i class="fas fa-history mr-2" style="color: var(--info);"></i>
                    Long History Grid
                </h2>
                <p class="text-sm mb-4" style="color: var(--text-secondary);">
                    A habit with 120 days of history showing scroll functionality
                </p>
                
                <div class="habit-grid-container" id="longGrid"></div>
                
                <div class="code-block mt-4">
const habit = {
    id: 3,
    name: "Daily Reading",
    timesPerDay: 1,
    startDaysAgo: 120,
    history: habitGrid.generateHabitHistory(45, 0.85, 120)
};
habitGrid.generateHabitGrid('longGrid', habit);
                </div>
            </div>
        </div>

        <!-- Demo Grid -->
        <div class="demo-section">
            <div class="neumorphic-card p-6 mb-4">
                <h2 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                    <i class="fas fa-rocket mr-2" style="color: var(--info);"></i>
                    Demo Grid (Onboarding)
                </h2>
                <p class="text-sm mb-4" style="color: var(--text-secondary);">
                    Quick demo grid for onboarding or previews
                </p>
                
                <div class="habit-grid-container" id="demoGrid"></div>
                
                <div class="code-block mt-4">
habitGrid.createDemoGrid('demoGrid', 8, 0.75); // 8 weeks, 75% completion
                </div>
            </div>
        </div>

        <!-- Custom Options Demo -->
        <div class="demo-section">
            <div class="neumorphic-card p-6 mb-4">
                <h2 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                    <i class="fas fa-cog mr-2" style="color: var(--info);"></i>
                    Custom Options
                </h2>
                <p class="text-sm mb-4" style="color: var(--text-secondary);">
                    Grid without day labels and auto-scroll disabled
                </p>
                
                <div class="habit-grid-container" id="customGrid"></div>
                
                <div class="code-block mt-4">
habitGrid.generateHabitGrid('customGrid', habit, {
    showLabels: false,  // Hide day labels
    autoScroll: false   // Don't auto-scroll to today
});
                </div>
            </div>
        </div>

        <!-- Grid Levels Legend -->
        <div class="demo-section">
            <div class="neumorphic-card p-6">
                <h2 class="text-xl font-semibold mb-4" style="color: var(--text-primary);">
                    <i class="fas fa-palette mr-2" style="color: var(--info);"></i>
                    Grid Levels Legend
                </h2>
                
                <div class="flex flex-wrap gap-4">
                    <div class="flex items-center space-x-2">
                        <div class="habit-grid-cell habit-grid-cell-0 w-4 h-4"></div>
                        <span class="text-sm" style="color: var(--text-secondary);">No activity</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="habit-grid-cell habit-grid-cell-1 w-4 h-4"></div>
                        <span class="text-sm" style="color: var(--text-secondary);">Attempted</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="habit-grid-cell habit-grid-cell-2 w-4 h-4"></div>
                        <span class="text-sm" style="color: var(--text-secondary);">Low progress</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="habit-grid-cell habit-grid-cell-3 w-4 h-4"></div>
                        <span class="text-sm" style="color: var(--text-secondary);">Medium progress</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="habit-grid-cell habit-grid-cell-4 w-4 h-4"></div>
                        <span class="text-sm" style="color: var(--text-secondary);">Completed</span>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t" style="border-color: var(--text-muted);">
                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">Special Indicators:</h3>
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center space-x-2">
                            <div class="habit-grid-cell habit-grid-cell-3 habit-grid-cell-today w-4 h-4"></div>
                            <span class="text-sm" style="color: var(--text-secondary);">Today's date</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="habit-grid-cell habit-grid-cell-4 habit-grid-cell-start w-4 h-4"></div>
                            <span class="text-sm" style="color: var(--text-secondary);">Habit start date</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="demo-section">
            <div class="neumorphic-card p-6">
                <h2 class="text-xl font-semibold mb-4" style="color: var(--text-primary);">
                    <i class="fas fa-info-circle mr-2" style="color: var(--info);"></i>
                    How to Use
                </h2>
                
                <div class="space-y-3 text-sm" style="color: var(--text-secondary);">
                    <p><strong>1. Include file:</strong> Add habit-grid.js to your HTML (CSS included automatically)</p>
                    <p><strong>2. Set CSS variables:</strong> Define --bg-primary, --text-primary, --text-secondary, --text-muted</p>
                    <p><strong>3. Create container:</strong> Add a div with class "habit-grid-container" and unique ID</p>
                    <p><strong>4. Initialize grid:</strong> Create HabitGrid instance and call generateHabitGrid()</p>
                    <p><strong>5. Interaction:</strong> Grid supports touch/mouse drag to scroll horizontally</p>
                </div>
                
                <div class="mt-4">
                    <p class="text-sm font-semibold mb-2" style="color: var(--text-primary);">Files to include:</p>
                    <div class="space-y-1 text-sm" style="color: var(--text-secondary);">
                        <p>• habit-grid.js - Component with embedded styles (all-in-one)</p>
                        <p>• HABIT-GRID-README.md - Complete documentation</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the habit grid JavaScript -->
    <script src="habit-grid.js"></script>
    
    <script>
        // Initialize the habit grid component
        const habitGrid = new HabitGrid();

        // Basic grid demo
        const basicHabit = {
            id: 1,
            name: "Morning Walk",
            timesPerDay: 1,
            startDaysAgo: 14,
            history: habitGrid.generateHabitHistory(10, 0.8, 14)
        };
        habitGrid.generateHabitGrid('basicGrid', basicHabit);

        // Multi-check habit demo
        const multiHabit = {
            id: 2,
            name: "Drink Water",
            timesPerDay: 8,
            todayCount: 5,
            startDaysAgo: 21,
            history: habitGrid.generateHabitHistory(15, 0.7, 21)
        };
        habitGrid.generateHabitGrid('multiGrid', multiHabit);

        // Long history demo
        const longHabit = {
            id: 3,
            name: "Daily Reading",
            timesPerDay: 1,
            startDaysAgo: 120,
            history: habitGrid.generateHabitHistory(45, 0.85, 120)
        };
        habitGrid.generateHabitGrid('longGrid', longHabit);

        // Demo grid
        habitGrid.createDemoGrid('demoGrid', 8, 0.75);

        // Custom options demo
        const customHabit = {
            id: 4,
            name: "Meditation",
            timesPerDay: 1,
            startDaysAgo: 30,
            history: habitGrid.generateHabitHistory(20, 0.9, 30)
        };
        habitGrid.generateHabitGrid('customGrid', customHabit, {
            showLabels: false,
            autoScroll: false
        });
    </script>
</body>

</html> 