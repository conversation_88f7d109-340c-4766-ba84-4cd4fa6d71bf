<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HabitApp - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --bg-primary: #e0e5ec;
            --text-primary: #4a5568;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --success: #34c759;
            --warning: #ff8d28;
            --info: #0088ff;
            --wellness: #00c8b3;
            --mindfulness: #cb30e0;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1a1a1a;
                --text-primary: #e2e8f0;
                --text-secondary: #a0aec0;
                --text-muted: #6c7b7f;
                --success: #30d158;
                --warning: #ff9230;
                --info: #0091ff;
                --wellness: #00dac3;
                --mindfulness: #db34f2;
            }
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .neumorphic-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-card {
                box-shadow: 9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }
        }

        .neumorphic-button:active {
            box-shadow: inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-button:active {
                box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .add-button-green {
            background: var(--success);
            box-shadow: 5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .add-button-green:hover {
            transform: translateY(-1px);
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.7), -6px -6px 12px rgba(255, 255, 255, 0.6);
        }

        .add-button-green:active {
            box-shadow: inset 3px 3px 6px rgba(52, 199, 89, 0.3), inset -3px -3px 6px rgba(255, 255, 255, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            .add-button-green {
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3);
            }

            .add-button-green:hover {
                transform: translateY(-1px);
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.9), -6px -6px 12px rgba(51, 51, 51, 0.4);
            }

            .add-button-green:active {
                box-shadow: inset 3px 3px 6px rgba(48, 209, 88, 0.3), inset -3px -3px 6px rgba(51, 51, 51, 0.1);
            }
        }

        .neumorphic-inset {
            box-shadow: inset 6px 6px 10px rgba(163, 177, 198, 0.6), inset -6px -6px 10px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .neumorphic-inset {
                box-shadow: inset 6px 6px 10px rgba(0, 0, 0, 0.8), inset -6px -6px 10px rgba(51, 51, 51, 0.3);
            }
        }

        .habit-completed {
            background: var(--success);
            border-radius: 50%;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .habit-completed {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .habit-incomplete {
            background: var(--bg-primary);
            border: 2px solid var(--text-muted);
            border-radius: 50%;
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.4), inset -2px -2px 4px rgba(255, 255, 255, 0.3);
        }

        @media (prefers-color-scheme: dark) {
            .habit-incomplete {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.6), inset -2px -2px 4px rgba(51, 51, 51, 0.2);
            }
        }

        .habit-item-completed {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .habit-item-completed {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }

        .habit-item-incomplete {
            background: var(--bg-primary);
            border-radius: 15px;
            box-shadow: inset 6px 6px 10px rgba(163, 177, 198, 0.6), inset -6px -6px 10px rgba(255, 255, 255, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (prefers-color-scheme: dark) {
            .habit-item-incomplete {
                box-shadow: inset 6px 6px 10px rgba(0, 0, 0, 0.8), inset -6px -6px 10px rgba(51, 51, 51, 0.3);
            }
        }



        .status-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        .tab-bar {
            background: rgba(224, 229, 236, 0.95);
            backdrop-filter: blur(20px);
        }

        @media (prefers-color-scheme: dark) {
            .status-bar {
                background: rgba(26, 26, 26, 0.95);
            }

            .tab-bar {
                background: rgba(26, 26, 26, 0.95);
            }
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-6px);
            }
        }

        .float-animation {
            animation: float 6s ease-in-out infinite;
        }

        .habit-checkbox {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .habit-checkbox:hover {
            transform: scale(1.1);
        }

        .habit-menu-btn {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .habit-menu-btn:hover {
            transform: translateY(-1px);
        }

        .habit-menu-btn:active {
            transform: translateY(0px);
        }

        .habit-status-container {
            position: relative;
        }

        .habit-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
            min-width: 140px;
            z-index: 1000;
            display: none;
            margin-top: 8px;
        }

        @media (prefers-color-scheme: dark) {
            .habit-dropdown {
                box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.8), -6px -6px 12px rgba(51, 51, 51, 0.3);
            }
        }

        .habit-dropdown.show {
            display: block;
            animation: fadeIn 0.2s ease-out;
        }

        .habit-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .habit-dropdown-item:first-child {
            border-radius: 12px 12px 0 0;
        }

        .habit-dropdown-item:last-child {
            border-radius: 0 0 12px 12px;
        }

        .habit-dropdown-item:only-child {
            border-radius: 12px;
        }

        .habit-dropdown-item:hover {
            background: rgba(163, 177, 198, 0.1);
        }

        .habit-dropdown-item.mark-done {
            color: var(--success);
        }

        .habit-dropdown-item.edit {
            color: var(--info);
        }

        .habit-dropdown-item.archive {
            color: var(--warning);
        }

        .tab-item {
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
        }

        .tab-item:hover {
            background: rgba(163, 177, 198, 0.1);
        }

        .tab-item:active {
            transform: scale(0.95);
        }

        /* Calendar Modal Styles */
        .calendar-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            animation: fadeIn 0.3s ease-out;
        }

        .calendar-modal-overlay.show {
            display: flex;
        }

        .calendar-modal {
            background: var(--bg-primary);
            border-radius: 25px;
            box-shadow: 12px 12px 24px rgba(163, 177, 198, 0.8), -12px -12px 24px rgba(255, 255, 255, 0.6);
            max-width: 350px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            transform: scale(0.8);
            animation: modalSlideIn 0.3s ease-out forwards;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-modal {
                box-shadow: 12px 12px 24px rgba(0, 0, 0, 0.9), -12px -12px 24px rgba(51, 51, 51, 0.4);
            }
        }

        .calendar-header {
            padding: 20px 20px 15px 20px;
            border-bottom: 1px solid rgba(163, 177, 198, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-header {
                border-bottom: 1px solid rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .calendar-close-btn {
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: 4px 4px 8px rgba(163, 177, 198, 0.6), -4px -4px 8px rgba(255, 255, 255, 0.5);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-close-btn {
                box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8), -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-close-btn:active {
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-close-btn:active {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(163, 177, 198, 0.2);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-nav {
                border-bottom: 1px solid rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-nav-btn {
            background: var(--bg-primary);
            border-radius: 10px;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-nav-btn {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-nav-btn:active {
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-nav-btn:active {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-month-year {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .calendar-content {
            padding: 20px;
        }

        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }

        .calendar-weekday {
            text-align: center;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            padding: 8px 4px;
        }

        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-height: 38px;
        }

        .calendar-day-other-month {
            color: var(--text-muted);
            background: var(--bg-primary);
            opacity: 0.3;
            cursor: not-allowed;
        }

        .calendar-day-past {
            background: var(--bg-primary);
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.4), -2px -2px 4px rgba(255, 255, 255, 0.3);
            color: var(--text-primary);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-past {
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6), -2px -2px 4px rgba(51, 51, 51, 0.2);
            }
        }

        .calendar-day-today {
            background: var(--info);
            color: white;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5);
            font-weight: 600;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-today {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-day-future {
            background: var(--bg-primary);
            color: var(--text-muted);
            cursor: not-allowed;
            opacity: 0.5;
            box-shadow: inset 1px 1px 2px rgba(163, 177, 198, 0.4), inset -1px -1px 2px rgba(255, 255, 255, 0.3);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-future {
                box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.6), inset -1px -1px 2px rgba(51, 51, 51, 0.2);
            }
        }

        .calendar-day-past:hover {
            transform: translateY(-1px);
            box-shadow: 4px 4px 8px rgba(163, 177, 198, 0.6), -4px -4px 8px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-past:hover {
                box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8), -4px -4px 8px rgba(51, 51, 51, 0.3);
            }
        }

        .calendar-day-past:active {
            transform: translateY(0);
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-past:active {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3);
            }
        }

        /* Habit completion indicators for calendar - Multi-level progression */
        .calendar-day-completed {
            background: var(--success) !important;
            color: white !important;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-completed {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3) !important;
            }
        }

        /* Multi-level completion states for calendar */
        .calendar-day-level-0 {
            background: var(--bg-primary) !important;
            color: var(--text-primary) !important;
            box-shadow: inset 1px 1px 2px rgba(163, 177, 198, 0.4), inset -1px -1px 2px rgba(255, 255, 255, 0.3) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-level-0 {
                box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.6), inset -1px -1px 2px rgba(51, 51, 51, 0.2) !important;
            }
        }

        .calendar-day-level-1 {
            background: var(--bg-primary) !important;
            color: var(--text-primary) !important;
            box-shadow: inset 2px 2px 4px rgba(163, 177, 198, 0.6), inset -2px -2px 4px rgba(255, 255, 255, 0.5) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-level-1 {
                box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.8), inset -2px -2px 4px rgba(51, 51, 51, 0.3) !important;
            }
        }

        .calendar-day-level-2 {
            background: #c6e48b !important;
            color: white !important;
            box-shadow: 1px 1px 2px rgba(163, 177, 198, 0.4), -1px -1px 2px rgba(255, 255, 255, 0.3) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-level-2 {
                background: #9ccc65 !important;
                box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6), -1px -1px 2px rgba(51, 51, 51, 0.2) !important;
            }
        }

        .calendar-day-level-3 {
            background: #7bc96f !important;
            color: white !important;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-level-3 {
                background: #66bb6a !important;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.4) !important;
            }
        }

        .calendar-day-level-4 {
            background: #2d5a37 !important;
            color: white !important;
            box-shadow: 2px 2px 4px rgba(163, 177, 198, 0.6), -2px -2px 4px rgba(255, 255, 255, 0.5) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-level-4 {
                background: #2e7d32 !important;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), -2px -2px 4px rgba(51, 51, 51, 0.4) !important;
            }
        }

        .calendar-day-partial {
            background: #ff8d28 !important;
            color: white !important;
            box-shadow: 3px 3px 6px rgba(163, 177, 198, 0.6), -3px -3px 6px rgba(255, 255, 255, 0.5) !important;
        }

        @media (prefers-color-scheme: dark) {
            .calendar-day-partial {
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), -3px -3px 6px rgba(51, 51, 51, 0.3) !important;
            }
        }

        .calendar-day-completion-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--success);
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.8) translateY(-20px);
                opacity: 0;
            }

            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        /* Habit grid styles now loaded from habit-grid.js */
    </style>
</head>

<body>
    <!-- iPhone 16 Frame -->
    <div class="max-w-sm mx-auto bg-black rounded-[3rem] p-2 shadow-2xl">
        <div class="rounded-[2.5rem] overflow-hidden" style="background-color: var(--bg-primary);">

            <!-- Status Bar -->
            <div class="status-bar px-6 py-2 flex justify-between items-center text-sm font-medium">
                <span style="color: var(--text-primary);">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-wifi text-xs" style="color: var(--text-primary);"></i>
                    <i class="fas fa-battery-three-quarters text-xs" style="color: var(--text-primary);"></i>
                </div>
            </div>

            <!-- Dynamic Island -->
            <div class="flex justify-center">
                <div class="w-32 h-6 bg-black rounded-full"></div>
            </div>

            <!-- Main Content -->
            <div class="px-4 pt-6 pb-20 min-h-screen">

                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="add-button-green w-10 h-10 flex items-center justify-center rounded-2xl"
                        onclick="window.location.href='create-habit.html'">
                        <i class="fas fa-plus text-sm text-white"></i>
                    </div>
                    <div class="text-center">
                        <h1 class="text-2xl font-bold" style="color: var(--text-primary);">Today</h1>
                        <p class="text-sm" style="color: var(--text-secondary);" id="currentDate">July 4</p>
                    </div>
                    <div class="neumorphic-button w-10 h-10 flex items-center justify-center" onclick="openSettings()">
                        <i class="fas fa-cog text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>

                <!-- Progress Overview Card -->
                <div class="neumorphic-card p-3 mb-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-base font-semibold" style="color: var(--text-primary);">Progress</h3>
                            <p class="text-xs" style="color: var(--text-secondary);"><span id="activeHabits">3</span>
                                active habits</p>
                        </div>
                        <div class="relative w-12 h-12">
                            <svg class="progress-ring w-12 h-12">
                                <circle cx="24" cy="24" r="20" stroke="var(--text-muted)" stroke-width="2.5"
                                    fill="transparent" opacity="0.3" />
                                <circle cx="24" cy="24" r="20" stroke="var(--success)" stroke-width="2.5"
                                    fill="transparent" stroke-dasharray="125.66" stroke-dashoffset="41.47"
                                    class="progress-ring-circle" id="progressCircle" />
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xs font-bold" style="color: var(--text-primary);"><span
                                        id="progressPercent">67</span>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Habits -->
                <div class="neumorphic-card p-4 mb-6">
                    <div class="space-y-3" id="habitsList">
                        <div class="habit-item p-3 habit-item-completed" data-habit-id="1" data-completed="true">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="habit-checkbox habit-completed w-8 h-8 flex items-center justify-center"
                                        onclick="toggleHabit(1)">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium" style="color: var(--text-primary);">Morning Walk</h4>
                                        <p class="habit-subtitle text-xs" style="color: var(--text-secondary);">5
                                            minutes completed</p>
                                    </div>
                                </div>
                                <div class="habit-status-container">
                                    <div class="habit-menu-btn neumorphic-button w-8 h-8 flex items-center justify-center"
                                        onclick="toggleHabitMenu(1)">
                                        <i class="fas fa-ellipsis-vertical text-sm"
                                            style="color: var(--text-primary);"></i>
                                    </div>
                                    <div class="habit-dropdown" id="habitMenu1">
                                        <div class="habit-dropdown-item mark-done"
                                            onclick="toggleHabit(1); hideHabitMenu(1);">
                                            <i class="fas fa-check text-xs"></i>
                                            <span class="text-sm">Mark Done</span>
                                        </div>
                                        <div class="habit-dropdown-item edit" onclick="editHabit(1); hideHabitMenu(1);">
                                            <i class="fas fa-edit text-xs"></i>
                                            <span class="text-sm">Edit Habit</span>
                                        </div>
                                        <div class="habit-dropdown-item edit"
                                            onclick="openCalendarModal(1); hideHabitMenu(1);">
                                            <i class="fas fa-calendar-alt text-xs"></i>
                                            <span class="text-sm">Edit Records</span>
                                        </div>
                                        <div class="habit-dropdown-item archive"
                                            onclick="archiveHabit(1); hideHabitMenu(1);">
                                            <i class="fas fa-archive text-xs"></i>
                                            <span class="text-sm">Archive</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="habit-grid-container" id="habitGrid1">
                                <!-- Grid will be generated by JavaScript -->
                            </div>
                        </div>

                        <div class="habit-item p-3 habit-item-completed" data-habit-id="2" data-completed="true">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="habit-checkbox habit-completed w-8 h-8 flex items-center justify-center"
                                        onclick="toggleHabit(2)">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium" style="color: var(--text-primary);">Read 1 Page</h4>
                                        <p class="habit-subtitle text-xs" style="color: var(--text-secondary);">Daily
                                            learning</p>
                                    </div>
                                </div>
                                <div class="habit-status-container">
                                    <div class="habit-menu-btn neumorphic-button w-8 h-8 flex items-center justify-center"
                                        onclick="toggleHabitMenu(2)">
                                        <i class="fas fa-ellipsis-vertical text-sm"
                                            style="color: var(--text-primary);"></i>
                                    </div>
                                    <div class="habit-dropdown" id="habitMenu2">
                                        <div class="habit-dropdown-item mark-done"
                                            onclick="toggleHabit(2); hideHabitMenu(2);">
                                            <i class="fas fa-check text-xs"></i>
                                            <span class="text-sm">Mark Done</span>
                                        </div>
                                        <div class="habit-dropdown-item edit" onclick="editHabit(2); hideHabitMenu(2);">
                                            <i class="fas fa-edit text-xs"></i>
                                            <span class="text-sm">Edit Habit</span>
                                        </div>
                                        <div class="habit-dropdown-item edit"
                                            onclick="openCalendarModal(2); hideHabitMenu(2);">
                                            <i class="fas fa-calendar-alt text-xs"></i>
                                            <span class="text-sm">Edit Records</span>
                                        </div>
                                        <div class="habit-dropdown-item archive"
                                            onclick="archiveHabit(2); hideHabitMenu(2);">
                                            <i class="fas fa-archive text-xs"></i>
                                            <span class="text-sm">Archive</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="habit-grid-container" id="habitGrid2">
                                <!-- Grid will be generated by JavaScript -->
                            </div>
                        </div>

                        <div class="habit-item p-3 habit-item-incomplete" data-habit-id="3" data-completed="false">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="habit-checkbox habit-incomplete w-8 h-8" onclick="toggleHabit(3)"></div>
                                    <div>
                                        <h4 class="font-medium" style="color: var(--text-primary);">Drink Water</h4>
                                        <p class="habit-subtitle text-xs" style="color: var(--text-secondary);">1 glass
                                            left</p>
                                    </div>
                                </div>
                                <div class="habit-status-container">
                                    <div class="habit-menu-btn neumorphic-button w-8 h-8 flex items-center justify-center"
                                        onclick="toggleHabitMenu(3)">
                                        <i class="fas fa-ellipsis-vertical text-sm"
                                            style="color: var(--text-primary);"></i>
                                    </div>
                                    <div class="habit-dropdown" id="habitMenu3">
                                        <div class="habit-dropdown-item mark-done"
                                            onclick="toggleHabit(3); hideHabitMenu(3);">
                                            <i class="fas fa-check text-xs"></i>
                                            <span class="text-sm">Mark Done</span>
                                        </div>
                                        <div class="habit-dropdown-item edit" onclick="editHabit(3); hideHabitMenu(3);">
                                            <i class="fas fa-edit text-xs"></i>
                                            <span class="text-sm">Edit Habit</span>
                                        </div>
                                        <div class="habit-dropdown-item edit"
                                            onclick="openCalendarModal(3); hideHabitMenu(3);">
                                            <i class="fas fa-calendar-alt text-xs"></i>
                                            <span class="text-sm">Edit Records</span>
                                        </div>
                                        <div class="habit-dropdown-item archive"
                                            onclick="archiveHabit(3); hideHabitMenu(3);">
                                            <i class="fas fa-archive text-xs"></i>
                                            <span class="text-sm">Archive</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="habit-grid-container" id="habitGrid3">
                                <!-- Grid will be generated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Bar -->
                <div class="tab-bar fixed bottom-0 left-0 right-0 px-6 py-2">
                    <div class="flex justify-around items-center">
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('dashboard')">
                            <i class="fas fa-home text-lg" style="color: var(--success);"></i>
                            <span class="text-xs mt-1" style="color: var(--success);">Home</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('progress')">
                            <i class="fas fa-chart-line text-lg" style="color: var(--text-muted);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-muted);">Progress</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('archive')">
                            <i class="fas fa-archive text-lg" style="color: var(--text-muted);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-muted);">Archive</span>
                        </div>
                        <div class="tab-item flex flex-col items-center" onclick="navigateToPage('settings')">
                            <i class="fas fa-cog text-lg" style="color: var(--text-muted);"></i>
                            <span class="text-xs mt-1" style="color: var(--text-muted);">Settings</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Modal -->
        <div class="calendar-modal-overlay" id="calendarModal">
            <div class="calendar-modal">
                <div class="calendar-header">
                    <div class="calendar-title" id="calendarTitle">Edit Habit Records</div>
                    <div class="calendar-close-btn" onclick="closeCalendarModal()">
                        <i class="fas fa-times text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>
                <div class="calendar-nav">
                    <div class="calendar-nav-btn" onclick="previousMonth()">
                        <i class="fas fa-chevron-left text-sm" style="color: var(--text-primary);"></i>
                    </div>
                    <div class="calendar-month-year" id="calendarMonthYear">July 2025</div>
                    <div class="calendar-nav-btn" onclick="nextMonth()">
                        <i class="fas fa-chevron-right text-sm" style="color: var(--text-primary);"></i>
                    </div>
                </div>
                <div class="calendar-content">
                    <div class="calendar-weekdays">
                        <div class="calendar-weekday">Sun</div>
                        <div class="calendar-weekday">Mon</div>
                        <div class="calendar-weekday">Tue</div>
                        <div class="calendar-weekday">Wed</div>
                        <div class="calendar-weekday">Thu</div>
                        <div class="calendar-weekday">Fri</div>
                        <div class="calendar-weekday">Sat</div>
                    </div>
                    <div class="calendar-days" id="calendarDays">
                        <!-- Calendar days will be generated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Include habit grid component -->
        <script src="habit-grid.js"></script>

        <script>
            // Initialize habit grid component
            const habitGrid = new HabitGrid();

            // Habit data structure
            let habits = [
                {
                    id: 1,
                    name: "Morning Walk",
                    subtitle: "5 minutes completed",
                    completed: true,
                    streak: 17,
                    startDaysAgo: 120, // Started 120 days ago from July 4, 2025
                    history: habitGrid.generateHabitHistory(17, 0.85, 120), // 85% completion rate, started 120 days ago
                    timesPerDay: 1, // Default to 1 time per day
                    category: 'fitness',
                    frequency: 'daily',
                    reminderEnabled: false,
                    reminderTime: '8:00 AM'
                },
                {
                    id: 2,
                    name: "Read 1 Page",
                    subtitle: "Daily learning",
                    completed: true,
                    streak: 12,
                    startDaysAgo: 60, // Started 60 days ago from July 4, 2025
                    history: habitGrid.generateHabitHistory(12, 0.75, 60), // 75% completion rate, started 60 days ago
                    timesPerDay: 1, // Default to 1 time per day
                    category: 'learning',
                    frequency: 'daily',
                    reminderEnabled: false,
                    reminderTime: '8:00 AM'
                },
                {
                    id: 3,
                    name: "Drink Water",
                    subtitle: "1 glass left",
                    completed: false,
                    streak: 5,
                    startDaysAgo: 30, // Started 30 days ago from July 4, 2025
                    history: habitGrid.generateHabitHistory(5, 0.65, 30), // 65% completion rate, started 30 days ago
                    timesPerDay: 3, // 3 times per day
                    category: 'health',
                    frequency: 'custom',
                    customDays: [1, 2, 3, 4, 5], // Monday to Friday
                    reminderEnabled: true,
                    reminderTime: '9:00 AM'
                }
            ];

            // Note: generateHabitHistory is now handled by the HabitGrid component

            // Ensure today's entries exist in habit history
            function ensureTodayEntries() {
                const today = getTodayDate().toISOString().split('T')[0];

                habits.forEach(habit => {
                    const todayIndex = habit.history.findIndex(h => h.date === today);

                    if (todayIndex === -1) {
                        // Add today's entry based on current completion status
                        let level;
                        if (habit.completed) {
                            level = Math.random() < 0.7 ? 4 : 3; // Convex styling
                        } else {
                            level = Math.random() < 0.6 ? 1 : 2; // Concave styling
                        }

                        habit.history.push({
                            date: today,
                            level: level,
                            completed: habit.completed,
                            isFuture: false
                        });

                        // Sort history by date
                        habit.history.sort((a, b) => new Date(a.date) - new Date(b.date));
                    }
                });
            }

            // Update current date
            function updateCurrentDate() {
                // Use the actual current date
                const today = new Date();
                const options = { month: 'long', day: 'numeric' };
                const dateString = today.toLocaleDateString('en-US', options);
                document.getElementById('currentDate').textContent = dateString;
            }

            // Get today's date (actual current date)
            function getTodayDate() {
                return new Date();
            }

            // Toggle habit completion
            function toggleHabit(habitId) {
                const habit = habits.find(h => h.id === habitId);
                if (habit) {
                    // Handle multi-check habits differently
                    if (habit.timesPerDay && habit.timesPerDay > 1) {
                        if (habit.completed) {
                            // If already completed, reset to 0
                            habit.todayCount = 0;
                            habit.completed = false;
                        } else {
                            // If not completed, set to max (timesPerDay)
                            habit.todayCount = habit.timesPerDay;
                            habit.completed = true;
                        }
                    } else {
                        // Single-check habit logic (existing behavior)
                        habit.completed = !habit.completed;

                        // Update streak
                        if (habit.completed) {
                            habit.streak++;
                            // Update subtitle for completed habit
                            if (habitId === 1) habit.subtitle = "5 minutes completed";
                            if (habitId === 2) habit.subtitle = "Daily learning";
                            if (habitId === 3) habit.subtitle = "8 glasses completed";
                        } else {
                            // Reset to pending state
                            if (habitId === 1) habit.subtitle = "5 minutes remaining";
                            if (habitId === 2) habit.subtitle = "1 page to read";
                            if (habitId === 3) habit.subtitle = "1 glass left";
                        }
                    }

                    updateHabitDisplay(habitId);
                    updateHabitHistory(habitId, habit.completed);
                    updateProgress();
                    saveHabits();

                    // Show feedback
                    showNotification(habit.completed ?
                        `✅ Great job! ${habit.name} completed!` :
                        `↩️ ${habit.name} marked as incomplete`
                    );
                }
            }

            // Update habit display
            function updateHabitDisplay(habitId) {
                const habit = habits.find(h => h.id === habitId);
                const habitElement = document.querySelector(`[data-habit-id="${habitId}"]`);

                if (habit && habitElement) {
                    const checkbox = habitElement.querySelector('.habit-checkbox');
                    const subtitle = habitElement.querySelector('.habit-subtitle');

                    // Handle multi-check habits differently
                    if (habit.timesPerDay && habit.timesPerDay > 1) {
                        // Update the counter display
                        const counterSpan = checkbox.querySelector('span');
                        if (counterSpan) {
                            counterSpan.textContent = habit.todayCount || 0;
                        }

                        // Update completion status and styling
                        if (habit.completed) {
                            habitElement.className = 'habit-item p-3 habit-item-completed';
                            const innerCircle = checkbox.querySelector('div');
                            if (innerCircle) {
                                innerCircle.className = 'habit-completed';
                                innerCircle.style.background = 'var(--success)';
                                innerCircle.style.border = 'none';
                            }
                            if (counterSpan) {
                                counterSpan.style.color = 'white';
                            }
                        } else {
                            habitElement.className = 'habit-item p-3 habit-item-incomplete';
                            const innerCircle = checkbox.querySelector('div');
                            if (innerCircle) {
                                innerCircle.className = 'habit-incomplete';
                                innerCircle.style.background = 'var(--bg-primary)';
                                innerCircle.style.border = '2px solid var(--text-muted)';
                            }
                            if (counterSpan) {
                                counterSpan.style.color = 'var(--text-primary)';
                            }
                        }
                    } else {
                        // Handle single-check habits (existing logic)
                        if (habit.completed) {
                            habitElement.className = 'habit-item p-3 habit-item-completed';
                            checkbox.className = 'habit-checkbox habit-completed w-8 h-8 flex items-center justify-center';
                            checkbox.innerHTML = '<i class="fas fa-check text-white text-sm"></i>';
                        } else {
                            habitElement.className = 'habit-item p-3 habit-item-incomplete';
                            checkbox.className = 'habit-checkbox habit-incomplete w-8 h-8';
                            checkbox.innerHTML = '';
                        }
                    }

                    // Update subtitle
                    if (subtitle) {
                        subtitle.textContent = habit.subtitle;
                    }

                    // Update data attribute
                    habitElement.setAttribute('data-completed', habit.completed);
                }
            }

            // Update progress statistics
            function updateProgress() {
                const totalHabits = habits.length;
                const completedHabits = habits.filter(h => h.completed).length;
                const successRate = totalHabits > 0 ? Math.round((completedHabits / totalHabits) * 100) : 0;

                // Update progress circle
                const circumference = 175.93; // 2 * PI * 28
                const offset = circumference - (successRate / 100) * circumference;

                document.getElementById('progressCircle').style.strokeDashoffset = offset;
                document.getElementById('progressPercent').textContent = successRate;
                document.getElementById('activeHabits').textContent = totalHabits;
            }

            // Load habits into the UI
            function loadHabits() {
                habits.forEach(habit => {
                    updateHabitDisplay(habit.id);
                });
            }

            // Add new habit
            function addNewHabit() {
                const habitName = prompt('Enter new habit name:');
                if (habitName && habitName.trim()) {
                    const newHabit = {
                        id: Date.now(), // Simple ID generation
                        name: habitName.trim(),
                        subtitle: 'Not started yet',
                        completed: false,
                        streak: 0,
                        startDaysAgo: 0, // Starting today
                        history: generateHabitHistory(0, 0.3, 0), // New habit with low completion rate, starting today
                        timesPerDay: 1 // Default to 1 time per day
                    };

                    habits.push(newHabit);
                    addHabitToDOM(newHabit);
                    updateProgress();
                    generateHabitGrid(newHabit.id); // Generate grid with start icon
                    showNotification(`🎯 New habit "${habitName}" added!`);
                }
            }

            // Add habit to DOM
            function addHabitToDOM(habit) {
                const habitsList = document.getElementById('habitsList');
                const habitElement = document.createElement('div');
                habitElement.className = 'habit-item p-3 habit-item-incomplete';
                habitElement.setAttribute('data-habit-id', habit.id);
                habitElement.setAttribute('data-completed', 'false');

                habitElement.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        ${renderHabitCheckCircle(habit)}
                        <div>
                            <h4 class="font-medium" style="color: var(--text-primary);">${habit.name}</h4>
                            <p class="habit-subtitle text-xs" style="color: var(--text-secondary);">${habit.subtitle}</p>
                        </div>
                    </div>
                    <div class="habit-status-container">
                        <div class="habit-menu-btn neumorphic-button w-8 h-8 flex items-center justify-center" onclick="toggleHabitMenu(${habit.id})">
                            <i class="fas fa-ellipsis-vertical text-sm" style="color: var(--text-primary);"></i>
                        </div>
                        <div class="habit-dropdown" id="habitMenu${habit.id}">
                            <div class="habit-dropdown-item mark-done" onclick="toggleHabit(${habit.id}); hideHabitMenu(${habit.id});">
                                <i class="fas fa-check text-xs"></i>
                                <span class="text-sm">Mark Done</span>
                            </div>
                            <div class="habit-dropdown-item edit" onclick="editHabit(${habit.id}); hideHabitMenu(${habit.id});">
                                <i class="fas fa-edit text-xs"></i>
                                <span class="text-sm">Edit Habit</span>
                            </div>
                            <div class="habit-dropdown-item edit" onclick="openCalendarModal(${habit.id}); hideHabitMenu(${habit.id});">
                                <i class="fas fa-calendar-alt text-xs"></i>
                                <span class="text-sm">Edit Records</span>
                            </div>
                            <div class="habit-dropdown-item archive" onclick="archiveHabit(${habit.id}); hideHabitMenu(${habit.id});">
                                <i class="fas fa-archive text-xs"></i>
                                <span class="text-sm">Archive</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="habit-grid-container" id="habitGrid${habit.id}">
                    <!-- Grid will be generated by JavaScript -->
                </div>
            `;

                habitsList.appendChild(habitElement);
            }

            // --- Multi-check logic for habits with timesPerDay > 1 ---
            function renderHabitCheckCircle(habit) {
                if (habit.timesPerDay && habit.timesPerDay > 1) {
                    return `
            <div class="habit-checkbox" 
                ontouchstart="startLongPress(${habit.id})" 
                ontouchend="endLongPress(${habit.id})"
                onmousedown="startLongPress(${habit.id})"
                onmouseup="endLongPress(${habit.id})"
                onclick="incrementHabitCount(${habit.id})"
                style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;">
                <div class="habit-incomplete" style="width:36px;height:36px;display:flex;align-items:center;justify-content:center;">
                    <span style="font-size:1.2rem;font-weight:bold;color:var(--text-primary);">${habit.todayCount || 0}</span>
                </div>
            </div>
        `;
                } else {
                    // Single check logic
                    return `<div class="habit-checkbox habit-incomplete w-8 h-8" onclick="toggleHabit(${habit.id})"></div>`;
                }
            }

            let longPressTimeout = null;
            // Add suppressTap flag per habit
            const suppressTap = {};
            function startLongPress(habitId) {
                longPressTimeout = setTimeout(() => {
                    resetHabitCount(habitId);
                }, 600);
            }
            function endLongPress(habitId) {
                if (longPressTimeout) {
                    clearTimeout(longPressTimeout);
                    longPressTimeout = null;
                }
            }
            function incrementHabitCount(habitId) {
                // Suppress tap if a long-press just occurred
                if (suppressTap[habitId]) {
                    suppressTap[habitId] = false;
                    return;
                }
                const habit = habits.find(h => h.id === habitId);
                if (!habit) return;
                if (!habit.todayCount) habit.todayCount = 0;
                if (habit.todayCount < habit.timesPerDay) {
                    habit.todayCount += 1;
                    if (habit.todayCount === habit.timesPerDay) {
                        habit.completed = true;
                    }
                }
                updateHabitDisplay(habitId);
                updateHabitHistory(habitId, habit.completed);
                updateProgress();
                saveHabits();
            }
            function resetHabitCount(habitId) {
                const habit = habits.find(h => h.id === habitId);
                if (!habit) return;
                habit.todayCount = 0;
                habit.completed = false;
                suppressTap[habitId] = true; // Set flag to suppress next tap
                updateHabitDisplay(habitId);
                updateHabitHistory(habitId, habit.completed);
                updateProgress();
                saveHabits();
            }

            // Navigation functions
            // Navigation functions
            function navigateToPage(page) {
                switch (page) {
                    case 'dashboard':
                        // Already on dashboard
                        showNotification('📍 You are on the Home page');
                        break;
                    case 'progress':
                        // Check if progress.html exists, if not show notification
                        window.location.href = 'progress.html';
                        break;
                    case 'archive':
                        window.location.href = 'archive.html';
                        break;
                    case 'settings':
                        // Check if settings.html exists, if not show notification
                        window.location.href = 'settings.html';
                        break;
                }
            }

            function openSettings() {
                window.location.href = 'settings.html';
            }

            function openFourWeekPlan() {
                window.location.href = 'four-week-plan.html';
            }

            // Update tab bar styling
            function updateTabBarStyling() {
                // Reset all tabs to inactive state
                const tabs = document.querySelectorAll('.tab-bar .flex.flex-col');
                tabs.forEach(tab => {
                    const icon = tab.querySelector('i');
                    const text = tab.querySelector('span');
                    if (icon && text) {
                        icon.style.color = 'var(--text-muted)';
                        text.style.color = 'var(--text-muted)';
                    }
                });

                // Set current tab (Home) to active state
                const homeTab = tabs[0];
                if (homeTab) {
                    const homeIcon = homeTab.querySelector('i');
                    const homeText = homeTab.querySelector('span');
                    if (homeIcon && homeText) {
                        homeIcon.style.color = 'var(--success)';
                        homeText.style.color = 'var(--success)';
                    }
                }
            }

            // Show notification
            function showNotification(message) {
                // Create notification element
                const notification = document.createElement('div');
                notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--bg-primary);
                color: var(--text-primary);
                padding: 12px 20px;
                border-radius: 15px;
                box-shadow: 6px 6px 12px rgba(163, 177, 198, 0.6), -6px -6px 12px rgba(255, 255, 255, 0.5);
                z-index: 1000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                text-align: center;
                transition: all 0.3s ease;
            `;

                notification.textContent = message;
                document.body.appendChild(notification);

                // Remove notification after 3 seconds
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(-50%) translateY(-20px)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // Local storage functions
            function saveHabits() {
                localStorage.setItem('habitTracker_habits', JSON.stringify(habits));
            }

            function loadHabitsFromStorage() {
                const saved = localStorage.getItem('habitTracker_habits');
                if (saved) {
                    const savedHabits = JSON.parse(saved);
                    // Merge saved habits with existing sample habits
                    savedHabits.forEach(savedHabit => {
                        // Ensure default values for new fields
                        if (!savedHabit.timesPerDay) savedHabit.timesPerDay = 1;
                        if (!savedHabit.todayCount) savedHabit.todayCount = 0;

                        // Regenerate history with current date to ensure proper grid alignment
                        const completionRate = savedHabit.streak > 0 ? 0.8 : 0.6; // Estimate completion rate
                        savedHabit.history = habitGrid.generateHabitHistory(savedHabit.streak || 0, completionRate, savedHabit.startDaysAgo || 0);

                        // Check if habit already exists
                        const existingIndex = habits.findIndex(h => h.id === savedHabit.id);
                        if (existingIndex >= 0) {
                            // Update existing habit
                            habits[existingIndex] = { ...habits[existingIndex], ...savedHabit };
                        } else {
                            // Add new habit
                            habits.push(savedHabit);
                        }
                    });

                    // Refresh the UI to show all habits including new ones
                    refreshHabitsUI();
                }
            }

            function refreshHabitsUI() {
                // Clear existing habit elements
                const habitsList = document.getElementById('habitsList');
                if (habitsList) {
                    habitsList.innerHTML = '';

                    // Add all habits to DOM
                    habits.forEach(habit => {
                        addHabitToDOM(habit);
                    });

                    // Update progress and generate grids
                    updateProgress();
                    generateAllHabitGrids();
                }
            }

            // Generate habit grid for all habits
            function generateAllHabitGrids() {
                habits.forEach(habit => {
                    generateHabitGrid(habit.id);
                });
            }

            // Generate habit grid for a specific habit using HabitGrid component
            function generateHabitGrid(habitId) {
                const habit = habits.find(h => h.id === habitId);
                const container = document.getElementById(`habitGrid${habitId}`);

                if (!habit || !container) return;

                // Use the HabitGrid component to generate the grid
                // Note: HabitGrid.generateHabitGrid expects (containerId, habit, options)
                habitGrid.generateHabitGrid(`habitGrid${habitId}`, habit, {
                    showLabels: true,
                    autoScroll: true
                });
            }

            // Note: enableDragScroll is now handled by the HabitGrid component

            // Update habit history when toggling - now using HabitGrid component
            function updateHabitHistory(habitId, completed) {
                const habit = habits.find(h => h.id === habitId);
                if (!habit) return;

                // Use the HabitGrid component's updateHabitHistory method
                habitGrid.updateHabitHistory(habit, completed);

                // Regenerate grid with updated data
                generateHabitGrid(habitId);
            }

            // Load from storage on page load
            document.addEventListener('DOMContentLoaded', function () {
                loadHabitsFromStorage();
                updateCurrentDate();
                ensureTodayEntries(); // Make sure today's entries exist
                updateProgress();
                loadHabits();
                generateAllHabitGrids();
                updateTabBarStyling();

                // Save habits to localStorage if they don't exist (for demo purposes)
                const savedHabits = localStorage.getItem('habitTracker_habits');
                if (!savedHabits) {
                    console.log('Saving default habits to localStorage');
                    saveHabits();
                } else {
                    // Update existing habits with any missing fields
                    console.log('Updating existing habits with new fields');
                    const existingHabits = JSON.parse(savedHabits);
                    let needsUpdate = false;

                    // Check if habit 3 needs custom frequency update
                    const habit3 = existingHabits.find(h => h.id === 3);
                    if (habit3 && habit3.frequency !== 'custom') {
                        habit3.frequency = 'custom';
                        habit3.customDays = [1, 2, 3, 4, 5];
                        habit3.timesPerDay = 3;
                        habit3.reminderEnabled = true;
                        habit3.reminderTime = '9:00 AM';
                        needsUpdate = true;
                    }

                    if (needsUpdate) {
                        localStorage.setItem('habitTracker_habits', JSON.stringify(existingHabits));
                        console.log('Updated habit 3 with custom frequency');
                    }
                }
            });

            // Handle habit menu dropdown
            function toggleHabitMenu(habitId) {
                const menu = document.getElementById(`habitMenu${habitId}`);
                const isCurrentlyOpen = menu.classList.contains('show');

                // Close all other open menus first
                document.querySelectorAll('.habit-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });

                // Toggle current menu
                if (!isCurrentlyOpen) {
                    menu.classList.add('show');

                    // Update mark done text based on completion status
                    const habit = habits.find(h => h.id === habitId);
                    const markDoneItem = menu.querySelector('.mark-done');
                    if (habit && markDoneItem) {
                        if (habit.completed) {
                            markDoneItem.innerHTML = '<i class="fas fa-undo text-xs"></i><span class="text-sm">Undone</span>';
                        } else {
                            markDoneItem.innerHTML = '<i class="fas fa-check text-xs"></i><span class="text-sm">Mark Done</span>';
                        }
                    }
                }
            }

            function hideHabitMenu(habitId) {
                const menu = document.getElementById(`habitMenu${habitId}`);
                menu.classList.remove('show');
            }

            // Close menus when clicking elsewhere
            document.addEventListener('click', function (event) {
                if (!event.target.closest('.habit-menu-btn') && !event.target.closest('.habit-dropdown')) {
                    document.querySelectorAll('.habit-dropdown.show').forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });

            // Edit specific habit
            function editHabit(habitId) {
                if (habitId) {
                    // Navigate to dedicated edit page with specific habit ID
                    window.location.href = `edit-habit.html?id=${habitId}`;
                } else {
                    // Fallback for general edit (shouldn't be called anymore)
                    alert('No habit selected for editing');
                }
            }

            // Archive habit function
            function archiveHabit(habitId) {
                const habit = habits.find(h => h.id === habitId);
                if (!habit) return;

                if (confirm(`Are you sure you want to archive "${habit.name}"? This will remove it from your dashboard.`)) {
                    // Get archived habits from localStorage
                    let archivedHabits = JSON.parse(localStorage.getItem('habitTracker_archivedHabits') || '[]');

                    // Add current habit to archived habits with archive date
                    const archivedHabit = {
                        ...habit,
                        archivedAt: new Date().toISOString()
                    };
                    archivedHabits.push(archivedHabit);

                    // Remove from active habits
                    habits = habits.filter(h => h.id !== habitId);

                    // Update localStorage
                    localStorage.setItem('habitTracker_habits', JSON.stringify(habits));
                    localStorage.setItem('habitTracker_archivedHabits', JSON.stringify(archivedHabits));

                    // Remove from DOM
                    const habitElement = document.querySelector(`[data-habit-id="${habitId}"]`);
                    if (habitElement) {
                        habitElement.remove();
                    }

                    // Update progress display
                    updateProgress();

                    // Show success message
                    showNotification(`📦 "${habit.name}" has been archived`);
                }
            }

            // Close dropdown when clicking outside
            document.addEventListener('click', function (event) {
                const openDropdowns = document.querySelectorAll('.habit-dropdown.show');
                const clickedInsideMenu = event.target.closest('.habit-status-container');

                if (!clickedInsideMenu && openDropdowns.length > 0) {
                    openDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });

            // Calendar Modal Functionality
            let currentCalendarHabit = null;
            let currentCalendarDate = new Date();
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'];

            // Open calendar modal for a specific habit
            function openCalendarModal(habitId) {
                currentCalendarHabit = habits.find(h => h.id === habitId);
                if (!currentCalendarHabit) return;

                currentCalendarDate = getTodayDate();

                // Update modal title
                document.getElementById('calendarTitle').textContent = `Edit "${currentCalendarHabit.name}" Records`;

                // Generate calendar
                generateCalendar();

                // Show modal
                const modal = document.getElementById('calendarModal');
                modal.classList.add('show');

                // Prevent body scroll
                document.body.style.overflow = 'hidden';
            }

            // Close calendar modal
            function closeCalendarModal() {
                const modal = document.getElementById('calendarModal');
                modal.classList.remove('show');

                // Restore body scroll
                document.body.style.overflow = '';

                currentCalendarHabit = null;
            }

            // Navigate to previous month
            function previousMonth() {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
                generateCalendar();
            }

            // Navigate to next month
            function nextMonth() {
                const today = getTodayDate();
                const nextMonth = new Date(currentCalendarDate);
                nextMonth.setMonth(nextMonth.getMonth() + 1);

                // Allow navigation to any month, but dates in future months will be non-clickable
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
                generateCalendar();
            }

            // Generate calendar for current month
            function generateCalendar() {
                if (!currentCalendarHabit) return;

                const year = currentCalendarDate.getFullYear();
                const month = currentCalendarDate.getMonth();
                const today = getTodayDate();

                // Update month/year display
                document.getElementById('calendarMonthYear').textContent =
                    `${monthNames[month]} ${year}`;

                // Get first day of month and number of days
                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                const daysInMonth = lastDay.getDate();
                const startDayOfWeek = firstDay.getDay(); // 0 = Sunday

                // Get previous month info for filling empty cells
                const prevMonth = new Date(year, month - 1, 0);
                const daysInPrevMonth = prevMonth.getDate();

                // Get next month info
                const nextMonthStart = new Date(year, month + 1, 1);

                let calendarHTML = '';
                let dayCount = 1;
                let nextMonthCount = 1;

                // Generate 6 weeks (42 days) to maintain consistent height
                for (let week = 0; week < 6; week++) {
                    for (let day = 0; day < 7; day++) {
                        const cellIndex = week * 7 + day;
                        let dayNumber, cellDate, isCurrentMonth = true, isClickable = false;
                        let cellClass = 'calendar-day ';

                        if (cellIndex < startDayOfWeek) {
                            // Previous month days
                            dayNumber = daysInPrevMonth - startDayOfWeek + cellIndex + 1;
                            cellDate = new Date(year, month - 1, dayNumber);
                            cellClass += 'calendar-day-other-month';
                            isCurrentMonth = false;
                        } else if (dayCount <= daysInMonth) {
                            // Current month days
                            dayNumber = dayCount;
                            cellDate = new Date(year, month, dayNumber);

                            // Determine cell type
                            if (cellDate.toDateString() === today.toDateString()) {
                                cellClass += 'calendar-day-today';
                                isClickable = true;
                            } else if (cellDate < today) {
                                cellClass += 'calendar-day-past';
                                isClickable = true;
                            } else {
                                cellClass += 'calendar-day-future';
                            }

                            dayCount++;
                        } else {
                            // Next month days
                            dayNumber = nextMonthCount;
                            cellDate = new Date(year, month + 1, nextMonthCount);
                            cellClass += 'calendar-day-other-month';
                            isCurrentMonth = false;
                            nextMonthCount++;
                        }

                        // Check if this date has habit completion
                        let habitStatus = null;
                        if (isCurrentMonth && currentCalendarHabit.history) {
                            const dateStr = cellDate.toISOString().split('T')[0];
                            habitStatus = currentCalendarHabit.history.find(h => h.date === dateStr);
                        }

                        // Add completion status classes
                        if (habitStatus && isCurrentMonth) {
                            // Apply level-based styling that matches the habit grid
                            cellClass += ` calendar-day-level-${habitStatus.level}`;
                        } else if (isCurrentMonth) {
                            // No habit data - use level 0 (no activity)
                            cellClass += ' calendar-day-level-0';
                        }

                        const onclick = isClickable ? `onclick="toggleCalendarDate('${cellDate.toISOString().split('T')[0]}')"` : '';

                        calendarHTML += `
                            <div class="${cellClass}" ${onclick}>
                                ${dayNumber}
                            </div>
                        `;
                    }
                }

                document.getElementById('calendarDays').innerHTML = calendarHTML;
            }

            // Toggle habit completion for a specific date
            function toggleCalendarDate(dateStr) {
                if (!currentCalendarHabit) return;

                // Find existing history entry
                let historyEntry = currentCalendarHabit.history.find(h => h.date === dateStr);

                if (!historyEntry) {
                    // Create new entry
                    historyEntry = {
                        date: dateStr,
                        level: 0,
                        completed: false,
                        isFuture: false
                    };
                    currentCalendarHabit.history.push(historyEntry);
                }

                // Handle progression based on habit type
                if (currentCalendarHabit.timesPerDay && currentCalendarHabit.timesPerDay > 1) {
                    // Multi-check habit: simplified progression
                    // Level progression: 0 -> 2 -> 3 -> 4 -> 0 (cycle)

                    if (historyEntry.level === 0) {
                        historyEntry.level = 2; // Light green (1/3 progress)
                        historyEntry.completed = false;
                    } else if (historyEntry.level === 2) {
                        historyEntry.level = 3; // Medium green (2/3 progress)
                        historyEntry.completed = false;
                    } else if (historyEntry.level === 3) {
                        historyEntry.level = 4; // Dark green (3/3 completed)
                        historyEntry.completed = true;
                    } else {
                        historyEntry.level = 0; // Reset to no activity
                        historyEntry.completed = false;
                    }
                } else {
                    // Single-check habit: toggle between two states
                    if (historyEntry.level === 0 || historyEntry.level === 1) {
                        // Not completed -> completed
                        historyEntry.level = 4; // Full completion
                        historyEntry.completed = true;
                    } else {
                        // Completed -> attempted but not completed
                        historyEntry.level = 1; // Attempted (concave)
                        historyEntry.completed = false;
                    }
                }

                // Update calendar display
                generateCalendar();

                // Update main grid if it's visible
                generateHabitGrid(currentCalendarHabit.id);

                // Update progress if it's today's date
                const today = getTodayDate().toISOString().split('T')[0];
                if (dateStr === today) {
                    currentCalendarHabit.completed = historyEntry.completed;
                    if (currentCalendarHabit.timesPerDay && currentCalendarHabit.timesPerDay > 1) {
                        // Update today count based on level
                        const maxCount = currentCalendarHabit.timesPerDay;
                        if (historyEntry.level === 2) {
                            currentCalendarHabit.todayCount = Math.round(maxCount * 0.33); // 1/3 progress
                        } else if (historyEntry.level === 3) {
                            currentCalendarHabit.todayCount = Math.round(maxCount * 0.66); // 2/3 progress
                        } else if (historyEntry.level === 4) {
                            currentCalendarHabit.todayCount = maxCount; // Full completion
                        } else {
                            currentCalendarHabit.todayCount = 0;
                        }
                    }
                    updateHabitDisplay(currentCalendarHabit.id);
                    updateProgress();
                }

                // Save changes
                saveHabits();

                // Show feedback with appropriate message
                let statusMessage = '';
                if (currentCalendarHabit.timesPerDay && currentCalendarHabit.timesPerDay > 1) {
                    const maxCount = currentCalendarHabit.timesPerDay;
                    let currentCount = 0;

                    if (historyEntry.level === 2) {
                        currentCount = Math.round(maxCount * 0.33);
                        statusMessage = `Light green (${currentCount}/${maxCount})`;
                    } else if (historyEntry.level === 3) {
                        currentCount = Math.round(maxCount * 0.66);
                        statusMessage = `Medium green (${currentCount}/${maxCount})`;
                    } else if (historyEntry.level === 4) {
                        currentCount = maxCount;
                        statusMessage = `Completed ✓ (${currentCount}/${maxCount})`;
                    } else {
                        statusMessage = 'Reset to no activity';
                    }
                } else {
                    if (historyEntry.completed) {
                        statusMessage = 'Completed ✓';
                    } else if (historyEntry.level === 1) {
                        statusMessage = 'Attempted';
                    } else {
                        statusMessage = 'Reset to no activity';
                    }
                }

                showNotification(`📅 ${dateStr}: ${statusMessage}`);
            }

            // Close modal when clicking outside or pressing escape
            document.addEventListener('click', function (event) {
                const modal = document.getElementById('calendarModal');
                if (event.target === modal) {
                    closeCalendarModal();
                }
            });

            document.addEventListener('keydown', function (event) {
                if (event.key === 'Escape') {
                    closeCalendarModal();
                }
            });
        </script>
</body>

</html>