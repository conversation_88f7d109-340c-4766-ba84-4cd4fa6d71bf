{"designSystem": {"name": "Neumorphic Bento Zen - Habit Tracker", "category": "Health & Fitness Habit Tracking", "description": "Neumorphic design system combining Apple's design principles with zen minimalist single-column layout for habit tracking interfaces, supporting both light and dark modes", "basedOn": "Apple Human Interface Guidelines + Neumorphic Design Principles", "philosophy": "Tiny habits, minimal interface, maximum impact", "colorPalette": {"light": {"background": {"primary": "#e0e5ec", "surface": "#e0e5ec", "cardBackground": "#e0e5ec"}, "text": {"primary": "#4a5568", "secondary": "#718096", "muted": "#a0aec0"}, "statusBar": "rgba(224, 229, 236, 0.95)", "tabBar": "rgba(224, 229, 236, 0.95)"}, "dark": {"background": {"primary": "#1a1a1a", "surface": "#1a1a1a", "cardBackground": "#1a1a1a"}, "text": {"primary": "#e2e8f0", "secondary": "#a0aec0", "muted": "#6c7b7f"}, "statusBar": "rgba(26, 26, 26, 0.95)", "tabBar": "rgba(26, 26, 26, 0.95)"}, "semantic": {"light": {"success": "#34c759", "warning": "#ff8d28", "error": "#ff383c", "info": "#0088ff", "wellness": "#00c8b3", "mindfulness": "#cb30e0"}, "dark": {"success": "#30d158", "warning": "#ff9230", "error": "#ff4245", "info": "#0091ff", "wellness": "#00dac3", "mindfulness": "#db34f2"}}, "habitCategories": {"health": ["#ff383c", "#ff2d55", "#34c759"], "fitness": ["#ff8d28", "#ff383c", "#ff2d55"], "mindfulness": ["#cb30e0", "#6155f5", "#00c3d0"], "productivity": ["#0088ff", "#00c0e8", "#6155f5"], "wellness": ["#00c8b3", "#00c3d0", "#34c759"]}}, "neumorphicEffects": {"light": {"baseCard": {"background": "linear-gradient(145deg, #e0e5ec, #e0e5ec)", "borderRadius": "20px", "boxShadow": "9px 9px 16px rgba(163, 177, 198, 0.6), -9px -9px 16px rgba(255, 255, 255, 0.5)", "transition": "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"}, "button": {"background": "linear-gradient(145deg, #e0e5ec, #e0e5ec)", "borderRadius": "15px", "boxShadow": "5px 5px 10px rgba(163, 177, 198, 0.6), -5px -5px 10px rgba(255, 255, 255, 0.5)", "activeState": "inset 3px 3px 6px rgba(163, 177, 198, 0.6), inset -3px -3px 6px rgba(255, 255, 255, 0.5)", "pressedState": {"background": "Always uses theme background color (var(--bg-primary))", "transform": "scale(0.98)", "boxShadow": "inset 4px 4px 8px rgba(163, 177, 198, 0.7), inset -4px -4px 8px rgba(255, 255, 255, 0.6)", "behavior": "Overrides any current background (including gradients) to show consistent pressed state", "transition": "all 0.1s cubic-bezier(0.4, 0, 0.2, 1)"}}, "insetSurface": {"boxShadow": "inset 6px 6px 10px rgba(163, 177, 198, 0.6), inset -6px -6px 10px rgba(255, 255, 255, 0.5)"}, "hover": {"transform": "translateY(-2px)", "boxShadow": "12px 12px 20px rgba(163, 177, 198, 0.7), -12px -12px 20px rgba(255, 255, 255, 0.6)"}}, "dark": {"baseCard": {"background": "linear-gradient(145deg, #1a1a1a, #1a1a1a)", "borderRadius": "20px", "boxShadow": "9px 9px 16px rgba(0, 0, 0, 0.8), -9px -9px 16px rgba(51, 51, 51, 0.3)", "transition": "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"}, "button": {"background": "linear-gradient(145deg, #1a1a1a, #1a1a1a)", "borderRadius": "15px", "boxShadow": "5px 5px 10px rgba(0, 0, 0, 0.8), -5px -5px 10px rgba(51, 51, 51, 0.3)", "activeState": "inset 3px 3px 6px rgba(0, 0, 0, 0.8), inset -3px -3px 6px rgba(51, 51, 51, 0.3)", "pressedState": {"background": "Always uses theme background color (var(--bg-primary))", "transform": "scale(0.98)", "boxShadow": "inset 4px 4px 8px rgba(0, 0, 0, 0.9), inset -4px -4px 8px rgba(51, 51, 51, 0.4)", "behavior": "Overrides any current background (including gradients) to show consistent pressed state", "transition": "all 0.1s cubic-bezier(0.4, 0, 0.2, 1)"}}, "insetSurface": {"boxShadow": "inset 6px 6px 10px rgba(0, 0, 0, 0.8), inset -6px -6px 10px rgba(51, 51, 51, 0.3)"}, "hover": {"transform": "translateY(-3px) scale(1.02)", "boxShadow": "12px 12px 20px rgba(0, 0, 0, 0.9), -12px -12px 20px rgba(51, 51, 51, 0.4)"}}}, "typography": {"fontFamily": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "weights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "hierarchy": {"h1": {"fontSize": "32px", "fontWeight": "700", "lineHeight": "1.2", "usage": "Page titles"}, "h2": {"fontSize": "24px", "fontWeight": "600", "lineHeight": "1.3", "usage": "Section headers"}, "h3": {"fontSize": "18px", "fontWeight": "600", "lineHeight": "1.4", "usage": "Card titles"}, "body": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.5", "usage": "General content"}, "caption": {"fontSize": "12px", "fontWeight": "400", "lineHeight": "1.4", "usage": "Subtitles, metadata"}}}, "spacing": {"unit": 4, "scale": [4, 8, 12, 16, 20, 24, 32, 40, 48, 64], "cardPadding": 16, "sectionGap": 24, "componentGap": 16, "containerPadding": 16}, "borderRadius": {"small": 8, "medium": 12, "large": 16, "xlarge": 20, "button": 15, "card": 20, "pill": 999}, "animations": {"light": {"pulseEffect": {"name": "pulse-soft", "duration": "3s", "timing": "ease-in-out", "iteration": "infinite", "usage": "Highlight important elements"}}, "dark": {"glowPulse": {"name": "glow-pulse", "duration": "3s", "timing": "ease-in-out", "iteration": "infinite", "usage": "Purple glow highlight effect"}, "floatAnimation": {"name": "float", "duration": "6s", "timing": "ease-in-out", "iteration": "infinite", "usage": "Subtle floating motion"}}}, "components": {"habitCards": {"structure": "Single-column vertical layout", "characteristics": ["Neumorphic elevation with soft shadows", "Rounded corners (20px radius)", "Color-coded completion indicators", "Progress visualization with dots/rings", "Consistent internal padding (16px)"], "states": {"completed": "Colored neumorphic button with checkmark", "incomplete": "Border-only circle indicator", "chain": "Dot progress indicators"}}, "progressVisualization": {"circularProgress": {"style": "SVG ring with gradient stroke", "rotation": "-90deg (starts at top)", "strokeWidth": "3px", "colors": "Green for progress, gray for background"}, "chainIndicators": {"style": "Small circular dots", "size": "2px x 2px", "spacing": "4px gaps", "colors": "Category-specific colors"}, "weeklyCalendar": {"layout": "7-column grid", "cellSize": "32px x 32px", "states": "Neumorphic completed vs inset incomplete", "borderRadius": "8px"}}, "navigation": {"tabBar": {"position": "Bottom fixed", "background": "Translucent with backdrop blur", "items": 3, "iconSize": "18px", "labelSize": "10px"}, "statusBar": {"height": "54px", "background": "Translucent with backdrop blur", "elements": ["time", "signal", "wifi", "battery"]}}, "headers": {"structure": "Add button + title + settings button", "layout": "Flex row with space-between", "buttons": "10px x 10px neumorphic buttons", "typography": "24px bold for title, 14px for subtitle"}}, "layoutPatterns": {"zenMinimalist": {"approach": "Single-column vertical flow", "principles": ["One primary focus per screen", "Generous white space", "Minimal cognitive load", "Clear visual hierarchy"], "structure": ["Header with actions", "Primary progress indicator", "Today's habits list", "Weekly journey visualization"]}, "mobileOptimized": {"phoneFrame": "393px x 852px (iPhone 16)", "safeAreas": "Account for Dynamic Island and home indicator", "scrolling": "Vertical scroll with fixed header/footer", "touchTargets": "Minimum 44px for interactive elements"}}, "interactions": {"neumorphicFeedback": {"press": "Inset shadow effect with subtle scale down", "hover": "Elevation increase with enhanced shadows", "transition": "200-300ms cubic-bezier easing", "pressedState": "Scale down to 98% with deeper inset shadows and theme background color for consistent tactile feedback"}, "buttonStates": {"normal": {"background": "Theme-based background color (--bg-primary)", "pressedBackground": "Same theme background color", "pressedShadow": "Inset concave shadows"}, "selected": {"background": "Gradient (--mindfulness to --info)", "pressedBackground": "Reverts to theme background color for consistency", "pressedShadow": "Standard inset shadows (not gradient-specific)"}}, "habitCompletion": {"visual": "Color change + checkmark animation", "feedback": "Immediate visual confirmation", "progress": "Real-time progress ring update"}}, "accessibility": {"lightMode": {"contrastRatio": "4.5:1 minimum for normal text", "colorBlindness": "Not solely dependent on color coding"}, "darkMode": {"contrastRatio": "7:1 minimum for enhanced readability", "eyeStrain": "Reduced blue light, comfortable viewing"}, "universal": {"focusStates": "Clear keyboard navigation indicators", "touchTargets": "Minimum 44px tap areas", "reducedMotion": "Respect prefers-reduced-motion settings"}}, "brandingElements": {"philosophy": "Tiny habits, big impact", "personality": "Calm, encouraging, progress-focused", "colorMeaning": {"green": "Growth, completion, wellness", "orange": "Energy, fitness, action", "purple": "Mindfulness, introspection, calm", "mint": "Fresh starts, renewal, health"}}, "implementationNotes": {"frameworks": "Tailwind CSS with custom neumorphic utilities", "responsive": "Mobile-first, 393px base width", "performance": "CSS transforms for animations, optimized shadows", "browserSupport": "Modern browsers with CSS Grid and backdrop-filter", "modeDetection": "prefers-color-scheme media query", "stateManagement": "Smooth transitions between light/dark modes"}, "usageGuidelines": {"neumorphicPrinciples": ["Consistent 20px border radius across cards", "Use Apple system colors for semantic meaning", "Apply elevated effects for cards, inset for inputs", "Maintain subtle depth without overwhelming shadows"], "zenLayout": ["Single column for focused attention", "Generous spacing between elements", "Clear visual hierarchy with typography", "Minimal interface, maximum clarity"], "habitTracking": ["Color-code by category for quick recognition", "Immediate visual feedback for actions", "Progress visualization for motivation", "Consistent interaction patterns"]}}}