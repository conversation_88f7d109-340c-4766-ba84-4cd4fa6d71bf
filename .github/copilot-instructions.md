# Copilot Instructions for `habitApp`

## Essential Architecture & Patterns

- **MVVM with SwiftUI 6**: All UI is built with SwiftUI 6 using the MVVM pattern. Use `@Observable` for ViewModels. No UIKit or Combine.
- **SwiftData for Persistence**: All data is persisted with SwiftData (not Core Data/Realm). Models must be CloudKit-compatible: no `@Attribute(.unique)`, all properties must have defaults or be optional, and relationships must be optional.
- **CloudKit Sync**: All persistence is designed for CloudKit sync. Use UUIDs for unique IDs and ensure all models are compatible.
- **Repository Pattern**: Data access is abstracted via repositories.
- **Strict Swift 6**: Use only Swift 6 syntax and concurrency. No legacy Swift 5 code.

## UI & Design System

- **Neumorphic Design**: All UI follows a custom neumorphic system. Reference HTML/CSS in `prototype/` for exact shadows, colors, and spacing.
  - Use `habitApp/Components/` for reusable SwiftUI components.
  - Color and shadow tokens are defined in `Assets.xcassets/`.
- **Typography**: Use SF Pro font, `.rounded` for UI, `.default` for body.
- **Spacing**: 8pt grid system for all margins and padding.

## Project Structure

- `habitApp/Models/`: SwiftData models
- `habitApp/Views/`: SwiftUI screens and subviews
- `habitApp/Components/`: Reusable UI elements
- `prototype/`: Reference HTML/CSS/JS for design and grid logic

## Developer Workflows

- **Build**: Use Xcode 16.3+ with iOS 18.4 SDK. Build for iPhone 16 simulator (iOS 18.4).
- **Test**: Write unit tests for ViewModels. Use SwiftUI previews for UI validation.
- **Build Command Example**:
  ```zsh
  xcodebuild -project habitApp.xcodeproj -scheme habitApp -destination 'platform=iOS Simulator,name=iPhone 16,OS=18.4' build
  ```
- **Debugging**: Use xcodebuildmcp to capture and analyze compile errors/warnings.

## Coding Conventions

- **Views**: Keep small, reusable, and logic-free. Delegate logic to ViewModels.
- **ViewModels**: Use `@Published` for state, expose only what the View needs.
- **Components**: Build generic, configurable SwiftUI components.
- **Concurrency**: Always use `Task { @ActorName in ... }` when crossing actor boundaries. Mark UI classes with `@MainActor`.
- **No Deprecated APIs**: Do not use UIKit, Combine, or legacy patterns.

## Reference Prototypes

- `prototype/dashboard.html`: Main UI
- `prototype/create-habit.html`: Forms
- `prototype/progress.html`: Analytics
- `prototype/habit-grid.js`: Progress grid logic

---

