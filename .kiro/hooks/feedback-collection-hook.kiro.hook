{"enabled": true, "name": "Pre-completion Feedback Collection", "description": "Automatically triggers feedback collection right before any task is finished, following mcp-feedback-enhanced instructions to gather user input and satisfaction", "version": "1", "when": {"type": "fileEdited", "patterns": ["tasks/*.md", "docs/*.md", "habitApp/**/*.swift", "prototype/*.html"]}, "then": {"type": "askAgent", "prompt": "A task appears to be nearing completion based on file changes. Before finalizing, please follow the mcp-feedback-enhanced instructions to collect user feedback. Ask the user about their satisfaction with the current progress, any concerns they have, and if they would like any adjustments before the task is marked as complete. Ensure you gather comprehensive feedback about the implementation quality, user experience, and any remaining issues."}}