# EditHabitView Implementation Plan

## Overview
Create an EditHabitView that allows users to modify existing habits with the exact UI/UX from the prototype/edit-habit.html. The view will load existing habit data, allow modifications, and include unsaved changes confirmation.

## Key Features to Implement

### 1. **View Structure & Navigation**
- Create `EditHabitView.swift` with neumorphic design matching prototype
- Header with back button and "Edit Habit" title
- Navigation handling with unsaved changes confirmation dialog
- Integration with DashboardView through habit row dropdown menu

### 2. **Core Components (Reuse from CreateHabitView)**
- Header section with back button (neumorphic style)
- Habit name input field with character counter (50 chars max)
- Category selection grid with custom category support
- Times per day counter with +/- buttons and tap-to-edit
- Frequency selection (Daily/Weekdays/Custom) with custom days picker
- Reminder toggle switch with time picker modal
- Update button (styled similar to create button but with "Update Habit" text)

### 3. **Data Management**
- Create `EditHabitViewModel.swift` based on CreateHabitViewModel
- Load existing habit data and populate form fields
- Track form state changes to detect unsaved modifications
- Handle habit updates through existing HabitRepository
- Proper error handling and validation

### 4. **User Experience Features**
- **Data Pre-population**: Load all existing habit properties into form
- **Unsaved Changes Detection**: Track form modifications
- **Confirmation Dialog**: Show "Continue editing or unsave changes" when back button pressed with unsaved changes
- **Validation**: Same validation rules as create habit (name required, category selected, etc.)
- **Auto-save Prevention**: Only save when "Update Habit" button is tapped

### 5. **Navigation Integration**
- Add navigation from HabitRowView dropdown menu "Edit Habit" option
- Pass habit ID/object to EditHabitView
- Return to DashboardView after successful update or cancellation

## Implementation Steps

### Phase 1: Core View Setup
1. Create `EditHabitView.swift` with basic structure
2. Copy and adapt UI components from CreateHabitView
3. Update styling to match prototype (colors, spacing, shadows)
4. Add navigation handling with back button

### Phase 2: View Model & Data Layer
1. Create `EditHabitViewModel.swift` extending CreateHabitViewModel pattern
2. Add habit loading, form population, and change tracking
3. Implement update functionality through HabitRepository
4. Add validation and error handling

### Phase 3: Form Pre-population & State Management
1. Load existing habit data on view appear
2. Populate all form fields (name, category, times, frequency, reminder, etc.)
3. Handle custom categories and custom days properly
4. Implement change detection for unsaved changes warning

### Phase 4: Navigation & UX Polish
1. Add EditHabitView navigation from HabitRowView dropdown
2. Implement unsaved changes confirmation dialog
3. Add success feedback and proper dismissal
4. Handle edge cases (habit not found, save errors, etc.)

### Phase 5: Testing & Integration
1. Test all form interactions and validation
2. Verify data persistence and updates
3. Test navigation flows and edge cases
4. Ensure neumorphic styling matches prototype exactly

## Files to Create/Modify

### New Files:
- `habitApp/Views/EditHabitView.swift`
- `habitApp/ViewModels/EditHabitViewModel.swift`

### Files to Modify:
- `habitApp/Components/HabitRowView.swift` - Add edit navigation
- `habitApp/Views/DashboardView.swift` - Add EditHabitView presentation
- `habitApp/Repositories/HabitRepository.swift` - Add update method if needed

## Reusable Components
- NeumorphicCard, NeumorphicButton components
- CategoryTypeButton, FrequencyButton, DayButton
- ToggleSwitch, time picker modal
- Form validation logic
- Neumorphic styling and shadow effects

## Technical Considerations
- Proper @Observable pattern for view model
- SwiftData context handling for updates
- CloudKit compatibility maintained
- Memory management and proper cleanup
- Accessibility support matching existing components

## Detailed Analysis from Prototype

### UI Components from edit-habit.html:
1. **Header**: Back button (neumorphic), "Edit Habit" title, empty right space
2. **Habit Name Section**: Text input with character counter (0/50)
3. **Category Section**: 4x2 grid with add custom button, selection state preserved
4. **Times Per Day**: +/- buttons with center display, tap to edit functionality
5. **Frequency Section**: Daily/Weekdays/Custom buttons with custom days picker
6. **Reminder Section**: Toggle switch with time picker modal
7. **Update Button**: Green button with save icon and "Update Habit" text

### Key Prototype Features:
- Form pre-population with existing habit data
- Unsaved changes detection and confirmation
- Exact neumorphic styling and animations
- Custom category support with icon/color selection
- Time picker with predefined slots
- Proper validation and error states

This plan ensures a complete, polished EditHabitView that matches the prototype design while reusing existing components and maintaining code consistency.