# Archive Feature Implementation Plan

Based on the prototype/archive.html analysis and current codebase structure, here's the comprehensive plan to implement the archive functionality:

## Core Architecture Changes

### 1. Data Model Updates
- **Update Habit model** to include archive properties (`isArchived: Bool`, `archivedAt: Date?`)
- **Modify HabitRepository** to handle archived habits filtering and operations
- Ensure CloudKit compatibility for new archive properties

### 2. Archive Management System
- **Create ArchiveViewModel** with @Observable pattern for managing archived habits
- Implement archive/restore/delete operations with proper SwiftData relationships
- Add confirmation dialogs for destructive actions (archive, delete)
- **Add search functionality** to filter archived habits by name/keywords

### 3. UI Components

#### Archive View (New)
- **Create ArchiveView** following neumorphic design from prototype
- **Header**: Back button, "Archive" title, **Search button** (replaces Clear All)
- **Search functionality**: Text field to filter archived habits by keywords
- Display filtered archived habits with inset shadow styling (opacity 0.7)
- Show archive date and read-only habit grids
- Implement dropdown menu with Restore/Delete options
- Add empty state when no archived habits exist or no search results

#### Dashboard Integration
- **Update HabitRowView** dropdown to include "Archive" option
- **Modify DashboardViewModel** to handle archiving with confirmation
- Filter out archived habits from main dashboard display

#### Navigation Updates
- **Update TabBarView** to navigate to ArchiveView
- Add proper navigation state management
- Implement active tab indication for archive section

## Key Features Implementation

### Archive Workflow
1. User taps dropdown → selects "Archive"
2. Confirmation dialog appears: "Archive [habit name]?"
3. If confirmed: habit marked as archived, removed from dashboard
4. Habit appears in Archive view with archive date

### Archive View Features
- **Header**: Back button, "Archive" title, Search button (magnifying glass icon)
- **Search**: Expandable search bar when search button tapped
- **List**: Archived habits (filtered by search) with archive date, dropdown menus
- **Actions**: Restore (moves back to active), Delete (permanent removal)
- **Empty States**: 
  - "No archived habits" when none exist
  - "No results found" when search yields no results

### Search Functionality
1. User taps search button → search bar expands
2. Real-time filtering as user types
3. Search by habit name (case-insensitive)
4. Clear search button to reset filter
5. Search state persists during session

### Restore Workflow
1. In Archive view, tap dropdown → select "Restore"
2. Habit immediately moves back to active habits
3. Archive properties cleared, appears in dashboard

### Delete Workflow
1. In Archive view, tap dropdown → select "Delete"
2. Confirmation dialog: "Permanently delete [habit name]?"
3. If confirmed: habit and all records permanently removed

## Technical Implementation Details

### SwiftData Considerations
- Archive properties as optional to maintain CloudKit compatibility
- Use soft delete pattern (isArchived flag) rather than separate model
- Maintain all existing relationships when archiving/restoring

### Search Implementation
- `@State private var searchText: String = ""`
- `@State private var isSearching: Bool = false`
- Computed property for filtered habits based on search text
- Debounced search to optimize performance

### UI/UX Consistency
- Follow existing neumorphic design patterns
- Use same color scheme and typography as prototype
- Implement smooth animations for state transitions
- Search bar matches existing input field styling
- Maintain grid view functionality in archived state (read-only)

### Error Handling
- Graceful handling of archive/restore failures
- User feedback through notifications/alerts
- Proper cleanup on operation failures

This plan maintains the existing architecture while adding comprehensive archive functionality with search capabilities that matches the prototype design and enhances user experience for managing archived habits.

## Implementation Progress

### Completed Tasks

#### 1. Data Model Updates ✅
- **Updated Habit model** (`/habitApp/Models/Habit.swift`):
  - Added `isArchived: Bool = false` property for CloudKit compatibility
  - Added `archivedAt: Date? = nil` property for archive timestamp
  - Added helper methods: `archive()`, `restore()`, `archiveDateString`
  - Maintains all existing relationships and properties

#### 2. Repository Layer ✅  
- **Updated HabitRepository** (`/habitApp/Repositories/HabitRepository.swift`):
  - Modified `fetchHabits()` to filter out archived habits using SwiftData predicates
  - Added `fetchArchivedHabits()` method sorted by archive date (newest first)
  - Added `archiveHabit()`, `restoreHabit()`, `permanentlyDeleteHabit()` methods
  - All methods properly handle SwiftData context saving and error handling

#### 3. ViewModels ✅
- **Created ArchiveViewModel** (`/habitApp/ViewModels/ArchiveViewModel.swift`):
  - @Observable pattern for SwiftUI 6 compatibility
  - Search functionality with real-time filtering
  - Archive management (restore/delete operations)
  - Loading states and error handling
  - Computed properties for filtered results and state management

- **Updated DashboardViewModel** (`/habitApp/ViewModels/DashboardViewModel.swift`):
  - Added `archiveHabit()` method using repository
  - Maintains existing functionality while filtering archived habits

#### 4. UI Components ✅
- **Created ArchiveView** (`/habitApp/Views/ArchiveView.swift`):
  - Follows neumorphic design pattern from prototype
  - Header with back button, title, and search functionality
  - Expandable search bar with real-time filtering
  - Custom `ArchivedHabitCard` with inset shadow styling (opacity 0.7)
  - Dropdown menus for restore/delete actions
  - Empty states for no archived habits and no search results
  - Delete confirmation dialog with proper messaging

- **Created ArchiveConfirmationDialog** (`/habitApp/Components/ArchiveConfirmationDialog.swift`):
  - SwiftUI ViewModifier pattern for reusability
  - Proper confirmation messaging with habit name
  - Extension method for easy usage: `.archiveConfirmationDialog()`

- **Updated HabitGridView** (`/habitApp/Views/HabitGridView.swift`):
  - Added `isReadOnly: Bool = false` parameter
  - Maintains backward compatibility with existing usage
  - Read-only mode disables interaction for archived habits

#### 5. Dashboard Integration ✅
- **Updated DashboardView** (`/habitApp/Views/DashboardView.swift`):
  - Added archive confirmation dialog state management
  - Updated HabitRowView calls to use archive functionality
  - Integrated confirmation workflow: dropdown → confirmation → archive

- **Updated HabitRowView** (`/habitApp/Components/HabitRowView.swift`):
  - Archive option remains in dropdown menu (existing functionality)
  - Updated HabitGridView calls to include `isReadOnly: false`
  - Maintains all existing functionality

#### 6. Navigation Updates ✅
- **Updated TabBarView** (`/habitApp/Views/Components/TabBarView.swift`):
  - Added sheet presentation for ArchiveView
  - Archive tab now properly navigates to archive functionality
  - Maintains existing tab functionality and notifications

#### 7. UI Refinements ✅
- **Fixed Archive Dropdown Menu** (`/habitApp/Views/ArchiveView.swift`):
  - Changed dropdown icon from `"ellipsis.vertical"` to `"ellipsis"` for proper 3-dot display
  - Optimized dropdown sizing with `.fixedSize()` to fit exactly 2 options (Restore/Delete)
  - Reduced minimum width to 100px for more compact archive-specific dropdown
  - Maintains proper 14px vertical padding for touch targets
  - Archive dropdown now properly sized for 2 options vs dashboard dropdown for 4 options

### Implementation Complete ✅

The archive feature is now fully implemented with:
- Complete data model support for archiving with CloudKit compatibility
- Robust search functionality for filtering archived habits
- Optimized UI components with proper neumorphic design
- Seamless integration with existing dashboard workflow
- Proper sizing and spacing for archive-specific dropdown menus
- Full archive/restore/delete functionality with confirmations

**Final Status**: Archive feature implementation complete and tested.