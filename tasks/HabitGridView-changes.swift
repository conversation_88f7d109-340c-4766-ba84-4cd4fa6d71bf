// HabitGridView.swift - cellOverlayView function changes
// Before:
/*
if day.isFirstDayOfMonth {
    Text("⭐")
        .font(.system(size: 4))
        .offset(x: -3, y: -3) // Position at top-left corner
        .shadow(color: .white.opacity(0.8), radius: 1)
        .shadow(color: .black.opacity(0.6), radius: 0.5)
}
*/

// After:
if day.isFirstDayOfMonth && appearanceManager.showMonthMarkers {
    Circle()
        .fill(Color.yellow)
        .frame(width: 3, height: 3)
        .offset(x: 3, y: -3) // Position at top-right corner
        .shadow(color: .black.opacity(0.3), radius: 0.5)
}

// Complete updated cellOverlayView function:
private func cellOverlayView(for day: HabitDay) -> some View {
    ZStack {
        if day.isToday {
            RoundedRectangle(cornerRadius: 2)
                .stroke(Color.red, lineWidth: 1)
        }
        
        if day.isFirstDayOfMonth && appearanceManager.showMonthMarkers {
            Circle()
                .fill(Color.yellow)
                .frame(width: 3, height: 3)
                .offset(x: 3, y: -3) // Position at top-right corner
                .shadow(color: .black.opacity(0.3), radius: 0.5)
        }
        
        if day.isStartDate {
            Text(day.level == 0 ? "▶️" : "🚀")
                .font(.system(size: day.level == 0 ? 5 : 6))
                .shadow(color: .white.opacity(0.9), radius: 2)
                .shadow(color: .black.opacity(0.7), radius: 1)
        }
    }
}
