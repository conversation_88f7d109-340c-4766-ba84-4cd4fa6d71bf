# Week Starts On Setting Implementation Plan

**Created:** 2025-07-29 22:35:52  
**Task:** Add "Week Starts On" setting to control whether week starts on Monday (current) or Sunday (when disabled)

## Current State Analysis

### Current Implementation
- **Grid Layout**: Starts Monday, ends Sunday (Monday-first layout)
- **Day Order**: `DayOfWeek.allCases` = `[.monday, .tuesday, .wednesday, .thursday, .friday, .saturday, .sunday]`
- **Grid Generation**: Uses `calendar.component(.weekday, from: current) != 2` to align to Monday (weekday=2)
- **Settings Location**: AppearanceManager handles appearance-related settings
- **UI Location**: SettingsView > Appearance Section has related toggles

### Affected Components
1. **AppearanceManager**: Needs new `weekStartsOnMonday` property
2. **SettingsView**: Needs new toggle in Appearance section
3. **HabitGridView**: Needs dynamic day ordering and grid generation
4. **CreateHabitView**: Custom frequency day selector needs reordering
5. **EditHabitView**: Custom frequency day selector needs reordering
6. **DayOfWeek enum**: Needs utility methods for flexible ordering

## Implementation Plan

### Phase 1: Core Infrastructure (AppearanceManager)

#### 1.1 Update AppearanceManager.swift
```swift
// Add new property
private let weekStartsOnMondayKey = "WeekStartsOnMonday"

var weekStartsOnMonday: Bool {
    didSet {
        userDefaults.set(weekStartsOnMonday, forKey: weekStartsOnMondayKey)
    }
}

// Update init()
self.weekStartsOnMonday = userDefaults.object(forKey: weekStartsOnMondayKey) != nil ? 
    userDefaults.bool(forKey: weekStartsOnMondayKey) : true // Default to Monday (current behavior)
```

#### 1.2 Add DayOfWeek Utility Methods
```swift
extension DayOfWeek {
    static func orderedCases(startingWith firstDay: DayOfWeek) -> [DayOfWeek] {
        // Returns reordered array starting with specified day
    }
    
    static func mondayFirst() -> [DayOfWeek] {
        return [.monday, .tuesday, .wednesday, .thursday, .friday, .saturday, .sunday]
    }
    
    static func sundayFirst() -> [DayOfWeek] {
        return [.sunday, .monday, .tuesday, .wednesday, .thursday, .friday, .saturday]
    }
    
    var calendarWeekday: Int {
        // Maps to Calendar.component(.weekday) values
        // Sunday = 1, Monday = 2, etc.
    }
}
```

### Phase 2: UI Settings (SettingsView)

#### 2.1 Add Toggle in Appearance Section
- **Location**: After "Today Highlight" setting
- **Title**: "Monday First"
- **Subtitle**: "Start weeks on Monday instead of Sunday"
- **Default**: `true` (Monday-first, current behavior)
- **Icon**: `"calendar.badge.minus"` (calendar icon)
- **Color**: `Color("Info")`

```swift
SettingRow(
    icon: "calendar.badge.minus",
    iconColor: Color("Info"),
    title: "Monday First",
    subtitle: "Start weeks on Monday instead of Sunday",
    isToggle: true,
    toggleValue: Binding(
        get: { appearanceManager.weekStartsOnMonday },
        set: { appearanceManager.weekStartsOnMonday = $0 }
    )
)
```

### Phase 3: Grid Display Logic (HabitGridView)

#### 3.1 Dynamic Day Label Ordering
```swift
private var dayLabelsView: some View {
    let orderedDays = appearanceManager.weekStartsOnMonday ? 
        DayOfWeek.mondayFirst() : DayOfWeek.sundayFirst()
    
    VStack(spacing: 1) {
        ForEach(Array(orderedDays.enumerated()), id: \.offset) { index, day in
            // existing label rendering logic
        }
    }
}
```

#### 3.2 Grid Generation Logic
```swift
private func generateWeekData() -> [WeekInfo] {
    let startWeekday = appearanceManager.weekStartsOnMonday ? 2 : 1 // Monday=2, Sunday=1
    
    // Align to start day
    while calendar.component(.weekday, from: current) != startWeekday {
        current = calendar.date(byAdding: .day, value: -1, to: current) ?? current
    }
    
    // rest of existing logic...
}
```

#### 3.3 Week Day Generation
```swift
private func generateWeekDays(weekStart: Date, calendar: Calendar, today: Date) -> [HabitDay] {
    let orderedDays = appearanceManager.weekStartsOnMonday ? 
        DayOfWeek.mondayFirst() : DayOfWeek.sundayFirst()
    
    // Generate days in correct order...
}
```

### Phase 4: Frequency Selectors (Create/Edit Views)

#### 4.1 Custom Days Section Reordering
- **CreateHabitView**: Update `customDaysSection`
- **EditHabitView**: Update `customDaysSection`

```swift
private var customDaysSection: some View {
    let orderedDays = // Get from AppearanceManager via environment
        appearanceManager.weekStartsOnMonday ? 
        DayOfWeek.mondayFirst() : DayOfWeek.sundayFirst()
    
    HStack(spacing: 0) {
        ForEach(orderedDays, id: \.self) { day in
            DayButton(
                day: day,
                isSelected: (viewModel?.customDays ?? Set()).contains(day)
            ) {
                // existing toggle logic
            }
            .frame(maxWidth: .infinity)
        }
    }
}
```

#### 4.2 Environment Integration
```swift
// Add to CreateHabitView and EditHabitView
@Environment(AppearanceManager.self) private var appearanceManager
```

### Phase 5: Testing & Validation

#### 5.1 Grid Alignment Tests
- [ ] Monday-first: Grid starts Monday, ends Sunday
- [ ] Sunday-first: Grid starts Sunday, ends Saturday  
- [ ] Day labels align correctly with grid columns
- [ ] Month markers appear on correct cells
- [ ] Today highlight appears on correct cell

#### 5.2 Frequency Selector Tests
- [ ] Custom frequency days show in correct order
- [ ] Monday-first: Mon-Sun layout
- [ ] Sunday-first: Sun-Sat layout
- [ ] Selected days persist correctly

#### 5.3 Settings Persistence Tests
- [ ] Setting persists across app launches
- [ ] Default value is Monday-first (true)
- [ ] Toggle animation works smoothly
- [ ] Grid updates immediately when setting changes

## Implementation Order

1. **AppearanceManager** - Add property and persistence
2. **DayOfWeek Extensions** - Add utility methods
3. **SettingsView** - Add UI toggle
4. **HabitGridView** - Update grid generation and labels
5. **CreateHabitView** - Update custom frequency selector
6. **EditHabitView** - Update custom frequency selector
7. **Testing** - Validate all scenarios

## Edge Cases to Consider

### Grid Generation
- **Month boundaries**: Ensure month markers appear correctly in both layouts
- **Year transitions**: January labels should work in both layouts
- **Today highlighting**: Must track correct column in both layouts

### Frequency Selection
- **Weekdays preset**: Should adapt to show Mon-Fri in correct positions
- **Custom selection**: Previously selected days should remain selected after layout change
- **Visual alignment**: Day buttons should visually align with grid columns

### User Experience
- **Live updates**: Grid should update immediately when setting changes
- **Animation**: Consider smooth transition animation for layout change
- **Consistency**: All day selectors throughout app should use same ordering

## Technical Notes

### Calendar Integration
- `Calendar.component(.weekday)` returns: Sunday=1, Monday=2, ..., Saturday=7
- Current grid aligns to Monday (weekday=2)
- Sunday-first would align to Sunday (weekday=1)

### Performance Considerations
- Grid regeneration should be efficient when setting changes
- Consider caching ordered day arrays to avoid repeated calculations
- Environment changes should trigger minimal view updates

### Backwards Compatibility
- Default to Monday-first to maintain current user experience
- Existing users won't see any change unless they modify the setting
- No data migration required as this only affects display logic

## Success Criteria

1. **Functional**: Week start day setting controls grid and frequency selector layout
2. **Persistent**: Setting value survives app restarts
3. **Consistent**: All day-related UI elements respect the setting
4. **Performant**: No noticeable lag when toggling setting
5. **Intuitive**: Clear labeling and immediate visual feedback
