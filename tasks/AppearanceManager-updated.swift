// AppearanceManager.swift - Complete updated file
import SwiftUI

enum AppearanceMode: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system:
            return "System"
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        }
    }
    
    var icon: String {
        switch self {
        case .system:
            return "circle.lefthalf.striped.horizontal"
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

@MainActor
@Observable
class AppearanceManager {
    private let userDefaults = UserDefaults.standard
    private let appearanceModeKey = "AppearanceMode"
    private let showFrequencyLabelsKey = "ShowFrequencyLabels"
    private let showMonthMarkersKey = "ShowMonthMarkers"
    
    var appearanceMode: AppearanceMode {
        didSet {
            userDefaults.set(appearanceMode.rawValue, forKey: appearanceModeKey)
        }
    }
    
    var showFrequencyLabels: Bool {
        didSet {
            userDefaults.set(showFrequencyLabels, forKey: showFrequencyLabelsKey)
        }
    }
    
    var showMonthMarkers: Bool {
        didSet {
            userDefaults.set(showMonthMarkers, forKey: showMonthMarkersKey)
        }
    }
    
    init() {
        let savedMode = userDefaults.string(forKey: appearanceModeKey) ?? AppearanceMode.system.rawValue
        self.appearanceMode = AppearanceMode(rawValue: savedMode) ?? .system
        self.showFrequencyLabels = userDefaults.bool(forKey: showFrequencyLabelsKey)
        self.showMonthMarkers = userDefaults.object(forKey: showMonthMarkersKey) != nil ? userDefaults.bool(forKey: showMonthMarkersKey) : true // Default to true
    }
}
