// SettingsView.swift - Appearance Section Addition
// Added this SettingRow in the AppearanceSection VStack after Frequency Labels:

SettingRow(
    icon: "calendar.circle.fill",
    iconColor: Color("Warning"),
    title: "Month Markers",
    subtitle: "Show yellow dots on first day of month",
    isToggle: true,
    toggleValue: Binding(
        get: { appearanceManager.showMonthMarkers },
        set: { appearanceManager.showMonthMarkers = $0 }
    )
)
