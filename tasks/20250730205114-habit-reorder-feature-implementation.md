# Habit Reorder Feature Implementation Plan

## Overview
Create a reorder feature in SettingsView that allows users to drag and reorder their active habits to change their display order in the dashboard.

## Current Architecture Analysis

### Data Models
- **Habit.swift**: Contains the habit model with properties for category, name, etc.
- **HabitRepository.swift**: Handles CRUD operations for habits
- **DashboardViewModel.swift**: Manages habit list display and filtering

### Current Habit Display Order
- Habits are currently sorted by `createdAt` date in ascending order (oldest first)
- No explicit `displayOrder` or `sortOrder` property exists

## Implementation Plan

### 1. Data Model Updates

#### 1.1 Add Display Order Property to Habit Model
```swift
// Add to Habit.swift
var displayOrder: Int = 0 // Default display order, CloudKit compatible
```

#### 1.2 Repository Methods for Reordering
```swift
// Add to HabitRepository.swift
func reorderHabits(_ habits: [Habit]) throws {
    for (index, habit) in habits.enumerated() {
        habit.displayOrder = index
    }
    try context.save()
}

func fetchHabitsOrderedByDisplayOrder() throws -> [Habit] {
    let descriptor = FetchDescriptor<Habit>(
        predicate: #Predicate<Habit> { !$0.isArchived },
        sortBy: [SortDescriptor(\.displayOrder), SortDescriptor(\.createdAt)]
    )
    return try context.fetch(descriptor)
}
```

### 2. Create Reorder View Components

#### 2.1 HabitReorderSheet.swift
- Sheet view containing draggable habit list
- Uses SwiftUI's `onMove` modifier for drag-and-drop
- Shows category icon with color and habit name
- Includes drag handle icon

#### 2.2 ReorderableHabitRow.swift
- Individual row component for reorder list
- Shows drag handle (⋮⋮), category icon with color, habit name
- Neumorphic styling to match app design

### 3. Settings Integration

#### 3.1 Add Reorder Setting Item
Add to SettingsView.swift in the Appearance section:
```swift
SettingRow(
    icon: "line.3.horizontal",
    iconColor: Color("Info"),
    title: "Reorder Habits",
    subtitle: "Change habit display order",
    showChevron: true
) {
    showingReorderModal = true
}
```

#### 3.2 State Management
```swift
@State private var showingReorderModal = false
```

### 4. Update Dashboard Display Order

#### 4.1 DashboardViewModel Updates
- Update `loadHabits()` to use new ordered fetch method
- Add method to handle reorder updates

#### 4.2 HabitRepository Integration
- Modify existing `fetchHabits()` to respect display order
- Ensure backward compatibility for existing habits

### 5. Migration Strategy

#### 5.1 Existing Habits Display Order
- When app updates, set `displayOrder` based on current `createdAt` order
- Ensure no breaking changes for existing users

#### 5.2 CloudKit Compatibility
- `displayOrder` property is CloudKit compatible (Int type)
- No unique constraints needed

## File Structure

```
habitApp/
├── Models/
│   └── Habit.swift (update with displayOrder)
├── Repositories/
│   └── HabitRepository.swift (add reorder methods)
├── ViewModels/
│   └── DashboardViewModel.swift (update for ordered display)
├── Views/
│   ├── SettingsView.swift (add reorder setting)
│   └── HabitReorderSheet.swift (new)
└── Components/
    └── ReorderableHabitRow.swift (new)
```

## Implementation Steps

### Phase 1: Data Layer
1. Add `displayOrder` property to Habit model
2. Add reorder methods to HabitRepository
3. Update fetch methods to respect display order
4. Handle migration for existing habits

### Phase 2: UI Components
1. Create ReorderableHabitRow component
2. Create HabitReorderSheet view
3. Implement drag-and-drop functionality

### Phase 3: Settings Integration
1. Add reorder setting item to SettingsView
2. Wire up sheet presentation
3. Handle reorder actions

### Phase 4: Dashboard Updates
1. Update DashboardViewModel to use ordered fetch
2. Test reorder functionality end-to-end
3. Ensure proper state management

## Technical Considerations

### SwiftUI Drag and Drop
- Use `onMove(perform:)` modifier on ForEach
- Handle IndexSet to Array conversion for reordering
- Animate reorder changes with `.animation()`

### State Management
- Pass reordered array back to parent view
- Update repository with new order
- Refresh dashboard display

### Neumorphic Design
- Match existing card styling for habit rows
- Use consistent spacing (8pt grid)
- Apply proper shadows and colors from design system

### Error Handling
- Handle reorder save failures gracefully
- Provide user feedback for successful reordering
- Maintain UI consistency during operations

## User Experience Flow

1. User taps "Reorder Habits" in Settings
2. Modal sheet presents with all active habits
3. Each habit shows: drag handle, category icon (colored), habit name
4. User drags habits to reorder
5. Changes are saved automatically or on dismiss
6. Dashboard reflects new order immediately
7. Order persists across app sessions

## Testing Strategy

1. Test with 0, 1, and multiple habits
2. Verify drag and drop functionality
3. Test CloudKit sync with reordered habits
4. Ensure backward compatibility
5. Test on different device sizes

This plan provides a comprehensive approach to implementing the habit reorder feature while maintaining consistency with the app's architecture and design system.
