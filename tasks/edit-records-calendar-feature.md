# Edit Records Calendar Feature Implementation Plan

## Overview
Build a calendar view feature that allows users to edit past habit records by tapping on calendar dates. The feature will use neumorphic design and follow the interaction patterns established in the HTML prototype.

## Background Research
Based on analysis of `prototype/dashboard.html`, the calendar modal includes:
- Monthly calendar view with neumorphic day cells
- Multi-level progression system for habit completion
- Rocket icon on habit start date
- Today highlighted with red border
- habit starting date indicated with rocket icon
- Future dates disabled (no interaction)
- Level cycling: tap to progress through completion levels

## Technical Approach

### Data Model (Already Exists)
- `Habit` model with `HabitRecord` relationship ✅
- Multi-level completion system (0-n levels) ✅
- SwiftData persistence ✅

### Design System (Established)
- Neumorphic styling with convex/concave shadows ✅
- Color levels using Success color with progressive opacity ✅
- Component patterns (NeumorphicCard, NeumorphicButton) ✅

## Implementation Tasks

### Task 1: Create Calendar ViewModel
**File:** `habitApp/ViewModels/CalendarRecordsViewModel.swift`

**Functionality:**
- `@Observable` class following Swift 6 patterns
- Current month/year state management
- Calendar grid calculation (weeks, days) 
- Date selection and level toggling logic
- Integration with existing HabitRecord CRUD operations

**Key Methods:**
```swift
func toggleDateLevel(_ date: Date)
func canEditDate(_ date: Date) -> Bool
func getLevelForDate(_ date: Date) -> Int
func isStartDate(_ date: Date) -> Bool
func isToday(_ date: Date) -> Bool
```

**Level Cycling Logic (example):**
- Single-check habits: 0 → 1 → 0
- Multi-check habits: 0 → 1 → 2 → 3 → 4 → 0
- Based on `habit.timesPerDay` property

### Task 2: Create Calendar Day Component  
**File:** `habitApp/Components/CalendarDayCell.swift`

**Functionality:**
- Neumorphic styling matching existing design system
- Display completion levels with proper colors and shadows
- Handle tap gestures for level cycling (past dates only)
- Show rocket icon for habit start date (centered)
- Show red border for today
- Disabled appearance for future dates

**Visual States:**
- Level 0: Gray background, no shadow
- Level 1: Success color with concave shadow (attempted)
- Levels 2-4: Success color with convex shadow, progressive opacity
- Start date: Rocket emoji (🚀) centered
- Today: Red border overlay
- Future: Grayed out, no interaction

### Task 3: Create Calendar Modal View
**File:** `habitApp/Views/CalendarRecordsView.swift`

**Functionality:**
- Sheet modal presentation
- Header with habit name and neumorphic close button
- Month navigation (previous/next) with neumorphic buttons
- Calendar grid layout (7x6 grid for consistency)
- Weekday labels (M, T, W, T, F, S, S)

**Layout:**
```
┌─────────────────────────────────┐
│ Edit "Habit Name" Records    [×]│
├─────────────────────────────────┤
│    [<]    July 2025     [>]     │
├─────────────────────────────────┤
│ M  T  W  T  F  S  S            │
│ 30 1  2  3  4  5  6            │
│ 7  8  9  10 11 12 13           │
│ ...                            │
└─────────────────────────────────┘
```

### Task 4: Integration Points
**Files to Modify:**

1. **`habitApp/Views/DashboardView.swift`**
   - Add `@State var showingCalendarRecords = false`
   - Add `@State var calendarHabit: Habit? = nil`
   - Add sheet presentation for CalendarRecordsView

2. **`habitApp/Components/HabitRowView.swift`**
   - Update "Edit Records" menu action to trigger calendar modal
   - Pass habit to parent view for calendar presentation

**Data Flow:**
```
HabitRowView "Edit Records" 
    → DashboardView sheet state
    → CalendarRecordsView modal
    → CalendarRecordsViewModel updates
    → SwiftData HabitRecord changes
    → HabitGridView auto-refresh
```

### Task 5: Testing & Validation

**Test Cases:**
1. **Single-check habit**: Level cycling 0→4→1→0
2. **Multi-check habit**: Level cycling 0→2→3→4→0  
3. **Start date**: Rocket icon displays correctly
4. **Today**: Red border shows on current date
5. **Future dates**: Cannot be interacted with
6. **Data persistence**: Changes save and reflect in grid view
7. **Month navigation**: Previous/next month works correctly

**Edge Cases:**
- Habit created today (start date = today)
- Habit created in previous month/year
- Large completion values (timesPerDay > 10)
- Rapid tapping (debouncing)

## File Structure
```
habitApp/
├── ViewModels/
│   └── CalendarRecordsViewModel.swift       [NEW]
├── Views/
│   ├── DashboardView.swift                  [MODIFY]
│   └── CalendarRecordsView.swift            [NEW]
├── Components/
│   ├── HabitRowView.swift                   [MODIFY]
│   └── CalendarDayCell.swift                [NEW]
```

## Design Specifications

### Colors (From Assets.xcassets)
- Background: `BackgroundPrimary`
- Shadows: `NeumorphicShadowDark`, `NeumorphicShadowLight`  
- Completion: `Success` with progressive opacity
- Text: `TextPrimary`, `TextSecondary`, `TextMuted`
- Today border: `Color.red`

### Typography
- Header: SF Pro 18pt, semibold
- Month/year: SF Pro 16pt, semibold
- Day numbers: SF Pro 14pt, medium
- Weekday labels: SF Pro 12pt, medium

### Spacing (8pt Grid)
- Modal padding: 20pt
- Cell spacing: 8pt
- Cell size: 44pt x 44pt (touch target)
- Corner radius: 12pt (cells), 20pt (modal)

## Success Criteria
- [ ] Calendar modal opens from "Edit Records" menu
- [ ] Past dates can be tapped to cycle completion levels
- [ ] Habit start date shows rocket icon (🚀)
- [ ] Today shows red border
- [ ] Future dates are disabled
- [ ] Changes persist to SwiftData
- [ ] Grid view reflects calendar changes
- [ ] Neumorphic design matches existing components
- [ ] Smooth animations and interactions
- [ ] No performance issues with date calculations

## Estimated Timeline
- Task 1 (ViewModel): 1-2 hours
- Task 2 (Day Cell): 2-3 hours  
- Task 3 (Modal View): 2-3 hours
- Task 4 (Integration): 1-2 hours
- Task 5 (Testing): 1-2 hours
- **Total: 7-12 hours**

## Dependencies
- Existing neumorphic design system ✅
- SwiftData models (Habit, HabitRecord) ✅
- Level progression logic from HabitGridView ✅
- No external packages required ✅

## Risk Mitigation
- **Complex date calculations**: Use Calendar.current and follow existing patterns in HabitGridView
- **Performance**: Cache calendar data, limit to current month view
- **Data consistency**: Ensure changes trigger proper SwiftData/SwiftUI updates
- **UI complexity**: Start with basic layout, add polish incrementally

---

**Note**: This implementation follows the exact interaction patterns established in `prototype/dashboard.html` while maintaining consistency with the existing SwiftUI codebase and neumorphic design system.
