import Foundation
import SwiftData

@Observable
@MainActor
final class CreateHabitViewModel {
    // Form State
    var selectedCategoryType: CategoryType?
    var habitName: String = ""
    var timesPerDay: Int = 1
    var selectedFrequency: FrequencyType = .daily
    var customDays: Set<DayOfWeek> = []
    var isReminderEnabled: Bool = false
    var reminderTime: Date = Date()
    var notificationTimes: [Date] = []
    
    // Custom Category State
    var customCategories: [CustomCategory] = []
    var allCategoryTypes: [CategoryType] = []
    
    // UI State
    var isCreating: Bool = false
    var errorMessage: String?
    
    private let repository: HabitRepository
    private let context: ModelContext
    
    init(context: ModelContext, notificationManager: NotificationManager) {
        self.repository = HabitRepository(context: context, notificationManager: notificationManager)
        self.context = context
        loadCustomCategories()
        setupAllCategories()
    }
    
    var canCreateHabit: Bool {
        selectedCategoryType != nil && !habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // Legacy computed property for backwards compatibility
    var selectedCategory: HabitCategory? {
        get {
            switch selectedCategoryType {
            case .predefined(let category):
                return category
            case .custom, .none:
                return nil
            }
        }
        set {
            if let category = newValue {
                selectedCategoryType = .predefined(category)
            } else {
                selectedCategoryType = nil
            }
        }
    }
    
    func createHabit() async -> Bool {
        guard canCreateHabit,
              let categoryType = selectedCategoryType else {
            errorMessage = "Please fill in all required fields"
            return false
        }
        
        isCreating = true
        errorMessage = nil
        
        do {
            // For now, use the existing method with a fallback category
            // TODO: Update repository to handle CategoryType properly
            let fallbackCategory: HabitCategory = {
                switch categoryType {
                case .predefined(let category):
                    return category
                case .custom:
                    return .health // Fallback - will be properly handled later
                }
            }()
            
            // Prepare notification times - if reminder enabled but no specific times, use reminderTime
            let finalNotificationTimes = isReminderEnabled ? (notificationTimes.isEmpty ? [reminderTime] : notificationTimes) : []
            
            try repository.createHabit(
                name: habitName.trimmingCharacters(in: .whitespacesAndNewlines),
                category: fallbackCategory,
                timesPerDay: timesPerDay,
                frequency: selectedFrequency,
                customDays: customDays,
                reminderEnabled: isReminderEnabled,
                reminderTime: isReminderEnabled ? reminderTime : nil
            )
            
            // Fix custom category assignment
            if case .custom(let customCategory) = categoryType,
               let createdHabit = getLastCreatedHabit() {
                createdHabit.categoryRawValue = "custom_\(customCategory.id.uuidString)"
                try context.save()
            }
            
            // Set notification times if enabled
            if isReminderEnabled, let createdHabit = getLastCreatedHabit() {
                createdHabit.notificationTimes = finalNotificationTimes
                try context.save()
            }
            
            // Reset form
            resetForm()
            isCreating = false
            return true
            
        } catch {
            errorMessage = "Failed to create habit: \(error.localizedDescription)"
            isCreating = false
            return false
        }
    }
    
    private func resetForm() {
        selectedCategoryType = nil
        habitName = ""
        timesPerDay = 1
        selectedFrequency = .daily
        customDays = []
        isReminderEnabled = false
        reminderTime = Date()
        notificationTimes = []
    }
    
    private func getLastCreatedHabit() -> Habit? {
        do {
            let descriptor = FetchDescriptor<Habit>(
                sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
            )
            return try context.fetch(descriptor).first
        } catch {
            return nil
        }
    }
    
    // MARK: - Notification Times Management
    
    func addNotificationTime(_ time: Date) {
        if !notificationTimes.contains(where: { Calendar.current.isDate($0, equalTo: time, toGranularity: .minute) }) {
            notificationTimes.append(time)
            notificationTimes.sort()
        }
    }
    
    func removeNotificationTime(at index: Int) {
        guard index < notificationTimes.count else { return }
        notificationTimes.remove(at: index)
    }
    
    func clearNotificationTimes() {
        notificationTimes.removeAll()
    }
    
    // MARK: - Custom Category Management
    
    func loadCustomCategories() {
        do {
            let descriptor = FetchDescriptor<CustomCategory>(
                sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
            )
            customCategories = try context.fetch(descriptor)
        } catch {
            print("Failed to load custom categories: \(error)")
            customCategories = []
        }
    }
    
    func setupAllCategories() {
        // Combine predefined and custom categories
        let predefinedTypes = HabitCategory.allCases.map { CategoryType.predefined($0) }
        let customTypes = customCategories.map { CategoryType.custom($0) }
        allCategoryTypes = predefinedTypes + customTypes
    }
    
    func addCustomCategory(_ category: CustomCategory) {
        // Save to SwiftData
        context.insert(category)
        
        do {
            try context.save()
            // Reload categories
            loadCustomCategories()
            setupAllCategories()
            
            // Auto-select the new category
            selectedCategoryType = .custom(category)
        } catch {
            errorMessage = "Failed to save custom category: \(error.localizedDescription)"
        }
    }
    
    func updateCustomCategory(_ category: CustomCategory) {
        do {
            try context.save()
            // Reload categories to reflect changes
            loadCustomCategories()
            setupAllCategories()
            
            // Update selected category if this was the one being edited
            if case .custom(let selectedCustom) = selectedCategoryType,
               selectedCustom.id == category.id {
                selectedCategoryType = .custom(category)
            }
        } catch {
            errorMessage = "Failed to update custom category: \(error.localizedDescription)"
        }
    }
    
    func deleteCustomCategory(_ category: CustomCategory) {
        context.delete(category)
        
        do {
            try context.save()
            loadCustomCategories()
            setupAllCategories()
            
            // Deselect if this was the selected category
            if case .custom(let selectedCustom) = selectedCategoryType,
               selectedCustom.id == category.id {
                selectedCategoryType = nil
            }
        } catch {
            errorMessage = "Failed to delete custom category: \(error.localizedDescription)"
        }
    }
    
    func refreshCategories() {
        loadCustomCategories()
        setupAllCategories()
    }
    
    func selectExample(_ example: String) {
        habitName = example
    }
}
