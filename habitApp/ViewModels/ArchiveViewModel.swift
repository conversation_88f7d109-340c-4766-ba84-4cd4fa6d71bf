import Foundation
import SwiftData

@MainActor
@Observable
final class ArchiveViewModel {
    private let repository: HabitRepository
    
    // State properties
    var archivedHabits: [Habit] = []
    var searchText: String = ""
    var isSearching: Bool = false
    var isLoading: Bool = false
    var errorMessage: String? = nil
    
    // Computed property for filtered habits
    var filteredHabits: [Habit] {
        if searchText.isEmpty {
            return archivedHabits
        } else {
            return archivedHabits.filter { habit in
                habit.name.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var hasArchivedHabits: Bool {
        !archivedHabits.isEmpty
    }
    
    var hasSearchResults: Bool {
        !filteredHabits.isEmpty
    }
    
    init(context: ModelContext, notificationManager: NotificationManager) {
        self.repository = HabitRepository(context: context, notificationManager: notificationManager)
    }
    
    // MARK: - Data Loading
    
    func loadArchivedHabits() {
        isLoading = true
        errorMessage = nil
        
        do {
            archivedHabits = try repository.fetchArchivedHabits()
        } catch {
            errorMessage = "Failed to load archived habits: \(error.localizedDescription)"
            print("Error loading archived habits: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - Search Functions
    
    func startSearch() {
        isSearching = true
    }
    
    func endSearch() {
        isSearching = false
        searchText = ""
    }
    
    func clearSearch() {
        searchText = ""
    }
    
    // MARK: - Archive Management
    
    func restoreHabit(_ habit: Habit) {
        do {
            try repository.restoreHabit(habit)
            // Remove from local array
            archivedHabits.removeAll { $0.id == habit.id }
            errorMessage = nil
            
            // Notify dashboard to refresh
            NotificationCenter.default.post(name: .habitRestored, object: nil)
        } catch {
            errorMessage = "Failed to restore habit: \(error.localizedDescription)"
            print("Error restoring habit: \(error)")
        }
    }
    
    func permanentlyDeleteHabit(_ habit: Habit) {
        do {
            try repository.permanentlyDeleteHabit(habit)
            // Remove from local array
            archivedHabits.removeAll { $0.id == habit.id }
            errorMessage = nil
        } catch {
            errorMessage = "Failed to delete habit: \(error.localizedDescription)"
            print("Error deleting habit: \(error)")
        }
    }
    
    // MARK: - Helper Functions
    
    func refreshData() {
        loadArchivedHabits()
    }
}