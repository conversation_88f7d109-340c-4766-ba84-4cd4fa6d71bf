import Foundation
import SwiftData

@Observable
@MainActor
final class DashboardViewModel {
  var habits: [Habit] = []
  var isLoading: Bool = false
  var errorMessage: String?
  var scrollToTodayTrigger: Date = Date()

  // Search properties
  var searchText: String = ""
  var isSearching: Bool = false

  private let repository: HabitRepository
  private let lastOpenDateKey = "LastAppOpenDate"

  init(context: ModelContext, notificationManager: NotificationManager) {
    self.repository = HabitRepository(context: context, notificationManager: notificationManager)
    checkForNewDay()
    initializeDisplayOrder()

    // Load initial data synchronously to avoid loading state
    loadInitialHabits()

    // Then optimize in background
    Task {
      await preloadOptimizations()
    }
  }

  private func initializeDisplayOrder() {
    do {
      try repository.initializeDisplayOrderForExistingHabits()
    } catch {
      errorMessage = "Failed to initialize habit order: \(error.localizedDescription)"
    }
  }

  func checkForNewDay() {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())

    let lastOpenDate = UserDefaults.standard.object(forKey: lastOpenDateKey) as? Date

    // If this is the first time opening the app, or it's a new day
    if let lastDate = lastOpenDate {
      if !calendar.isDate(lastDate, inSameDayAs: today) {
        // It's a new day - refresh habits data
        loadHabits()
      }
    }

    // Update the last open date to today
    UserDefaults.standard.set(today, forKey: lastOpenDateKey)
  }

  func refreshForNewDay() {
    checkForNewDay()
    loadHabits()
    triggerScrollToToday()
  }

  func triggerScrollToToday() {
    scrollToTodayTrigger = Date()
  }

  func loadHabits() {
    Task {
      await loadHabitsOptimized()
    }
  }

  private func loadInitialHabits() {
    // Load habits synchronously without showing loading state
    do {
      let fetchedHabits = try repository.fetchHabits()
      habits = fetchedHabits
    } catch {
      errorMessage = "Failed to load habits: \(error.localizedDescription)"
    }
  }

  private func preloadOptimizations() async {
    // No cache preloading needed - using optimized Habit model methods
  }

  private func loadHabitsOptimized() async {
    // Don't show loading state for background refreshes
    await MainActor.run {
      errorMessage = nil
    }

    do {
      let fetchedHabits = try repository.fetchHabits()

      // No cache preloading needed with optimized approach

      await MainActor.run {
        habits = fetchedHabits
      }
    } catch {
      await MainActor.run {
        errorMessage = "Failed to load habits: \(error.localizedDescription)"
      }
    }
  }

  func toggleHabit(_ habit: Habit) {
    do {
      try repository.toggleHabitCompletion(habit)
      // SwiftData @Model objects automatically update - no need to reload
    } catch {
      errorMessage = "Failed to update habit: \(error.localizedDescription)"
    }
  }

  func markHabitCompleted(_ habit: Habit, date: Date = Date(), level: Int16) {
    Task {
      do {
        try await repository.addOrUpdateRecord(
          for: habit.id,
          date: date,
          completionLevel: level
        )

        // UI updates automatically with SwiftData @Model objects
      } catch {
        await MainActor.run {
          errorMessage = "Failed to update habit: \(error.localizedDescription)"
        }
      }
    }
  }

  func deleteHabit(_ habit: Habit) {
    do {
      try repository.deleteHabit(habit)
      loadHabits()
    } catch {
      errorMessage = "Failed to delete habit: \(error.localizedDescription)"
    }
  }

  func archiveHabit(_ habit: Habit) {
    do {
      try repository.archiveHabit(habit)
      loadHabits()  // Refresh the list to remove archived habit
      errorMessage = nil
    } catch {
      errorMessage = "Failed to archive habit: \(error.localizedDescription)"
    }
  }

  var progressPercentage: Double {
    let relevantHabits = isSearching ? filteredHabits : habits
    // Only count habits that are scheduled for today
    let todaysHabits = relevantHabits.filter { $0.isScheduledForToday }
    guard !todaysHabits.isEmpty else { return 0 }
    let completedCount = todaysHabits.filter { $0.isCompletedToday }.count
    return Double(completedCount) / Double(todaysHabits.count)
  }

  // MARK: - Search Functions

  var filteredHabits: [Habit] {
    if searchText.isEmpty {
      return habits
    } else {
      return habits.filter { habit in
        habit.name.localizedCaseInsensitiveContains(searchText)
      }
    }
  }

  var hasSearchResults: Bool {
    !filteredHabits.isEmpty
  }

  func startSearch() {
    isSearching = true
  }

  func endSearch() {
    isSearching = false
    searchText = ""
  }

  func clearSearch() {
    searchText = ""
  }

  // MARK: - Habit Reordering

  func reorderHabits(_ orderedHabits: [Habit]) {
    do {
      try repository.reorderHabits(orderedHabits)
      loadHabits()  // Refresh the list to reflect new order
    } catch {
      errorMessage = "Failed to reorder habits: \(error.localizedDescription)"
    }
  }
}
