import Foundation
import SwiftData

@Observable
@MainActor
final class EditHabitViewModel {
    // Form State
    var selectedCategoryType: CategoryType?
    var habitName: String = ""
    var timesPerDay: Int = 1
    var selectedFrequency: FrequencyType = .daily
    var customDays: Set<DayOfWeek> = []
    var isReminderEnabled: Bool = false
    var reminderTime: Date = Date()
    var notificationTimes: [Date] = []
    
    // Custom Category State
    var customCategories: [CustomCategory] = []
    var allCategoryTypes: [CategoryType] = []
    
    // UI State
    var isUpdating: Bool = false
    var errorMessage: String?
    var hasUnsavedChanges: Bool = false
    
    // Original habit state for change detection
    private var originalHabitName: String = ""
    private var originalCategoryType: CategoryType?
    private var originalTimesPerDay: Int = 1
    private var originalFrequency: FrequencyType = .daily
    private var originalCustomDays: Set<DayOfWeek> = []
    private var originalIsReminderEnabled: Bool = false
    private var originalReminderTime: Date = Date()
    private var originalNotificationTimes: [Date] = []
    
    private let repository: HabitRepository
    private let context: ModelContext
    private let habitId: UUID
    
    init(habitId: UUID, context: ModelContext, notificationManager: NotificationManager) {
        self.habitId = habitId
        self.repository = HabitRepository(context: context, notificationManager: notificationManager)
        self.context = context
        loadCustomCategories()
        setupAllCategories()
        loadHabitData()
    }
    
    var canUpdateHabit: Bool {
        selectedCategoryType != nil && !habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // Legacy computed property for backwards compatibility
    var selectedCategory: HabitCategory? {
        get {
            switch selectedCategoryType {
            case .predefined(let category):
                return category
            case .custom, .none:
                return nil
            }
        }
        set {
            if let category = newValue {
                selectedCategoryType = .predefined(category)
            } else {
                selectedCategoryType = nil
            }
            updateChangeStatus()
        }
    }
    
    // MARK: - Habit Data Management
    
    private func loadHabitData() {
        do {
            let habitId = self.habitId // Store in local variable
            let descriptor = FetchDescriptor<Habit>(predicate: #Predicate<Habit> { habit in
                habit.id == habitId
            })
            
            guard let habit = try context.fetch(descriptor).first else {
                errorMessage = "Habit not found"
                return
            }
            
            // Load habit data into form
            habitName = habit.name
            timesPerDay = habit.timesPerDay
            selectedFrequency = habit.frequency
            customDays = habit.customDays
            isReminderEnabled = habit.reminderEnabled
            reminderTime = habit.reminderTime ?? Date()
            notificationTimes = habit.notificationTimes
            
            // Set category type - check if it's a custom category first
            if habit.categoryRawValue.hasPrefix("custom_") {
                // Try to find the custom category
                let customId = String(habit.categoryRawValue.dropFirst("custom_".count))
                if let uuid = UUID(uuidString: customId),
                   let customCategory = customCategories.first(where: { $0.id == uuid }) {
                    selectedCategoryType = .custom(customCategory)
                } else {
                    // Fallback to predefined category if custom not found
                    selectedCategoryType = .predefined(habit.category)
                }
            } else {
                selectedCategoryType = .predefined(habit.category)
            }
            
            // Store original values for change detection
            storeOriginalValues()
            
        } catch {
            errorMessage = "Failed to load habit: \(error.localizedDescription)"
        }
    }
    
    private func storeOriginalValues() {
        originalHabitName = habitName
        originalCategoryType = selectedCategoryType
        originalTimesPerDay = timesPerDay
        originalFrequency = selectedFrequency
        originalCustomDays = customDays
        originalIsReminderEnabled = isReminderEnabled
        originalReminderTime = reminderTime
        originalNotificationTimes = notificationTimes
        hasUnsavedChanges = false
    }
    
    private func updateChangeStatus() {
        hasUnsavedChanges = (
            habitName != originalHabitName ||
            selectedCategoryType?.id != originalCategoryType?.id ||
            timesPerDay != originalTimesPerDay ||
            selectedFrequency != originalFrequency ||
            customDays != originalCustomDays ||
            isReminderEnabled != originalIsReminderEnabled ||
            (isReminderEnabled && !Calendar.current.isDate(reminderTime, equalTo: originalReminderTime, toGranularity: .minute)) ||
            notificationTimes.count != originalNotificationTimes.count ||
            !notificationTimes.elementsEqual(originalNotificationTimes, by: { Calendar.current.isDate($0, equalTo: $1, toGranularity: .minute) })
        )
    }
    
    func updateHabit() async -> Bool {
        guard canUpdateHabit,
              let categoryType = selectedCategoryType else {
            errorMessage = "Please fill in all required fields"
            return false
        }
        
        isUpdating = true
        errorMessage = nil
        
        do {
            let habitId = self.habitId // Store in local variable
            let descriptor = FetchDescriptor<Habit>(predicate: #Predicate<Habit> { habit in
                habit.id == habitId
            })
            
            guard let habit = try context.fetch(descriptor).first else {
                errorMessage = "Habit not found"
                isUpdating = false
                return false
            }
            
            // Update habit properties
            habit.name = habitName.trimmingCharacters(in: .whitespacesAndNewlines)
            habit.timesPerDay = timesPerDay
            habit.frequencyRawValue = selectedFrequency.rawValue
            habit.customDaysRawValue = customDays.map { $0.rawValue }
            habit.reminderEnabled = isReminderEnabled
            habit.reminderTime = isReminderEnabled ? reminderTime : nil
            habit.notificationTimes = notificationTimes
            
            // Update category - handle both predefined and custom categories
            switch categoryType {
            case .predefined(let category):
                habit.categoryRawValue = category.rawValue
            case .custom(let customCategory):
                habit.categoryRawValue = "custom_\(customCategory.id.uuidString)"
            }
            
            try context.save()
            
            // Update original values to new current values
            storeOriginalValues()
            isUpdating = false
            return true
            
        } catch {
            errorMessage = "Failed to update habit: \(error.localizedDescription)"
            isUpdating = false
            return false
        }
    }
    
    // MARK: - Change Tracking for Form Fields
    
    func setHabitName(_ name: String) {
        habitName = name
        updateChangeStatus()
    }
    
    func setTimesPerDay(_ times: Int) {
        timesPerDay = times
        updateChangeStatus()
    }
    
    func setSelectedFrequency(_ frequency: FrequencyType) {
        selectedFrequency = frequency
        updateChangeStatus()
    }
    
    func setCustomDays(_ days: Set<DayOfWeek>) {
        customDays = days
        updateChangeStatus()
    }
    
    func setIsReminderEnabled(_ enabled: Bool) {
        isReminderEnabled = enabled
        updateChangeStatus()
    }
    
    func setReminderTime(_ time: Date) {
        reminderTime = time
        updateChangeStatus()
    }
    
    func setSelectedCategoryType(_ categoryType: CategoryType?) {
        selectedCategoryType = categoryType
        updateChangeStatus()
    }
    
    // MARK: - Custom Category Management
    
    func loadCustomCategories() {
        do {
            let descriptor = FetchDescriptor<CustomCategory>(
                sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
            )
            customCategories = try context.fetch(descriptor)
        } catch {
            print("Failed to load custom categories: \(error)")
            customCategories = []
        }
    }
    
    func setupAllCategories() {
        // Combine predefined and custom categories
        let predefinedTypes = HabitCategory.allCases.map { CategoryType.predefined($0) }
        let customTypes = customCategories.map { CategoryType.custom($0) }
        allCategoryTypes = predefinedTypes + customTypes
    }
    
    func addCustomCategory(_ category: CustomCategory) {
        // Save to SwiftData
        context.insert(category)
        
        do {
            try context.save()
            // Reload categories
            loadCustomCategories()
            setupAllCategories()
            
            // Auto-select the new category
            setSelectedCategoryType(.custom(category))
        } catch {
            errorMessage = "Failed to save custom category: \(error.localizedDescription)"
        }
    }
    
    func updateCustomCategory(_ category: CustomCategory) {
        do {
            try context.save()
            // Reload categories to reflect changes
            loadCustomCategories()
            setupAllCategories()
            
            // Update selected category if this was the one being edited
            if case .custom(let selectedCustom) = selectedCategoryType,
               selectedCustom.id == category.id {
                setSelectedCategoryType(.custom(category))
            }
        } catch {
            errorMessage = "Failed to update custom category: \(error.localizedDescription)"
        }
    }
    
    func deleteCustomCategory(_ category: CustomCategory) {
        context.delete(category)
        
        do {
            try context.save()
            loadCustomCategories()
            setupAllCategories()
            
            // Deselect if this was the selected category
            if case .custom(let selectedCustom) = selectedCategoryType,
               selectedCustom.id == category.id {
                setSelectedCategoryType(nil)
            }
        } catch {
            errorMessage = "Failed to delete custom category: \(error.localizedDescription)"
        }
    }
    
    func refreshCategories() {
        loadCustomCategories()
        setupAllCategories()
    }
    
    func selectExample(_ example: String) {
        setHabitName(example)
    }
    
    // MARK: - Notification Times Management
    
    func addNotificationTime(_ time: Date) {
        if !notificationTimes.contains(where: { Calendar.current.isDate($0, equalTo: time, toGranularity: .minute) }) {
            notificationTimes.append(time)
            notificationTimes.sort()
            updateChangeStatus()
        }
    }
    
    func removeNotificationTime(at index: Int) {
        guard index < notificationTimes.count else { return }
        notificationTimes.remove(at: index)
        updateChangeStatus()
    }
    
    func clearNotificationTimes() {
        notificationTimes.removeAll()
        updateChangeStatus()
    }
    
    // MARK: - Validation and Reset
    
    func resetToOriginal() {
        habitName = originalHabitName
        selectedCategoryType = originalCategoryType
        timesPerDay = originalTimesPerDay
        selectedFrequency = originalFrequency
        customDays = originalCustomDays
        isReminderEnabled = originalIsReminderEnabled
        reminderTime = originalReminderTime
        notificationTimes = originalNotificationTimes
        hasUnsavedChanges = false
    }
}
