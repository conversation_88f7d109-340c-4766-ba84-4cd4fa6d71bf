import SwiftUI

enum AppearanceMode: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system:
            return "System"
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        }
    }
    
    var icon: String {
        switch self {
        case .system:
            return "circle.lefthalf.striped.horizontal"
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

enum GridSize: String, CaseIterable {
    case small = "S"
    case medium = "M" 
    case large = "L"
    
    var displayName: String {
        switch self {
        case .small:
            return "S"
        case .medium:
            return "M"
        case .large:
            return "L"
        }
    }
    
    var cellSize: CGFloat {
        switch self {
        case .small:
            return 6
        case .medium:
            return 12
        case .large:
            return 18  // 1.5x of medium (12 * 1.5 = 18)
        }
    }
    
    var cornerRadius: CGFloat {
        switch self {
        case .small:
            return 1
        case .medium:
            return 2
        case .large:
            return 3  // 1.5x of medium
        }
    }
    
    var strokeWidth: CGFloat {
        switch self {
        case .small:
            return 0.5
        case .medium:
            return 1
        case .large:
            return 1.5  // 1.5x of medium
        }
    }
    
    var markerSize: CGFloat {
        switch self {
        case .small:
            return 2
        case .medium:
            return 3
        case .large:
            return 4.5  // 1.5x of medium
        }
    }
    
    var markerOffset: CGFloat {
        switch self {
        case .small:
            return 1.5
        case .medium:
            return 3
        case .large:
            return 4.5  // 1.5x of medium
        }
    }
    
    var emojiSize: CGFloat {
        switch self {
        case .small:
            return 3
        case .medium:
            return 6
        case .large:
            return 9  // 1.5x of medium
        }
    }
    
    var monthLabelFontSize: CGFloat {
        switch self {
        case .small:
            return 6
        case .medium:
            return 7
        case .large:
            return 10.5  // 1.5x of medium
        }
    }
    
    var dayLabelFontSize: CGFloat {
        switch self {
        case .small:
            return 6
        case .medium:
            return 8
        case .large:
            return 12  // 1.5x of medium
        }
    }
    
    // For backward compatibility with existing compactGrid boolean
    var isCompact: Bool {
        return self == .small
    }
}

@MainActor
@Observable
class AppearanceManager {
    private let userDefaults = UserDefaults.standard
    private let appearanceModeKey = "AppearanceMode"
    private let showFrequencyLabelsKey = "ShowFrequencyLabels"
    private let showMonthMarkersKey = "ShowMonthMarkers"
    private let showTodayHighlightKey = "ShowTodayHighlight"
    private let weekStartsOnMondayKey = "WeekStartsOnMonday"
    private let compactGridKey = "CompactGrid"  // Keep for migration
    private let gridSizeKey = "GridSize"
    private let useGridColorKey = "UseGridColor"
    private let showStartMarkKey = "ShowStartMark"
    
    var appearanceMode: AppearanceMode {
        didSet {
            userDefaults.set(appearanceMode.rawValue, forKey: appearanceModeKey)
        }
    }
    
    var showFrequencyLabels: Bool {
        didSet {
            userDefaults.set(showFrequencyLabels, forKey: showFrequencyLabelsKey)
        }
    }
    
    var showMonthMarkers: Bool {
        didSet {
            userDefaults.set(showMonthMarkers, forKey: showMonthMarkersKey)
        }
    }
    
    var showTodayHighlight: Bool {
        didSet {
            userDefaults.set(showTodayHighlight, forKey: showTodayHighlightKey)
        }
    }
    
    var weekStartsOnMonday: Bool {
        didSet {
            userDefaults.set(weekStartsOnMonday, forKey: weekStartsOnMondayKey)
        }
    }
    
    var gridSize: GridSize {
        didSet {
            userDefaults.set(gridSize.rawValue, forKey: gridSizeKey)
        }
    }
    
    var useGridColor: Bool {
        didSet {
            userDefaults.set(useGridColor, forKey: useGridColorKey)
        }
    }
    
    var showStartMark: Bool {
        didSet {
            userDefaults.set(showStartMark, forKey: showStartMarkKey)
        }
    }
    
    // Backward compatibility property
    var compactGrid: Bool {
        get { gridSize == .small }
        set { gridSize = newValue ? .small : .medium }
    }
    
    init() {
        let savedMode = userDefaults.string(forKey: appearanceModeKey) ?? AppearanceMode.system.rawValue
        self.appearanceMode = AppearanceMode(rawValue: savedMode) ?? .system
        self.showFrequencyLabels = userDefaults.bool(forKey: showFrequencyLabelsKey)
        self.showMonthMarkers = userDefaults.object(forKey: showMonthMarkersKey) != nil ? userDefaults.bool(forKey: showMonthMarkersKey) : true // Default to true
        self.showTodayHighlight = userDefaults.object(forKey: showTodayHighlightKey) != nil ? userDefaults.bool(forKey: showTodayHighlightKey) : true // Default to true
        self.weekStartsOnMonday = userDefaults.object(forKey: weekStartsOnMondayKey) != nil ? userDefaults.bool(forKey: weekStartsOnMondayKey) : true // Default to true (current behavior)
        self.useGridColor = userDefaults.object(forKey: useGridColorKey) != nil ? userDefaults.bool(forKey: useGridColorKey) : true // Default to true (feature enabled)
        self.showStartMark = userDefaults.object(forKey: showStartMarkKey) != nil ? userDefaults.bool(forKey: showStartMarkKey) : true // Default to true
        
        // Initialize gridSize with migration from old compactGrid setting
        if let savedGridSize = userDefaults.string(forKey: gridSizeKey),
           let gridSize = GridSize(rawValue: savedGridSize) {
            self.gridSize = gridSize
        } else {
            // Migration: convert old compactGrid boolean to new GridSize
            let wasCompact = userDefaults.bool(forKey: compactGridKey)
            self.gridSize = wasCompact ? .small : .medium
            // Save the migrated value
            userDefaults.set(self.gridSize.rawValue, forKey: gridSizeKey)
        }
    }
}
