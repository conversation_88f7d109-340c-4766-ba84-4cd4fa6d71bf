import Foundation
import SwiftData

@MainActor
@Observable
final class CalendarRecordsViewModel {
    var currentDate = Date()
    var habit: Habit?
    var modelContext: ModelContext?
    var appearanceManager: AppearanceManager?
    
    private let calendar = Calendar.current
    
    init(habit: Habit, modelContext: ModelContext, appearanceManager: AppearanceManager) {
        self.habit = habit
        self.modelContext = modelContext
        self.appearanceManager = appearanceManager
    }
    
    // MARK: - Calendar Navigation
    
    var currentMonthName: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter.string(from: currentDate)
    }
    
    func goToPreviousMonth() {
        currentDate = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
    }
    
    func goToNextMonth() {
        currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
    }
    
    // MARK: - Calendar Grid Data
    
    var calendarDays: [CalendarDay] {
        generateCalendarDays()
    }
    
    private func generateCalendarDays() -> [CalendarDay] {
        guard let habit = habit else { return [] }
        
        let monthStart = calendar.dateInterval(of: .month, for: currentDate)?.start ?? currentDate
        let monthEnd = calendar.dateInterval(of: .month, for: currentDate)?.end ?? currentDate
        
        // Find the first day of the calendar grid based on week start setting
        var gridStart = monthStart
        let targetWeekday = appearanceManager?.weekStartsOnMonday == true ? 2 : 1  // Monday=2, Sunday=1
        while calendar.component(.weekday, from: gridStart) != targetWeekday {
            gridStart = calendar.date(byAdding: .day, value: -1, to: gridStart) ?? gridStart
        }
        
        // Generate 42 days (6 weeks) for consistent grid
        var days: [CalendarDay] = []
        var currentDay = gridStart
        
        for _ in 0..<42 {
            let isInCurrentMonth = calendar.isDate(currentDay, equalTo: monthStart, toGranularity: .month)
            let isToday = calendar.isDate(currentDay, inSameDayAs: Date())
            let isFuture = currentDay > Date()
            let isStartDate = calendar.isDate(currentDay, inSameDayAs: habit.createdAt)
            let level = getLevelForDate(currentDay)
            
            days.append(CalendarDay(
                date: currentDay,
                dayNumber: calendar.component(.day, from: currentDay),
                isInCurrentMonth: isInCurrentMonth,
                isToday: isToday,
                isFuture: isFuture,
                isStartDate: isStartDate,
                level: level
            ))
            
            currentDay = calendar.date(byAdding: .day, value: 1, to: currentDay) ?? currentDay
        }
        
        return days
    }
    
    // MARK: - Date Information
    
    func canEditDate(_ date: Date) -> Bool {
        // Can only edit past dates and today
        return date <= Date()
    }
    
    func getLevelForDate(_ date: Date) -> Int {
        guard let habit = habit else { return 0 }
        
        // For today, check if there's already a record or use 0
        if calendar.isDate(date, inSameDayAs: Date()) {
            // Check for existing record first
            if let existingRecord = habit.records?.first(where: { record in
                calendar.isDate(record.date, inSameDayAs: date)
            }) {
                return Int(existingRecord.completionLevel)
            }
            // If no record exists, return current completion level from habit
            return habit.todayCompletionLevel
        }
        
        // For other dates, check records
        let habitRecord = habit.records?.first { record in
            calendar.isDate(record.date, inSameDayAs: date)
        }
        return Int(habitRecord?.completionLevel ?? 0)
    }
    
    func isStartDate(_ date: Date) -> Bool {
        guard let habit = habit else { return false }
        return calendar.isDate(date, inSameDayAs: habit.createdAt)
    }
    
    func isToday(_ date: Date) -> Bool {
        return calendar.isDate(date, inSameDayAs: Date())
    }
    
    // MARK: - Level Toggling
    
    func toggleDateLevel(_ date: Date) {
        guard let habit = habit,
              let modelContext = modelContext,
              canEditDate(date) else { return }
        
        let currentLevel = getLevelForDate(date)
        let newLevel = calculateNextLevel(currentLevel: currentLevel, habit: habit)
        
        // Find or create habit record for this date
        let startOfDay = calendar.startOfDay(for: date)
        
        if let existingRecord = habit.records?.first(where: { record in
            calendar.isDate(record.date, inSameDayAs: date)
        }) {
            // Update existing record
            existingRecord.completionLevel = Int16(newLevel)
        } else {
            // Create new record
            let newRecord = HabitRecord(date: startOfDay, completionLevel: Int16(newLevel), habit: habit)
            modelContext.insert(newRecord)
            
            // Add to habit's records relationship
            if habit.records == nil {
                habit.records = []
            }
            habit.records?.append(newRecord)
        }
        
        // Save changes
        do {
            try modelContext.save()
        } catch {
            print("Error saving habit record: \(error)")
        }
        
        // For today's date, also update the habit's today completion level
        if calendar.isDate(date, inSameDayAs: Date()) {
            // The habit's todayCompletionLevel will be automatically updated via the computed property
            // No need to manually update it here
        }
    }
    
    private func calculateNextLevel(currentLevel: Int, habit: Habit) -> Int {
        let maxLevel = habit.timesPerDay
        
        if habit.timesPerDay == 1 {
            // Single-check habit: 0 → 1 → 0
            switch currentLevel {
            case 0:
                return 1
            case 1:
                return 0
            default:
                return 0
            }
        } else {
            // Multi-check habit: 0 → 1 → 2 → 3 → ... → maxLevel → 0
            if currentLevel >= maxLevel {
                return 0
            } else {
                return currentLevel + 1
            }
        }
    }
}

// MARK: - Supporting Types

struct CalendarDay {
    let date: Date
    let dayNumber: Int
    let isInCurrentMonth: Bool
    let isToday: Bool
    let isFuture: Bool
    let isStartDate: Bool
    let level: Int
}
