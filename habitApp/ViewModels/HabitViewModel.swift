import Foundation
import SwiftData

@Observable
@MainActor
final class HabitViewModel {
    private let repository: HabitRepository
    
    init(repository: HabitRepository) {
        self.repository = repository
    }
    
    // MARK: - Optimized Record Operations
    
    /// Optimized record lookup using cached index - O(1) instead of O(n)
    func recordForDate(_ habitId: UUID, _ date: Date) async -> HabitRecord? {
        return await repository.getRecord(for: habitId, on: date)
    }
    
    /// Batch load grid data with optimized lookups
    func generateOptimizedGridDays(for habit: Habit, startDate: Date, totalDays: Int) async -> [HabitDay] {
        var result: [HabitDay] = []
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // NOTE: Cache preloading is handled by DashboardViewModel, not here
        // Avoid race conditions by not triggering additional cache operations during grid rendering
        
        for dayOffset in (0..<totalDays).reversed() {
            guard let day = calendar.date(byAdding: .day, value: -dayOffset, to: startDate) else {
                continue
            }
            
            let isToday = calendar.isDate(day, inSameDayAs: today)
            
            // O(1) lookup instead of O(n) search
            let level: Int
            if isToday {
                // For today, still use the reactive property to ensure fresh data
                level = habit.todayCompletionLevel
            } else {
                let record = await recordForDate(habit.id, day)
                level = Int(record?.completionLevel ?? 0)
            }
            
            let isFuture = day > today
            let isStartDate = calendar.isDate(day, inSameDayAs: habit.createdAt)
            let isFirstDayOfMonth = calendar.component(.day, from: day) == 1
            
            result.append(HabitDay(
                date: day,
                level: level,
                isToday: isToday,
                isFuture: isFuture,
                isStartDate: isStartDate,
                isFirstDayOfMonth: isFirstDayOfMonth
            ))
        }
        
        return result
    }
    
    /// Get completion level for a specific date - optimized lookup
    func getCompletionLevel(for habitId: UUID, on date: Date) async -> Int {
        if let record = await recordForDate(habitId, date) {
            return Int(record.completionLevel)
        }
        return 0
    }
    
    /// Batch lookup for multiple dates - more efficient than individual calls
    func getCompletionLevels(for habitId: UUID, dates: [Date]) async -> [Date: Int] {
        // Ensure cache is loaded
        await repository.preloadCacheForHabits([habitId])
        
        var results: [Date: Int] = [:]
        
        for date in dates {
            if let record = await recordForDate(habitId, date) {
                results[date] = Int(record.completionLevel)
            } else {
                results[date] = 0
            }
        }
        
        return results
    }
}
