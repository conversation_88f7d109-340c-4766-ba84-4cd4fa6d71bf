//
//  habitAppApp.swift
//  habitApp
//
//  Created by xiaomeng on 7/16/25.
//

import SwiftUI
import SwiftData

@main
struct habitAppApp: App {
    @State private var appearanceManager = AppearanceManager()
    @State private var notificationManager = NotificationManager()
    
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Habit.self,
            HabitRecord.self,
            CustomCategory.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(appearanceManager)
                .environment(notificationManager)
                .environment(HabitRepository(context: sharedModelContainer.mainContext, notificationManager: notificationManager))
                .preferredColorScheme(appearanceManager.appearanceMode.colorScheme)
                .task {
                    await notificationManager.checkAuthorizationStatus()
                }
        }
        .modelContainer(sharedModelContainer)
    }
}
