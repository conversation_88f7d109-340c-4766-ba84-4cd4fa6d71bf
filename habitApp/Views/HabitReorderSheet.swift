import SwiftUI

struct HabitReorderSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var orderedHabits: [Habit]
    let originalHabits: [Habit]
    let onReorder: ([Habit]) -> Void
    
    init(habits: [Habit], onReorder: @escaping ([Habit]) -> Void) {
        self.originalHabits = habits
        self.onReorder = onReorder
        self._orderedHabits = State(initialValue: habits)
        print("🔄 HabitReorderSheet initialized with \(habits.count) habits")
        for (index, habit) in habits.enumerated() {
            print("  \(index + 1): \(habit.name) (order: \(habit.displayOrder))")
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
                // Header
                headerView
                    .padding(.horizontal, 16)
                    .padding(.top, 3)
                
                // Instructions Header
                VStack(spacing: 8) {
                    Image(systemName: "arrow.up.arrow.down")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(Color("Info"))
                    
                    Text("Drag habits to change their order")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color("TextSecondary"))
                        .multilineTextAlignment(.center)
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
                .background(Color("BackgroundPrimary"))
                
                // Reorderable List
                if orderedHabits.isEmpty {
                    Spacer()
                    VStack(spacing: 16) {
                        Image(systemName: "tray")
                            .font(.system(size: 32))
                            .foregroundColor(Color("TextMuted"))
                        
                        Text("No Active Habits")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color("TextPrimary"))
                        
                        Text("Create some habits first to reorder them")
                            .font(.system(size: 14))
                            .foregroundColor(Color("TextSecondary"))
                            .multilineTextAlignment(.center)
                    }
                    Spacer()
                } else {
                    List {
                        ForEach(orderedHabits, id: \.id) { habit in
                            ReorderableHabitRow(habit: habit)
                                .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                                .listRowBackground(Color("BackgroundPrimary"))
                                .listRowSeparator(.hidden)
                        }
                        .onMove(perform: moveHabits)
                    }
                    .listStyle(.plain)
                    .scrollContentBackground(.hidden)
                    .background(Color("BackgroundPrimary"))
                    .environment(\.editMode, .constant(.active))
                }
            }
            .background(Color("BackgroundPrimary"))
            .navigationBarHidden(true)
        }
    
    // MARK: - Header
    
    private var headerView: some View {
        HStack {
            // Back button
            NeumorphicButton(
                icon: "chevron.left",
                size: 40,
                isPressed: false
            ) {
                dismiss()
            }
            
            Spacer()
            
            // Title
            VStack(spacing: 2) {
                Text("Reorder Habits")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(Color("TextPrimary"))
                
                Text("\(orderedHabits.count) active habit\(orderedHabits.count == 1 ? "" : "s")")
                    .font(.system(size: 14))
                    .foregroundStyle(Color("TextSecondary"))
            }
            
            Spacer()
            
            // Placeholder for alignment
            NeumorphicButton(
                icon: "chevron.left",
                size: 40,
                isPressed: false
            ) {
                // No action - just for spacing
            }
            .opacity(0)
        }
        .padding(.bottom, 24)
    }
    
    private func moveHabits(from source: IndexSet, to destination: Int) {
        orderedHabits.move(fromOffsets: source, toOffset: destination)
        
        // Auto-save after reorder
        print("🔄 Auto-saving new order:")
        for (index, habit) in orderedHabits.enumerated() {
            print("  \(index + 1): \(habit.name)")
        }
        onReorder(orderedHabits)
    }
}

#Preview {
    HabitReorderSheet(
        habits: [
            Habit(name: "Morning Walk", category: .fitness),
            Habit(name: "Read 1 Page", category: .learning),
            Habit(name: "Drink Water", category: .health),
            Habit(name: "Meditation", category: .mindfulness)
        ],
        onReorder: { _ in }
    )
}
