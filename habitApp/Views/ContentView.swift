//
//  ContentView.swift
//  habitApp
//
//  Created by xiaomeng on 7/16/25.
//

import SwiftData
import SwiftUI

struct ContentView: View {
  @Environment(\.modelContext) private var modelContext
  @Query private var items: [Item]
  @State private var showingSettings: Bool = false

  var body: some View {
    NavigationStack {
      ZStack {
        // Main content - Dashboard View
        DashboardView(showingSettings: $showingSettings)
          .opacity(showingSettings ? 0.3 : 1)
          .scaleEffect(showingSettings ? 0.95 : 1)
          .blur(radius: showingSettings ? 2 : 0)
          .animation(.easeInOut(duration: 0.3), value: showingSettings)

        // Settings View Overlay
        if showingSettings {
          NavigationStack {
            SettingsView(showingSettings: $showingSettings)
          }
          .transition(
            .asymmetric(
              insertion: .move(edge: .trailing),
              removal: .move(edge: .trailing)
            )
          )
          .zIndex(1)
        }
      }
    }
  }

  private func addItem() {
    withAnimation {
      let newItem = Item(timestamp: Date())
      modelContext.insert(newItem)
    }
  }

  private func deleteItems(offsets: IndexSet) {
    withAnimation {
      for index in offsets {
        modelContext.delete(items[index])
      }
    }
  }
}

#Preview {
  ContentView()
    .modelContainer(for: Item.self, inMemory: true)
}
