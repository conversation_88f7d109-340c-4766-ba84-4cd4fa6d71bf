import SwiftUI
import SwiftData

struct EditHabitView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(AppearanceManager.self) private var appearanceManager
  @Environment(NotificationManager.self) private var notificationManager
  @State private var viewModel: EditHabitViewModel?
  @State private var showTimePicker = false
  @State private var showExamples = false
  @State private var isEditingTimes = false
  @State private var showCustomCategoryModal = false
  @State private var showEditCategoryModal = false
  @State private var editingCategory: CustomCategory? = nil
  @State private var showUnsavedChangesAlert = false

  let habitId: UUID

  private var canUpdateHabit: Bool {
    viewModel?.canUpdateHabit ?? false
  }

  var body: some View {
    NavigationView {
      ZStack {
        Color("BackgroundPrimary")
          .ignoresSafeArea()

        ScrollView {
          VStack(spacing: 24) {
            // Header
            headerSection

            // Category Selection
            categorySection

            // Habit Name
            habitNameSection

            // Quick Examples (shown when category is selected)
            if showExamples {
              examplesSection
            }

            // Times Per Day
            timesPerDaySection

            // Frequency
            frequencySection

            // Reminder Time
            reminderSection

            // Update Button
            updateButton

            Spacer(minLength: 100)
          }
          .padding(.horizontal, 16)
          .padding(.top, 24)
        }

        // Time Picker Modal
        if showTimePicker {
          timePickerModal
        }
        
        // Custom Category Modal
        if showCustomCategoryModal {
          CustomCategoryModal(isPresented: $showCustomCategoryModal) { customCategory in
            viewModel?.addCustomCategory(customCategory)
          }
        }
        
        // Edit Category Modal
        if showEditCategoryModal {
          CustomCategoryModal(isPresented: $showEditCategoryModal, editingCategory: editingCategory) { updatedCategory in
            viewModel?.updateCustomCategory(updatedCategory)
          }
        }
      }
      .navigationBarHidden(true)
      .onAppear {
        if viewModel == nil {
          viewModel = EditHabitViewModel(habitId: habitId, context: modelContext, notificationManager: notificationManager)
        }
      }
      .alert("Unsaved Changes", isPresented: $showUnsavedChangesAlert) {
        Button("Continue Editing", role: .cancel) { }
        Button("Discard Changes", role: .destructive) {
          dismiss()
        }
      } message: {
        Text("You have unsaved changes. Do you want to continue editing or discard them?")
      }
    }
    .environment(appearanceManager)
  }

  // MARK: - Header Section
  private var headerSection: some View {
    HStack {
      // Back Button
      NeumorphicButton(icon: "arrow.left", size: 40) {
        handleBackNavigation()
      }

      Spacer()

      // Title
      VStack(spacing: 2) {
        Text("Edit Habit")
          .font(.system(size: 20, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text("Modify your mini habit")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextSecondary"))
      }

      Spacer()

      // Empty space for symmetry
      Rectangle()
        .fill(Color.clear)
        .frame(width: 40, height: 40)
    }
  }

  // MARK: - Category Section
  private var categorySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        HStack {
          Text("Choose Category")
            .font(.system(size: 18, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))

          Spacer()
          
          // Edit Category Button (only show if a custom category is selected)
          if case .custom(let customCategory) = viewModel?.selectedCategoryType {
            NeumorphicButton(icon: "pencil", size: 32) {
              editingCategory = customCategory
              showEditCategoryModal = true
            }
          }

          NeumorphicButton(icon: "plus", size: 32) {
            showCustomCategoryModal = true
          }
        }

        LazyVGrid(
          columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8
        ) {
          ForEach(viewModel?.allCategoryTypes ?? [], id: \.id) { categoryType in
            CategoryTypeButton(
              categoryType: categoryType,
              isSelected: (viewModel?.selectedCategoryType?.id ?? "") == categoryType.id,
              action: {
                if (viewModel?.selectedCategoryType?.id ?? "") == categoryType.id {
                  // Deselect if tapping the same category
                  viewModel?.setSelectedCategoryType(nil)
                  showExamples = false
                } else {
                  // Select new category
                  viewModel?.setSelectedCategoryType(categoryType)
                  showExamples = true
                }
              },
              onDelete: { customCategory in
                viewModel?.deleteCustomCategory(customCategory)
              }
            )
          }
        }
        
        // Instruction note - only show if custom categories exist
        if !(viewModel?.customCategories.isEmpty ?? true) {
          HStack(spacing: 8) {
            Image(systemName: "info.circle")
              .font(.system(size: 12))
              .foregroundStyle(Color("Info"))
            
            Text("Long press custom categories to delete")
              .font(.system(size: 12))
              .foregroundStyle(Color("TextMuted"))
            
            Spacer()
          }
          .padding(.top, 8)
        }
      }
    }
  }

  // MARK: - Habit Name Section
  private var habitNameSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        Text("Habit Name")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        ZStack {
          // Base background
          RoundedRectangle(cornerRadius: 12)
            .fill(Color("BackgroundPrimary"))

          // Inset shadow effect - Top and Left inner shadows (thicker for more pronounced effect)
          VStack(spacing: 0) {
            // Top inner shadow - made thicker and more intense
            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                    .init(color: Color.clear, location: 1),
                  ]),
                  startPoint: .top,
                  endPoint: .bottom
                )
              )
              .frame(height: 12)

            Spacer()
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          HStack(spacing: 0) {
            // Left inner shadow - made thicker and more intense
            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                    .init(color: Color.clear, location: 1),
                  ]),
                  startPoint: .leading,
                  endPoint: .trailing
                )
              )
              .frame(width: 12)

            Spacer()
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          // Bottom-right light highlight for depth - made thicker to match top-left shadows
          VStack(spacing: 0) {
            Spacer()

            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                  ]),
                  startPoint: .top,
                  endPoint: .bottom
                )
              )
              .frame(height: 12)
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          HStack(spacing: 0) {
            Spacer()

            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                  ]),
                  startPoint: .leading,
                  endPoint: .trailing
                )
              )
              .frame(width: 12)
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          // Text field
          TextField("e.g., 1 push-up, 1 page, 1 minute walk", text: Binding(
            get: { viewModel?.habitName ?? "" },
            set: { viewModel?.setHabitName($0) }
          ))
            .font(.system(size: 16))
            .foregroundStyle(Color("TextPrimary"))
            .padding(16)
            .background(Color.clear)
        }
        .clipShape(RoundedRectangle(cornerRadius: 12))

        Text(
          viewModel?.selectedCategoryType == nil
            ? "Select a category to see suggestions" : "Keep it small and specific"
        )
        .font(.system(size: 12))
        .foregroundStyle(Color("TextMuted"))
      }
    }
  }

  // MARK: - Examples Section
  private var examplesSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 12) {
        Text("Examples")
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        if let categoryType = viewModel?.selectedCategoryType {
          VStack(spacing: 8) {
            ForEach(categoryType.examples, id: \.self) { example in
              ExampleButton(text: example) {
                viewModel?.selectExample(example)
              }
            }
          }
        }
      }
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: showExamples)
  }

  // MARK: - Times Per Day Section
  private var timesPerDaySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(spacing: 16) {
        Text("Times per Day")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))
          .frame(maxWidth: .infinity, alignment: .leading)

        HStack(spacing: 16) {
          // Minus Button
          NeumorphicButton(icon: "minus", size: 40) {
            if (viewModel?.timesPerDay ?? 1) > 1 {
              viewModel?.setTimesPerDay((viewModel?.timesPerDay ?? 1) - 1)
            }
          }

          Spacer()

          // Times Display
          if isEditingTimes {
            TextField("", value: Binding(
              get: { viewModel?.timesPerDay ?? 1 },
              set: { viewModel?.setTimesPerDay($0) }
            ), format: .number)
              .font(.system(size: 24, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))
              .multilineTextAlignment(.center)
              .frame(width: 80, height: 48)
              .background(Color("BackgroundPrimary"))
              .clipShape(RoundedRectangle(cornerRadius: 12))
              .overlay {
                RoundedRectangle(cornerRadius: 12)
                  .stroke(Color("NeumorphicShadowDark").opacity(0.3), lineWidth: 1)
              }
              .onSubmit {
                isEditingTimes = false
              }
          } else {
            Button {
              isEditingTimes = true
            } label: {
              Text("\(viewModel?.timesPerDay ?? 1)")
                .font(.system(size: 24, weight: .semibold))
                .foregroundStyle(Color("TextPrimary"))
                .frame(width: 80, height: 48)
                .background(Color("BackgroundPrimary"))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(
                  color: Color("NeumorphicShadowDark").opacity(0.6),
                  radius: 6,
                  x: 3,
                  y: 3
                )
                .shadow(
                  color: Color("NeumorphicShadowLight").opacity(0.5),
                  radius: 6,
                  x: -3,
                  y: -3
                )
            }
            .buttonStyle(PlainButtonStyle())
          }

          Spacer()

          // Plus Button
          NeumorphicButton(icon: "plus", size: 40) {
            if (viewModel?.timesPerDay ?? 1) < 99 {
              viewModel?.setTimesPerDay((viewModel?.timesPerDay ?? 1) + 1)
            }
          }
        }

        Text("How many times you want to do this habit per day")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextMuted"))
          .multilineTextAlignment(.center)
      }
    }
  }

  // MARK: - Frequency Section
  private var frequencySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        Text("Frequency")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        // Frequency Options
        HStack(spacing: 8) {
          ForEach(FrequencyType.allCases, id: \.self) { frequency in
            FrequencyButton(
              frequency: frequency,
              isSelected: (viewModel?.selectedFrequency ?? .daily) == frequency
            ) {
              viewModel?.setSelectedFrequency(frequency)
            }
          }
        }

        // Custom Days Selection
        if (viewModel?.selectedFrequency ?? .daily) == .custom {
          customDaysSection
        }
      }
    }
  }

  private var customDaysSection: some View {
    let orderedDays = DayOfWeek.orderedCases(startingWithMonday: appearanceManager.weekStartsOnMonday)
    
    return VStack(alignment: .leading, spacing: 12) {
      Text("Select days of the week:")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextSecondary"))

      HStack(spacing: 0) {
        ForEach(orderedDays, id: \.self) { day in
          DayButton(
            day: day,
            isSelected: (viewModel?.customDays ?? Set()).contains(day)
          ) {
            var currentDays = viewModel?.customDays ?? Set()
            if currentDays.contains(day) {
              currentDays.remove(day)
            } else {
              currentDays.insert(day)
            }
            viewModel?.setCustomDays(currentDays)
          }
          .frame(maxWidth: .infinity)
        }
      }

      Text((viewModel?.customDays ?? Set()).isEmpty ? "No days selected" : "\((viewModel?.customDays ?? Set()).count) days selected")
        .font(.system(size: 12))
        .foregroundStyle(Color("TextMuted"))
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.selectedFrequency)
  }

  // MARK: - Reminder Section
  private var reminderSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        HStack {
          Text("Reminder Time")
            .font(.system(size: 18, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))

          Spacer()

          ToggleSwitch(isOn: Binding(
            get: { viewModel?.isReminderEnabled ?? false },
            set: { viewModel?.setIsReminderEnabled($0) }
          ))
        }

        if viewModel?.isReminderEnabled ?? false {
          reminderTimeSection
        } else {
          reminderDisabledSection
        }
      }
    }
  }

  private var reminderTimeSection: some View {
    VStack(spacing: 12) {
      // Existing times list
      if let times = viewModel?.notificationTimes, !times.isEmpty {
        ForEach(Array(times.enumerated()), id: \.offset) { index, time in
          HStack {
            HStack(spacing: 12) {
              Image(systemName: "clock")
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(Color("Info"))

              Text(time, style: .time)
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(Color("TextPrimary"))
            }

            Spacer()

            Button {
              viewModel?.removeNotificationTime(at: index)
            } label: {
              Image(systemName: "minus.circle.fill")
                .font(.system(size: 16))
                .foregroundStyle(Color("Warning"))
            }
          }
          .padding(12)
          .background(Color("BackgroundPrimary"))
          .clipShape(RoundedRectangle(cornerRadius: 12))
          .shadow(
            color: Color("NeumorphicShadowDark").opacity(0.4),
            radius: 3,
            x: 2,
            y: 2
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(0.3),
            radius: 3,
            x: -2,
            y: -2
          )
        }
      }
      
      // Add time button
      Button {
        showTimePicker = true
      } label: {
        HStack {
          HStack(spacing: 12) {
            Image(systemName: "plus.circle")
              .font(.system(size: 18, weight: .medium))
              .foregroundStyle(Color("Success"))

            Text(viewModel?.notificationTimes.isEmpty == true ? "Set reminder time" : "Add another time")
              .font(.system(size: 16, weight: .medium))
              .foregroundStyle(Color("TextPrimary"))
          }

          Spacer()

          Image(systemName: "chevron.right")
            .font(.system(size: 14, weight: .medium))
            .foregroundStyle(Color("TextSecondary"))
        }
        .padding(16)
        .background(Color("BackgroundPrimary"))
        .clipShape(RoundedRectangle(cornerRadius: 15))
        .shadow(
          color: Color("NeumorphicShadowDark").opacity(0.6),
          radius: 5,
          x: 3,
          y: 3
        )
        .shadow(
          color: Color("NeumorphicShadowLight").opacity(0.5),
          radius: 5,
          x: -3,
          y: -3
        )
      }
      .buttonStyle(PlainButtonStyle())

      Text("Tap to add notification times for this habit")
        .font(.system(size: 12))
        .foregroundStyle(Color("TextMuted"))
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.isReminderEnabled)
  }

  private var reminderDisabledSection: some View {
    VStack(spacing: 8) {
      Image(systemName: "bell.slash")
        .font(.system(size: 24))
        .foregroundStyle(Color("TextMuted"))

      Text("Reminders are disabled")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextMuted"))
    }
    .frame(maxWidth: .infinity)
    .padding(16)
    .background(Color("BackgroundPrimary"))
    .clipShape(RoundedRectangle(cornerRadius: 12))
    .overlay {
      RoundedRectangle(cornerRadius: 12)
        .stroke(Color("NeumorphicShadowDark").opacity(0.3), lineWidth: 1)
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.isReminderEnabled)
  }

  // MARK: - Update Button
  private var updateButton: some View {
    Button {
      updateHabit()
    } label: {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 16)
          .fill(canUpdateHabit ? Color("Success") : Color("BackgroundPrimary"))

        // Button content
        HStack(spacing: 8) {
          Image(systemName: "checkmark")
            .font(.system(size: 16, weight: .semibold))

          Text("Update Habit")
            .font(.system(size: 16, weight: .semibold))
        }
        .foregroundStyle(canUpdateHabit ? .white : Color("TextPrimary"))
      }
      .frame(maxWidth: .infinity)
      .frame(height: 128)
      .clipShape(RoundedRectangle(cornerRadius: 16))
      // Convex shadows when not clickable (matching FrequencyButton style)
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(canUpdateHabit ? 0 : 0.6),
        radius: canUpdateHabit ? 0 : 8,
        x: canUpdateHabit ? 0 : 4,
        y: canUpdateHabit ? 0 : 4
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(canUpdateHabit ? 0 : 0.5),
        radius: canUpdateHabit ? 0 : 8,
        x: canUpdateHabit ? 0 : -4,
        y: canUpdateHabit ? 0 : -4
      )
      // Green glow effect when clickable
      .shadow(
        color: canUpdateHabit ? Color("Success").opacity(0.4) : Color.clear,
        radius: 15,
        x: 0,
        y: 4
      )
    }
    .buttonStyle(PlainButtonStyle())
    .disabled(!canUpdateHabit)
    .animation(.easeInOut(duration: 0.2), value: canUpdateHabit)
  }

  // MARK: - Time Picker Modal
  private var timePickerModal: some View {
    ZStack {
      // Background overlay
      Color.black.opacity(0.3)
        .ignoresSafeArea()
        .onTapGesture {
          showTimePicker = false
        }

      VStack {
        Spacer()

        // Time picker content
        VStack(spacing: 24) {
          // Header
          HStack {
            Text("Set Reminder Time")
              .font(.system(size: 18, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))

            Spacer()

            NeumorphicButton(icon: "xmark", size: 32) {
              showTimePicker = false
            }
          }

          // Time picker
          DatePicker(
            "Reminder Time",
            selection: Binding(
              get: { viewModel?.reminderTime ?? Date() },
              set: { viewModel?.setReminderTime($0) }
            ),
            displayedComponents: .hourAndMinute
          )
          .datePickerStyle(.wheel)
          .labelsHidden()

          // Confirm button
          Button {
            // Add the time to notification times instead of just setting reminderTime
            viewModel?.addNotificationTime(viewModel?.reminderTime ?? Date())
            showTimePicker = false
          } label: {
            Text("Add Reminder Time")
              .font(.system(size: 16, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))
              .frame(maxWidth: .infinity)
              .frame(height: 44)
              .background(Color("BackgroundPrimary"))
              .clipShape(RoundedRectangle(cornerRadius: 16))
              .shadow(
                color: Color("NeumorphicShadowDark").opacity(0.6),
                radius: 5,
                x: 3,
                y: 3
              )
              .shadow(
                color: Color("NeumorphicShadowLight").opacity(0.5),
                radius: 5,
                x: -3,
                y: -3
              )
          }
          .buttonStyle(PlainButtonStyle())
        }
        .padding(24)
        .background(Color("BackgroundPrimary"))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
      }
    }
    .transition(.move(edge: .bottom).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: showTimePicker)
  }

  // MARK: - Actions
  private func updateHabit() {
    Task {
      let success = await viewModel?.updateHabit() ?? false
      if success {
        dismiss()
      }
    }
  }
  
  private func handleBackNavigation() {
    if viewModel?.hasUnsavedChanges == true {
      showUnsavedChangesAlert = true
    } else {
      dismiss()
    }
  }
}

#Preview {
    // Create a sample habit for preview
    let sampleHabit = Habit(
        name: "Morning Walk",
        category: .fitness,
        timesPerDay: 1,
        frequency: .daily
    )
    
    EditHabitView(habitId: sampleHabit.id)
}
