import SwiftData
import SwiftUI

struct DashboardView: View {
  @Environment(\.modelContext) private var modelContext
  @Environment(NotificationManager.self) private var notificationManager

  // Use @Query for real-time data observation
  @Query(
    filter: #Predicate<Habit> { !$0.isArchived },
    sort: [SortDescriptor(\.displayOrder), SortDescriptor(\.createdAt)]
  ) private var activeHabits: [Habit]

  @Environment(HabitRepository.self) private var repository
  
  @Binding var showingSettings: Bool
  @State private var showingHabitMenu: String? = nil
  @State private var showingCreateHabit = false
  @State private var showingEditHabit = false
  @State private var editingHabitId: UUID?
  @State private var showingCalendarRecords = false
  @State private var calendarHabit: Habit? = nil
  @State private var currentDate: Date = Date()
  @State private var showingArchiveConfirmation = false
  @State private var habitToArchive: Habit? = nil
  @FocusState private var isSearchFieldFocused: Bool
  
  // Search state - moved from viewModel
  @State private var searchText: String = ""
  @State private var isSearching: Bool = false
  @State private var scrollToTodayTrigger: Date = Date()
  @State private var errorMessage: String?
  
  private let lastOpenDateKey = "LastAppOpenDate"

  var body: some View {
    GeometryReader { geometry in
      ZStack {
        // Background
        Color("BackgroundPrimary")
          .ignoresSafeArea()

        VStack(spacing: 0) {
          // Header
          headerView
            .padding(.horizontal, 16)
            .padding(.top, 3)

          // Search bar (if searching)
          if isSearching {
            searchBarView
              .padding(.horizontal, 16)
              .padding(.bottom, 16)
              .transition(.move(edge: .top).combined(with: .opacity))
          }          // Main content
          ScrollViewReader { proxy in
            ScrollView {
              VStack(spacing: 12) {
                // Progress Overview Card
                progressCardView
                  .padding(.horizontal, 16)
                  .padding(.top, 8)

                // Today's Habits
                habitsListWithoutContainer
                  .padding(.horizontal, 16)
              }
              .padding(.bottom, 20)  // Bottom padding without tab bar
            }
          }

          Spacer()
        }
      }
      .onTapGesture {
        showingHabitMenu = nil
      }
      .sheet(isPresented: $showingCreateHabit) {
        CreateHabitView()
          .onDisappear {
            checkForNewDay()
          }
      }
      .sheet(isPresented: $showingEditHabit) {
        if let habitId = editingHabitId {
          EditHabitView(habitId: habitId)
            .onDisappear {
              checkForNewDay()
              editingHabitId = nil
            }
        }
      }
      .sheet(isPresented: $showingCalendarRecords) {
        if let calendarHabit = calendarHabit {
          CalendarRecordsView(habit: calendarHabit)
            .onDisappear {
              checkForNewDay()
              self.calendarHabit = nil
            }
        }
      }
      .navigationBarHidden(true)
      .onAppear {
        checkForNewDay()
        
        let calendar = Calendar.current
        let today = Date()
        if !calendar.isDate(currentDate, inSameDayAs: today) {
          currentDate = today
        }

        // Trigger scroll to today when dashboard appears
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
          triggerScrollToToday()
        }
      }
      .onReceive(
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
      ) { _ in
        // Check for new day when app comes to foreground
        checkForNewDay()

        let calendar = Calendar.current
        let today = Date()
        if !calendar.isDate(currentDate, inSameDayAs: today) {
          currentDate = today
        }
      }

      .onReceive(NotificationCenter.default.publisher(for: .habitRestored)) { _ in
        // SwiftData @Query will automatically refresh when habits are restored
        checkForNewDay()
      }
      .onChange(of: showingSettings) { oldValue, newValue in
        // When settings view is dismissed, trigger scroll to today
        if oldValue && !newValue {
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            triggerScrollToToday()
          }
        }
      }
      .archiveConfirmationDialog(
        isPresented: $showingArchiveConfirmation,
        habitName: habitToArchive?.name ?? "",
        onConfirm: {
          if let habit = habitToArchive {
            archiveHabit(habit)
            habitToArchive = nil
          }
        }
      )
    }
  }

  // MARK: - Header

  private var headerView: some View {
    ZStack {
      // Background HStack for buttons
      HStack {
        // Add button
        NeumorphicGreenButton(icon: "plus", size: 40) {
          showingCreateHabit = true
        }

        Spacer()

        // Search button
        NeumorphicButton(
          icon: "magnifyingglass",
          size: 40,
          isPressed: isSearching
        ) {
          withAnimation(.easeInOut(duration: 0.15)) {
            if isSearching {
              endSearch()
              isSearchFieldFocused = false
            } else {
              startSearch()
              DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                isSearchFieldFocused = true
              }
            }
          }
        }

        // Settings button
        NeumorphicButton(
          icon: "gearshape.fill",
          size: 40,
          isPressed: false
        ) {
          // Navigate to Settings with slide out to right animation
          withAnimation(.easeInOut(duration: 0.4)) {
            showingSettings = true
          }
        }
      }

      // Centered title and date
      VStack(spacing: 2) {
        Text("Today")
          .font(.system(size: 24, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text(currentDateString)
          .font(.system(size: 14))
          .foregroundStyle(Color("TextSecondary"))
      }
    }
    .padding(.bottom, 24)
  }

  // MARK: - Search Bar

  private var searchBarView: some View {
    HStack {
      NeumorphicCard(padding: 12, cornerRadius: 15) {
        HStack {
          Image(systemName: "magnifyingglass")
            .font(.system(size: 14))
            .foregroundStyle(Color("TextMuted"))

          TextField(
            "Search habits...",
            text: $searchText
          )
          .font(.system(size: 14))
          .foregroundStyle(Color("TextPrimary"))
          .focused($isSearchFieldFocused)

          if !searchText.isEmpty {
            Button {
              clearSearch()
            } label: {
              Image(systemName: "xmark.circle.fill")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextMuted"))
            }
            .buttonStyle(PlainButtonStyle())
          }

          Button {
            withAnimation(.easeInOut(duration: 0.15)) {
              endSearch()
              isSearchFieldFocused = false
            }
          } label: {
            Text("Cancel")
              .font(.system(size: 14))
              .foregroundStyle(Color("TextSecondary"))
          }
          .buttonStyle(PlainButtonStyle())
        }
      }
      .frame(height: 44)
    }
  }

  // MARK: - Progress Card

  private var progressCardView: some View {
    NeumorphicCard(padding: 12) {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text("Today's Achievements")
            .font(.system(size: 16, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))

          Text(progressCardSubtitle)
            .font(.system(size: 12))
            .foregroundStyle(Color("TextSecondary"))
        }

        Spacer()

        CircularProgressView(
          progress: progressPercentage,
          size: 48,
          lineWidth: 2.5
        )
      }
    }
  }

  // MARK: - Habits List

  // MARK: - Habits List Without Container

  private var habitsListWithoutContainer: some View {
    // Display habits without container - each card same width as progress card
    VStack(spacing: 12) {
      if displayedHabits.isEmpty && !isCurrentlySearching {
          NeumorphicCard(padding: 12) {
            VStack(spacing: 16) {
              Image(systemName: "plus.circle")
                .font(.system(size: 32))
                .foregroundStyle(Color("TextMuted"))

              Text("No habits yet")
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(Color("TextPrimary"))

              Text("Tap the + button to create your first habit")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextSecondary"))
                .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity, minHeight: 80)
          }
      } else if displayedHabits.isEmpty && isCurrentlySearching {
        NeumorphicCard(padding: 12) {
          VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
              .font(.system(size: 32))
              .foregroundStyle(Color("TextMuted"))

            Text("No results found")
              .font(.system(size: 16, weight: .medium))
              .foregroundStyle(Color("TextPrimary"))

            Text("Try searching with different keywords")
              .font(.system(size: 14))
              .foregroundStyle(Color("TextSecondary"))
              .multilineTextAlignment(.center)
          }
          .frame(maxWidth: .infinity, minHeight: 80)
        }
      } else {
          ForEach(Array(displayedHabits.enumerated()), id: \.element.id) { index, habit in
            HabitRowView(
              habit: habit,
              showingHabitMenu: $showingHabitMenu,
              scrollTrigger: scrollTriggerBinding,
              onToggle: toggleHabit,
              onEdit: editHabit,
              onEditRecords: editRecords,
              onArchive: archiveHabit
            )
            .zIndex(showingHabitMenu == habit.id.uuidString ? 1000 - Double(index) : 0)
          }
        }

      if let errorMessage = errorMessage {
        Text(errorMessage)
          .font(.system(size: 12))
          .foregroundStyle(Color("Warning"))
          .padding(.top, 8)
      }
    }
  }

  // MARK: - Helper Properties

  // Computed properties for search state
  private var isCurrentlySearching: Bool {
    isSearching
  }

  // Computed properties for habit management using @Query
  private var displayedHabits: [Habit] {
    if isSearching && !searchText.isEmpty {
      return activeHabits.filter { habit in
        habit.name.localizedCaseInsensitiveContains(searchText)
      }
    } else {
      return activeHabits
    }
  }

  private var progressPercentage: Double {
    // Only count habits that are scheduled for today
    let todaysHabits = displayedHabits.filter { $0.isScheduledForToday }
    guard !todaysHabits.isEmpty else { return 0 }
    let completedCount = todaysHabits.filter { $0.isCompletedToday }.count
    return Double(completedCount) / Double(todaysHabits.count)
  }

  private var currentDateString: String {
    let formatter = DateFormatter()
    formatter.dateFormat = "MMMM d, yyyy"
    return formatter.string(from: currentDate)
  }

  private var progressCardSubtitle: String {
    if isSearching {
      let count = displayedHabits.count
      return count == 1 ? "1 result" : "\(count) results"
    } else {
      let todaysCount = activeHabits.filter { $0.isScheduledForToday }.count
      return todaysCount == 1 ? "1 habit today" : "\(todaysCount) habits today"
    }
  }

  private var scrollTriggerBinding: Binding<Date?> {
    Binding(
      get: { scrollToTodayTrigger },
      set: { _ in }
    )
  }

  // MARK: - Actions
  
  private func checkForNewDay() {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    
    let lastOpenDate = UserDefaults.standard.object(forKey: lastOpenDateKey) as? Date
    
    // If this is the first time opening the app, or it's a new day
    if let lastDate = lastOpenDate {
      if !calendar.isDate(lastDate, inSameDayAs: today) {
        // It's a new day - trigger scroll to today
        triggerScrollToToday()
      }
    }
    
    // Update the last open date to today
    UserDefaults.standard.set(today, forKey: lastOpenDateKey)
  }
  
  private func triggerScrollToToday() {
    scrollToTodayTrigger = Date()
  }
  
  // Search functions
  private func startSearch() {
    isSearching = true
  }
  
  private func endSearch() {
    isSearching = false
    searchText = ""
  }
  
  private func clearSearch() {
    searchText = ""
  }

  private func toggleHabit(_ habit: Habit) {
    do {
      try repository.toggleHabitCompletion(habit)
      // SwiftData @Model objects automatically update - no need to reload
    } catch {
      errorMessage = "Failed to update habit: \(error.localizedDescription)"
    }
  }

  private func editHabit(_ habit: Habit) {
    editingHabitId = habit.id
    showingEditHabit = true
  }

  private func editRecords(_ habit: Habit) {
    calendarHabit = habit
    showingCalendarRecords = true
  }

  private func archiveHabit(_ habit: Habit) {
    do {
      try repository.archiveHabit(habit)
      errorMessage = nil
    } catch {
      errorMessage = "Failed to archive habit: \(error.localizedDescription)"
    }
  }


}


// MARK: - Custom Container for Habits List

struct HabitsListContainer<Content: View>: View {
  let content: Content
  let padding: CGFloat = 16
  let cornerRadius: CGFloat = 20

  init(@ViewBuilder content: () -> Content) {
    self.content = content()
  }

  var body: some View {
    ZStack {
      // Background with neumorphic effect (clipped)
      RoundedRectangle(cornerRadius: cornerRadius)
        .fill(Color("BackgroundPrimary"))
        .shadow(
          color: Color("NeumorphicShadowDark").opacity(0.8),
          radius: max(12, cornerRadius * 0.6),
          x: max(8, cornerRadius * 0.4),
          y: max(8, cornerRadius * 0.4)
        )
        .shadow(
          color: Color("NeumorphicShadowLight").opacity(0.7),
          radius: max(12, cornerRadius * 0.6),
          x: -max(8, cornerRadius * 0.4),
          y: -max(8, cornerRadius * 0.4)
        )

      // Content container (NOT clipped to allow dropdowns to overflow)
      content
        .padding(padding)
    }
  }
}

#Preview {
  DashboardView(showingSettings: .constant(false))
    .modelContainer(for: [Habit.self, HabitRecord.self], inMemory: true)
}
