import SwiftUI

extension Notification.Name {
  static let homeTabTapped = Notification.Name("homeTabTapped")
  static let habitRestored = Notification.Name("habitRestored")
  static let habitCompletedFromNotification = Notification.Name("habitCompletedFromNotification")
}

struct TabBarView: View {
  @Binding var selectedTab: String

  var body: some View {
    HStack {
      Spacer()

      tabItem(icon: "house.fill", title: "Home", isActive: selectedTab == "home") {
        selectedTab = "home"
        NotificationCenter.default.post(name: .homeTabTapped, object: nil)
      }

      Spacer()

      tabItem(
        icon: "chart.line.uptrend.xyaxis", title: "Progress", isActive: selectedTab == "progress"
      ) {
        selectedTab = "progress"
      }

      Spacer()
    }
    .padding(.horizontal, 10)
    .padding(.top, 5)
    .padding(.bottom, -25)
    .background {
      Rectangle()
        .fill(Color("BackgroundPrimary"))
        .ignoresSafeArea(.container, edges: .bottom)
    }
  }

  private func tabItem(
    icon: String,
    title: String,
    isActive: Bool,
    action: @escaping () -> Void
  ) -> some View {
    Button(action: action) {
      VStack(spacing: 4) {
        Image(systemName: icon)
          .font(.system(size: 18))
          .foregroundStyle(isActive ? Color("Success") : Color("TextMuted"))

        Text(title)
          .font(.system(size: 12))
          .foregroundStyle(isActive ? Color("Success") : Color("TextMuted"))
      }
      .padding(.vertical, 8)
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(0.95)
    .animation(.easeInOut(duration: 0.2), value: isActive)
  }
}

#Preview {
  TabBarView(selectedTab: .constant("home"))
    .padding(.bottom, 34)  // For preview safe area
}
