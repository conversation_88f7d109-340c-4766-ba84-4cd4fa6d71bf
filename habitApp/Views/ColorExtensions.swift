import SwiftUI

// MARK: - Custom Color Extensions (not auto-generated)
extension Color {
  // MARK: - Habit Level Color Extensions
  // Transparency-based habit level colors
  static func habitLevel(_ level: Int, baseColor: Color) -> Color {
    switch level {
    case 0: return Color.backgroundPrimary
    case 1: return baseColor.opacity(0.2)
    case 2: return baseColor.opacity(0.4)
    case 3: return baseColor.opacity(0.6)
    case 4: return baseColor.opacity(0.8)
    case 5: return baseColor
    default: return Color.backgroundPrimary
    }
  }

  // Legacy habit level colors (deprecated - use habitLevel(_:baseColor:) instead)
  static let habitLevel0 = Color.backgroundPrimary
  static let habitLevel1 = Color.backgroundPrimary
  static let habitLevel2 = Color(red: 0.78, green: 0.89, blue: 0.55)  // #c6e48b
  static let habitLevel3 = Color(red: 0.48, green: 0.79, blue: 0.44)  // #7bc96f
  static let habitLevel4 = Color(red: 0.18, green: 0.35, blue: 0.22)  // #2d5a37

  // Semantic color aliases for habit tracking
  static let habitSuccess = Color.success
  static let habitWarning = Color.warning
  static let habitInfo = Color.info
  static let habitWellness = Color.wellness
  static let habitMindfulness = Color.mindfulness
  
  // MARK: - Hex Color Support
  /// Initialize a Color from a hex string (e.g., "#FF6B6B") or color asset name
  static func fromHexOrAsset(_ value: String) -> Color {
    if value.hasPrefix("#") {
      return Color(hex: value) ?? Color("Success")
    } else {
      return Color(value)
    }
  }
}
