import SwiftData
import SwiftUI

struct SettingsView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(AppearanceManager.self) private var appearanceManager
  @Environment(NotificationManager.self) private var notificationManager

  @Binding var showingSettings: Bool

  // Use @Query to directly observe active habits
  @Query(
    filter: #Predicate<Habit> { !$0.isArchived },
    sort: [SortDescriptor(\.displayOrder), SortDescriptor(\.createdAt)]
  ) private var activeHabits: [Habit]

  @State private var iCloudSync = true
  @State private var dailyReminders = true
  @State private var streaks = true
  @State private var showFrequencyLabels = false
  @State private var smartTips = true
  @State private var growthInsights = true
  @State private var motivationalQuotes = false
  @State private var showingAppearanceModal = false
  @State private var showingReorderModal = false

  // Single repository instance to avoid context issues
  @State private var habitRepository: HabitRepository?
  @State private var isInitialized = false

  // Add initializer to accept showingSettings binding
  init(
    showingSettings: Binding<Bool> = .constant(false)
  ) {
    self._showingSettings = showingSettings
  }

  var body: some View {
    ScrollView {
      VStack(spacing: 24) {
        // Custom Header (in case navigation bar doesn't show)
        headerView
          .padding(.horizontal, 16)
          .padding(.top, 8)

        // Privacy First Banner
        PrivacyBannerView()

        // Appearance Section
        AppearanceSection(
          showingAppearanceModal: $showingAppearanceModal,
          showingReorderModal: $showingReorderModal,
          currentAppearanceMode: appearanceManager.appearanceMode,
          appearanceManager: appearanceManager
        )

        // Notifications Section
        NotificationsSection(
          dailyReminders: $dailyReminders,
          streaks: $streaks,
          notificationManager: notificationManager
        )

        // Data & Sync Section
        DataSyncSection(iCloudSync: $iCloudSync)

        // App Preferences Section
        AppPreferencesSection(
          smartTips: $smartTips,
          growthInsights: $growthInsights,
          motivationalQuotes: $motivationalQuotes
        )

        // Support & Info Section
        SupportInfoSection()

        // App Version Section
        AppVersionSection()

        Spacer(minLength: 100)  // Space for tab bar
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
    }
    .background(Color("BackgroundPrimary"))
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      // Move heavy operations to background for instant UI response
      Task {
        await initializeSettingsAsync()
      }
    }
    .navigationDestination(isPresented: $showingAppearanceModal) {
      AppearanceModeModal()
        .environment(appearanceManager)
    }
    .navigationDestination(isPresented: $showingReorderModal) {
      HabitReorderSheet(
        habits: isInitialized ? activeHabits : [],
        onReorder: handleHabitReorder
      )
    }
    .toolbar {
      ToolbarItem(placement: .navigationBarLeading) {
        NeumorphicButton(icon: "arrow.left", size: 40) {
          // Navigate back to dashboard with slide out to left animation
          withAnimation(.easeInOut(duration: 0.4)) {
            showingSettings = false
          }
        }
      }

      ToolbarItem(placement: .principal) {
        VStack(spacing: 2) {
          Text("Settings")
            .font(.system(size: 24, weight: .bold, design: .rounded))
            .foregroundColor(Color("TextPrimary"))
          Text("Privacy & Preferences")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(Color("TextSecondary"))
        }
      }

      ToolbarItem(placement: .navigationBarTrailing) {
        NeumorphicButton(icon: "info.circle", size: 40) {
          // Info action
        }
      }
    }
  }

  // MARK: - Header

  private var headerView: some View {
    HStack {
      // Back button
      NeumorphicButton(icon: "arrow.left", size: 40) {
        // Navigate back to dashboard with slide out to left animation
        withAnimation(.easeInOut(duration: 0.4)) {
          showingSettings = false
        }
      }

      Spacer()

      // Title
      VStack(spacing: 2) {
        Text("Settings")
          .font(.system(size: 24, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text("Privacy & Preferences")
          .font(.system(size: 14))
          .foregroundStyle(Color("TextSecondary"))
      }

      Spacer()

      // Info button
      NeumorphicButton(icon: "info.circle", size: 40) {
        // Info action
      }
    }
    .padding(.bottom, 16)
  }

  // MARK: - Helper Methods for Habit Reordering

  private func handleHabitReorder(_ orderedHabits: [Habit]) {
    guard let repository = habitRepository, isInitialized else { return }
    
    Task { @MainActor in
      do {
        try repository.reorderHabits(orderedHabits)
      } catch {
        print("Error reordering habits: \(error)")
      }
    }
  }

  // MARK: - Async Initialization

  @MainActor
  private func initializeSettingsAsync() async {
    guard !isInitialized else { return }
    
    // Initialize repository on main actor since it's required
    let repository = HabitRepository(
      context: modelContext, 
      notificationManager: notificationManager
    )
    
    // Update UI immediately
    self.habitRepository = repository
    self.configureNavigationBarAppearance()
    self.isInitialized = true
  }

  private func configureNavigationBarAppearance() {
    let appearance = UINavigationBarAppearance()
    appearance.configureWithOpaqueBackground()
    appearance.backgroundColor = UIColor(named: "BackgroundPrimary")
    appearance.shadowColor = .clear

    UINavigationBar.appearance().standardAppearance = appearance
    UINavigationBar.appearance().scrollEdgeAppearance = appearance
    UINavigationBar.appearance().compactAppearance = appearance
  }
}

// MARK: - Privacy Banner
struct PrivacyBannerView: View {
  var body: some View {
    HStack(spacing: 12) {
      // Privacy Icon
      ZStack {
        Circle()
          .fill(Color.white.opacity(0.2))
          .frame(width: 40, height: 40)

        Image(systemName: "shield.fill")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.white)
      }

      VStack(alignment: .leading, spacing: 4) {
        Text("Privacy First")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.white)

        Text("No accounts, no tracking")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.white.opacity(0.9))

        Text("Your data stays on your device. Optional iCloud sync for backup only.")
          .font(.system(size: 11, weight: .regular))
          .foregroundColor(.white.opacity(0.8))
          .lineLimit(2)
      }

      Spacer()
    }
    .padding(16)
    .background(
      LinearGradient(
        gradient: Gradient(colors: [Color("Success"), Color("Wellness")]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
      )
    )
    .clipShape(RoundedRectangle(cornerRadius: 20))
    .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 6, x: 6, y: 6)
    .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 6, x: -6, y: -6)
  }
}

// MARK: - Data & Sync Section
struct DataSyncSection: View {
  @Binding var iCloudSync: Bool
  @State private var showingArchiveView = false

  var body: some View {
    NeumorphicCard {
      VStack(alignment: .leading, spacing: 16) {
        Text("Data & Sync")
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        VStack(spacing: 12) {
          SettingRow(
            icon: "icloud.fill",
            iconColor: Color("Info"),
            title: "iCloud Sync",
            subtitle: "Sync habits across devices",
            isToggle: true,
            toggleValue: $iCloudSync
          )

          SettingRow(
            icon: "square.and.arrow.down",
            iconColor: Color("Success"),
            title: "Import Data",
            subtitle: "Import habits from backup",
            showChevron: true
          ) {
            // Import action
          }

          SettingRow(
            icon: "square.and.arrow.up",
            iconColor: Color("Warning"),
            title: "Export Data",
            subtitle: "Export habits for backup",
            showChevron: true
          ) {
            // Export action
          }

          SettingRow(
            icon: "archivebox.fill",
            iconColor: Color("Mindfulness"),
            title: "Archives",
            subtitle: "View archived habits",
            showChevron: true
          ) {
            showingArchiveView = true
          }
        }
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 8)
    }
    .navigationDestination(isPresented: $showingArchiveView) {
      ArchiveView()
    }
  }
}

// MARK: - Notifications Section
struct NotificationsSection: View {
  @Binding var dailyReminders: Bool
  @Binding var streaks: Bool
  let notificationManager: NotificationManager

  var body: some View {
    NeumorphicCard {
      VStack(alignment: .leading, spacing: 16) {
        Text("Notifications")
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        VStack(spacing: 12) {
          // Notification permission status
          if !notificationManager.isAuthorized
            && notificationManager.authorizationStatus != .notDetermined
          {
            SettingRow(
              icon: "exclamationmark.triangle.fill",
              iconColor: Color("Warning"),
              title: "Permission Required",
              subtitle: "Tap to enable in Settings",
              showChevron: true
            ) {
              notificationManager.openNotificationSettings()
            }
          }

          SettingRow(
            icon: "bell.fill",
            iconColor: Color("Warning"),
            title: "Daily Reminders",
            subtitle: getNotificationSubtitle(),
            isToggle: true,
            toggleValue: Binding(
              get: { notificationManager.isAuthorized && dailyReminders },
              set: { newValue in
                if newValue && !notificationManager.isAuthorized {
                  Task {
                    await notificationManager.requestAuthorization()
                  }
                }
                dailyReminders = newValue
              }
            )
          )

          SettingRow(
            icon: "flame.fill",
            iconColor: Color("Success"),
            title: "Streaks",
            subtitle: "Celebrate streak milestones",
            isToggle: true,
            toggleValue: $streaks
          )

          // Debug: Test Notification Button
          if notificationManager.isAuthorized {
            SettingRow(
              icon: "paperplane.fill",
              iconColor: Color("Info"),
              title: "Test Notification",
              subtitle: "Send test notification in 5 seconds",
              showChevron: true
            ) {
              Task {
                await notificationManager.scheduleTestNotification()
              }
            }
          }
        }
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 8)
    }
  }

  // MARK: - Helper Functions

  private func getNotificationSubtitle() -> String {
    switch notificationManager.authorizationStatus {
    case .authorized:
      return "Gentle habit reminders"
    case .denied:
      return "Permission denied"
    case .notDetermined:
      return "Tap to enable notifications"
    case .provisional:
      return "Quiet notifications enabled"
    case .ephemeral:
      return "Limited notifications"
    @unknown default:
      return "Check notification settings"
    }
  }
}

// MARK: - Appearance Section
struct AppearanceSection: View {
  @Binding var showingAppearanceModal: Bool
  @Binding var showingReorderModal: Bool
  let currentAppearanceMode: AppearanceMode
  let appearanceManager: AppearanceManager

  var body: some View {
    NeumorphicCard {
      VStack(alignment: .leading, spacing: 16) {
        Text("Appearance")
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        VStack(spacing: 12) {
          GridSizeSelector(
            selectedSize: Binding(
              get: { appearanceManager.gridSize },
              set: { appearanceManager.gridSize = $0 }
            )
          )

          SettingRow(
            icon: currentAppearanceMode.icon,
            iconColor: appearanceModeColor,
            title: "Display Mode",
            subtitle: currentAppearanceMode == .system ? "System appearance" : currentAppearanceMode.displayName,
            showChevron: true
          ) {
            showingAppearanceModal = true
          }

          SettingRow(
            icon: "paintpalette.fill",
            iconColor: Color("Warning"),
            title: "Habit Color",
            subtitle: "Show habit category colors",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.useGridColor },
              set: { appearanceManager.useGridColor = $0 }
            )
          )

          SettingRow(
            icon: "calendar.badge.clock",
            iconColor: Color("Info"),
            title: "Weekday Labels",
            subtitle: "Show schedules on day labels",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.showFrequencyLabels },
              set: { appearanceManager.showFrequencyLabels = $0 }
            )
          )

          SettingRow(
            icon: "flag.fill",
            iconColor: Color("Success"),
            title: "Start day Mark",
            subtitle: "Show icon on start date",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.showStartMark },
              set: { appearanceManager.showStartMark = $0 }
            )
          )

          SettingRow(
            icon: "calendar.circle.fill",
            iconColor: Color("Warning"),
            title: "Month Markers",
            subtitle: "Show dots on 1st day of month",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.showMonthMarkers },
              set: { appearanceManager.showMonthMarkers = $0 }
            )
          )

          SettingRow(
            icon: "calendar.badge.exclamationmark",
            iconColor: Color("Success"),
            title: "Highlight Today",
            subtitle: "Show highlight border on today",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.showTodayHighlight },
              set: { appearanceManager.showTodayHighlight = $0 }
            )
          )

          SettingRow(
            icon: "calendar.badge.minus",
            iconColor: Color("Info"),
            title: "Monday First",
            subtitle: "Start week on Monday",
            isToggle: true,
            toggleValue: Binding(
              get: { appearanceManager.weekStartsOnMonday },
              set: { appearanceManager.weekStartsOnMonday = $0 }
            )
          )

          SettingRow(
            icon: "line.3.horizontal",
            iconColor: Color("Warning"),
            title: "Reorder Habits",
            subtitle: "Change habit display order",
            showChevron: true
          ) {
            showingReorderModal = true
          }
        }
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 12)
    }
  }

  private var appearanceModeColor: Color {
    switch currentAppearanceMode {
    case .system:
      return Color("Mindfulness")
    case .light:
      return Color("Warning")
    case .dark:
      return Color("Info")
    }
  }
}

// MARK: - App Preferences Section
struct AppPreferencesSection: View {
  @Binding var smartTips: Bool
  @Binding var growthInsights: Bool
  @Binding var motivationalQuotes: Bool

  var body: some View {
    NeumorphicCard {
      VStack(alignment: .leading, spacing: 16) {
        Text("App Preferences")
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        VStack(spacing: 12) {
          SettingRow(
            icon: "lightbulb.fill",
            iconColor: Color("Info"),
            title: "Smart Tips",
            subtitle: "Helpful habit suggestions",
            isToggle: true,
            toggleValue: $smartTips
          )

          SettingRow(
            icon: "leaf.fill",
            iconColor: Color("Success"),
            title: "Growth Insights",
            subtitle: "Weekly progress analysis",
            isToggle: true,
            toggleValue: $growthInsights
          )

          SettingRow(
            icon: "heart.fill",
            iconColor: Color("Warning"),
            title: "Motivational Quotes",
            subtitle: "Daily inspiration",
            isToggle: true,
            toggleValue: $motivationalQuotes
          )
        }
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 8)
    }
  }
}

// MARK: - Support & Info Section
struct SupportInfoSection: View {
  var body: some View {
    NeumorphicCard {
      VStack(alignment: .leading, spacing: 16) {
        Text("Support & Info")
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        VStack(spacing: 12) {
          SupportInfoRow(
            icon: "questionmark.circle.fill",
            iconColor: Color("Info"),
            title: "Help & FAQ"
          ) {
            // Help action
          }

          SupportInfoRow(
            icon: "envelope.fill",
            iconColor: Color("Wellness"),
            title: "Contact Support"
          ) {
            // Contact action
          }

          SupportInfoRow(
            icon: "star.fill",
            iconColor: Color("Warning"),
            title: "Rate HabitApp"
          ) {
            // Rate action
          }

          SupportInfoRow(
            icon: "square.and.arrow.up",
            iconColor: Color("Success"),
            title: "Share with Friends"
          ) {
            // Share action
          }

          SupportInfoRow(
            icon: "shield.fill",
            iconColor: Color("Mindfulness"),
            title: "Privacy Policy"
          ) {
            // Privacy policy action
          }
        }
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 8)
    }
  }
}

// MARK: - App Version Section
struct AppVersionSection: View {
  var body: some View {
    NeumorphicCard {
      VStack(spacing: 12) {
        NeumorphicButton(icon: "leaf.fill", size: 64) {
          // App icon action if needed
        }

        Text("HabitApp")
          .font(.system(size: 16, weight: .semibold, design: .rounded))
          .foregroundColor(Color("TextPrimary"))

        Text("Version 1.0.0")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(Color("TextSecondary"))

        Text("Built with ❤️ for sustainable habits")
          .font(.system(size: 10, weight: .regular))
          .foregroundColor(Color("TextMuted"))
          .multilineTextAlignment(.center)
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 8)
    }
  }
}

// MARK: - Setting Row Component
struct SettingRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let subtitle: String
  var isToggle: Bool = false
  @Binding var toggleValue: Bool
  var showChevron: Bool = false
  var action: (() -> Void)?

  init(
    icon: String,
    iconColor: Color,
    title: String,
    subtitle: String,
    isToggle: Bool = false,
    toggleValue: Binding<Bool> = .constant(false),
    showChevron: Bool = false,
    action: (() -> Void)? = nil
  ) {
    self.icon = icon
    self.iconColor = iconColor
    self.title = title
    self.subtitle = subtitle
    self.isToggle = isToggle
    self._toggleValue = toggleValue
    self.showChevron = showChevron
    self.action = action
  }

  var body: some View {
    Button(action: {
      if !isToggle {
        action?()
      }
    }) {
      HStack(spacing: 12) {
        // Icon
        ZStack {
          RoundedRectangle(cornerRadius: 8)
            .fill(Color("BackgroundPrimary"))
            .frame(width: 32, height: 32)
            .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 2, x: 2, y: 2)
            .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 2, x: -2, y: -2)

          Image(systemName: icon)
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(iconColor)
        }

        // Title and Subtitle
        VStack(alignment: .leading, spacing: 2) {
          Text(title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(Color("TextPrimary"))

          Text(subtitle)
            .font(.system(size: 11, weight: .regular))
            .foregroundColor(Color("TextSecondary"))
        }

        Spacer()

        // Toggle or Chevron
        if isToggle {
          NeumorphicToggle(isOn: $toggleValue)
        } else if showChevron {
          Image(systemName: "chevron.right")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(Color("TextSecondary"))
        }
      }
      .padding(.vertical, 12)
      .padding(.horizontal, 12)
      .background(
        RoundedRectangle(cornerRadius: 15)
          .fill(Color("BackgroundPrimary"))
          .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 5, x: 5, y: 5)
          .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 5, x: -5, y: -5)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Support Info Row Component
struct SupportInfoRow: View {
  let icon: String
  let iconColor: Color
  let title: String
  let action: (() -> Void)?

  var body: some View {
    Button(action: {
      action?()
    }) {
      HStack(spacing: 12) {
        Image(systemName: icon)
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(iconColor)
          .frame(width: 24)

        Text(title)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(Color("TextPrimary"))

        Spacer()

        Image(systemName: "chevron.right")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(Color("TextSecondary"))
      }
      .padding(.vertical, 16)
      .padding(.horizontal, 4)
      .background(
        RoundedRectangle(cornerRadius: 15)
          .fill(Color("BackgroundPrimary"))
          .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 5, x: 5, y: 5)
          .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 5, x: -5, y: -5)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Neumorphic Toggle Component
struct NeumorphicToggle: View {
  @Binding var isOn: Bool

  var body: some View {
    Button(action: {
      withAnimation(.easeInOut(duration: 0.3)) {
        isOn.toggle()
      }
    }) {
      ZStack {
        // Background Track
        RoundedRectangle(cornerRadius: 12)
          .frame(width: 48, height: 24)
          .foregroundColor(isOn ? .clear : Color("BackgroundPrimary"))
          .background(
            Group {
              if isOn {
                LinearGradient(
                  gradient: Gradient(colors: [Color("Success"), Color("Wellness")]),
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 3, x: 3, y: 3)
                .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 3, x: -3, y: -3)
              } else {
                RoundedRectangle(cornerRadius: 12)
                  .fill(Color("BackgroundPrimary"))
                  .shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 0)
                  .shadow(color: .white.opacity(0.5), radius: 4, x: 0, y: 0)
                  .overlay(
                    RoundedRectangle(cornerRadius: 12)
                      .stroke(Color.black.opacity(0.1), lineWidth: 0.5)
                      .blur(radius: 0.5)
                      .offset(x: 1, y: 1)
                  )
                  .overlay(
                    RoundedRectangle(cornerRadius: 12)
                      .stroke(Color.white.opacity(0.3), lineWidth: 0.5)
                      .blur(radius: 0.5)
                      .offset(x: -1, y: -1)
                  )
              }
            }
          )

        // Knob
        Circle()
          .frame(width: 20, height: 20)
          .foregroundColor(isOn ? .white : Color("BackgroundPrimary"))
          .shadow(
            color: isOn ? Color.black.opacity(0.3) : Color("NeumorphicShadowDark").opacity(0.6),
            radius: isOn ? 1 : 2, x: isOn ? 1 : 2, y: isOn ? 1 : 2
          )
          .shadow(
            color: isOn ? .clear : Color("NeumorphicShadowLight").opacity(0.5),
            radius: isOn ? 0 : 2, x: isOn ? 0 : -2, y: isOn ? 0 : -2
          )
          .offset(x: isOn ? 12 : -12)
      }
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Grid Size Selector Component
struct GridSizeSelector: View {
  @Binding var selectedSize: GridSize
  
  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack(spacing: 12) {
        // Icon
        ZStack {
          RoundedRectangle(cornerRadius: 8)
            .fill(Color("BackgroundPrimary"))
            .frame(width: 32, height: 32)
            .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 2, x: 2, y: 2)
            .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 2, x: -2, y: -2)

          Image(systemName: "grid")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(Color("Wellness"))
        }

        // Title and Subtitle
        VStack(alignment: .leading, spacing: 2) {
          Text("Grid Size")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(Color("TextPrimary"))

          Text("Choose contribution square size")
            .font(.system(size: 11, weight: .regular))
            .foregroundColor(Color("TextSecondary"))
        }

        Spacer()
      }
      
      // Tab View for Size Selection
      HStack(spacing: 8) {
        ForEach(GridSize.allCases, id: \.self) { size in
          Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
              selectedSize = size
            }
          }) {
            ZStack {
              // Base background
              RoundedRectangle(cornerRadius: 15)
                .fill(Color("BackgroundPrimary"))

              // Concave inner shadows when selected
              if selectedSize == size {
                concaveInnerShadows
              }

              VStack(spacing: 8) {
                // Visual representation of grid size
                HStack(spacing: 1) {
                  ForEach(0..<3, id: \.self) { _ in
                    RoundedRectangle(cornerRadius: size.cornerRadius)
                      .fill(selectedSize == size ? Color("Success") : Color("TextMuted"))
                      .frame(width: size.cellSize * 0.5, height: size.cellSize * 0.5)
                      .opacity(selectedSize == size ? 1.0 : 0.4)
                  }
                }
                
                Text(size.displayName)
                  .font(.system(size: 12, weight: selectedSize == size ? .semibold : .medium))
                  .foregroundColor(selectedSize == size ? Color("TextPrimary") : Color("TextSecondary"))
              }
              .frame(maxWidth: .infinity)
              .padding(.vertical, 12)
            }
            .clipShape(RoundedRectangle(cornerRadius: 15))
            .shadow(
              color: Color("NeumorphicShadowDark").opacity(selectedSize == size ? 0 : 0.6),
              radius: 3,
              x: selectedSize == size ? 0 : 2,
              y: selectedSize == size ? 0 : 2
            )
            .shadow(
              color: Color("NeumorphicShadowLight").opacity(selectedSize == size ? 0 : 0.5),
              radius: 3,
              x: selectedSize == size ? 0 : -2,
              y: selectedSize == size ? 0 : -2
            )
          }
          .buttonStyle(PlainButtonStyle())
          .animation(.easeInOut(duration: 0.15), value: selectedSize == size)
        }
      }
    }
    .padding(.vertical, 12)
    .padding(.horizontal, 12)
    .background(
      RoundedRectangle(cornerRadius: 15)
        .fill(Color("BackgroundPrimary"))
        .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 5, x: 5, y: 5)
        .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 5, x: -5, y: -5)
    )
  }
  
  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 12)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 12)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 12)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 12)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))
    }
    .allowsHitTesting(false)
  }
}

#Preview {
  SettingsView()
    .environment(AppearanceManager())
}
