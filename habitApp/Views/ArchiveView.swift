import SwiftData
import SwiftUI

struct ArchiveView: View {
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dismiss) private var dismiss
  @Environment(NotificationManager.self) private var notificationManager
  @State private var viewModel: ArchiveViewModel?
  @State private var showingHabitMenu: String? = nil
  @State private var showingDeleteConfirmation = false
  @State private var habitToDelete: Habit? = nil

  var body: some View {
    GeometryReader { geometry in
      ZStack {
        // Background
        Color("BackgroundPrimary")
          .ignoresSafeArea()

        VStack(spacing: 0) {
          // Header
          headerView
            .padding(.horizontal, 16)
            .padding(.top, 3)

          // Search bar (if searching)
          if viewModel?.isSearching == true {
            searchBarView
              .padding(.horizontal, 16)
              .padding(.bottom, 16)
              .transition(.move(edge: .top).combined(with: .opacity))
          }

          // Main content
          ScrollView {
            VStack(spacing: 12) {
              archivedHabitsListView
                .padding(.horizontal, 16)
                .padding(.top, 8)
            }
            .padding(.bottom, 100)  // Account for tab bar
          }

          Spacer()
        }
      }
      .onTapGesture {
        showingHabitMenu = nil
      }
      .navigationBarHidden(true)
      .onAppear {
        if viewModel == nil {
          viewModel = ArchiveViewModel(
            context: modelContext, notificationManager: notificationManager)
        }
        viewModel?.loadArchivedHabits()
      }
      .alert("Delete Habit", isPresented: $showingDeleteConfirmation) {
        Button("Cancel", role: .cancel) {
          habitToDelete = nil
        }
        Button("Delete", role: .destructive) {
          if let habit = habitToDelete {
            viewModel?.permanentlyDeleteHabit(habit)
            habitToDelete = nil
          }
        }
      } message: {
        if let habit = habitToDelete {
          Text(
            "Are you sure you want to permanently delete \"\(habit.name)\"? This action cannot be undone."
          )
        }
      }
    }
  }

  // MARK: - Header

  private var headerView: some View {
    HStack {
      // Back button
      NeumorphicButton(
        icon: "chevron.left",
        size: 40,
        isPressed: false
      ) {
        dismiss()
      }

      Spacer()

      // Title
      VStack(spacing: 2) {
        Text("Archive")
          .font(.system(size: 24, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text("Archived habits")
          .font(.system(size: 14))
          .foregroundStyle(Color("TextSecondary"))
      }

      Spacer()

      // Search button
      NeumorphicButton(
        icon: "magnifyingglass",
        size: 40,
        isPressed: viewModel?.isSearching == true
      ) {
        withAnimation(.easeInOut(duration: 0.3)) {
          if viewModel?.isSearching == true {
            viewModel?.endSearch()
          } else {
            viewModel?.startSearch()
          }
        }
      }
    }
    .padding(.bottom, 24)
  }

  // MARK: - Search Bar

  private var searchBarView: some View {
    HStack {
      NeumorphicCard(padding: 12, cornerRadius: 15) {
        HStack {
          Image(systemName: "magnifyingglass")
            .font(.system(size: 14))
            .foregroundStyle(Color("TextMuted"))

          TextField(
            "Search archived habits...",
            text: Binding(
              get: { viewModel?.searchText ?? "" },
              set: { viewModel?.searchText = $0 }
            )
          )
          .font(.system(size: 14))
          .foregroundStyle(Color("TextPrimary"))

          if !(viewModel?.searchText.isEmpty ?? true) {
            Button {
              viewModel?.clearSearch()
            } label: {
              Image(systemName: "xmark.circle.fill")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextMuted"))
            }
            .buttonStyle(PlainButtonStyle())
          }
        }
      }
      .frame(height: 44)
    }
  }

  // MARK: - Archived Habits List

  private var archivedHabitsListView: some View {
    NeumorphicCard(padding: 16) {
      VStack(spacing: 12) {
        if let viewModel = viewModel {
          if viewModel.isLoading {
            VStack {
              ProgressView()
              Text("Loading archived habits...")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextSecondary"))
            }
            .frame(maxWidth: .infinity, minHeight: 100)
          } else if !viewModel.hasArchivedHabits {
            // No archived habits
            emptyStateView
          } else if !viewModel.hasSearchResults && viewModel.isSearching {
            // No search results
            noSearchResultsView
          } else {
            // Show filtered habits
            ForEach(Array(viewModel.filteredHabits.enumerated()), id: \.element.id) {
              index, habit in
              ArchivedHabitRowView(
                habit: habit,
                showingHabitMenu: $showingHabitMenu,
                onRestore: {
                  viewModel.restoreHabit(habit)
                },
                onDelete: {
                  habitToDelete = habit
                  showingDeleteConfirmation = true
                }
              )
              .zIndex(showingHabitMenu == habit.id.uuidString ? 1000 - Double(index) : 0)
            }
          }

          if let errorMessage = viewModel.errorMessage {
            Text(errorMessage)
              .font(.system(size: 12))
              .foregroundStyle(Color("Warning"))
              .padding(.top, 8)
          }
        }
      }
    }
  }

  // MARK: - Empty States

  private var emptyStateView: some View {
    VStack(spacing: 16) {
      NeumorphicButton(icon: "archivebox", size: 64) {
        // No action for empty state icon
      }
      .disabled(true)

      Text("No archived habits")
        .font(.system(size: 16, weight: .semibold))
        .foregroundStyle(Color("TextPrimary"))

      Text("Habits you archive will appear here")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextSecondary"))
        .multilineTextAlignment(.center)
    }
    .frame(maxWidth: .infinity, minHeight: 120)
  }

  private var noSearchResultsView: some View {
    VStack(spacing: 16) {
      Image(systemName: "magnifyingglass")
        .font(.system(size: 32))
        .foregroundStyle(Color("TextMuted"))

      Text("No results found")
        .font(.system(size: 16, weight: .semibold))
        .foregroundStyle(Color("TextPrimary"))

      Text("Try searching with different keywords")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextSecondary"))
        .multilineTextAlignment(.center)
    }
    .frame(maxWidth: .infinity, minHeight: 120)
  }
}

// MARK: - Archived Habit Row View

struct ArchivedHabitRowView: View {
  let habit: Habit
  @Binding var showingHabitMenu: String?
  let onRestore: () -> Void
  let onDelete: () -> Void

  var body: some View {
    ArchivedHabitCard {
      VStack(spacing: 8) {
        // Habit header
        HStack {
          // Archive icon
          ZStack {
            Circle()
              .fill(Color("TextMuted"))
              .frame(width: 24, height: 24)

            Image(systemName: "archivebox")
              .font(.system(size: 10))
              .foregroundStyle(.white)
          }

          // Habit info
          VStack(alignment: .leading, spacing: 2) {
            Text(habit.name)
              .font(.system(size: 14, weight: .medium))
              .foregroundStyle(Color("TextPrimary"))

            Text("Archived on \(habit.archiveDateString)")
              .font(.system(size: 12))
              .foregroundStyle(Color("TextSecondary"))
          }

          Spacer()

          // Menu button
          NeumorphicButton(
            icon: "ellipsis",
            size: 32,
            isPressed: showingHabitMenu == habit.id.uuidString
          ) {
            withAnimation(.easeInOut(duration: 0.2)) {
              showingHabitMenu = showingHabitMenu == habit.id.uuidString ? nil : habit.id.uuidString
            }
          }
        }

        // Read-only habit grid
        HabitGridView(
          habit: habit,
          showLabels: true,
          autoScroll: true,
          scrollTrigger: .constant(nil),
          isReadOnly: true
        )
        .opacity(0.7)  // Dim archived grid
      }
    }
    .overlay(alignment: .topTrailing) {
      // Dropdown menu
      if showingHabitMenu == habit.id.uuidString {
        archiveDropdownMenu
          .offset(x: -8, y: 45)
          .zIndex(1000)
      }
    }
    .zIndex(showingHabitMenu == habit.id.uuidString ? 999 : 0)
  }

  private var archiveDropdownMenu: some View {
    NeumorphicCard(cornerRadius: 12) {
      VStack(spacing: 0) {
        archiveDropdownItem(
          icon: "arrow.uturn.backward",
          title: "Restore",
          color: Color("Success")
        ) {
          onRestore()
          showingHabitMenu = nil
        }

        archiveDropdownItem(
          icon: "trash",
          title: "Delete",
          color: Color("Warning")
        ) {
          onDelete()
          showingHabitMenu = nil
        }
      }
    }
    .frame(minWidth: 100)
    .fixedSize()
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color("BackgroundPrimary"))
        .shadow(color: Color.black.opacity(0.3), radius: 8, x: 0, y: 4)
    )
    .transition(
      .asymmetric(
        insertion: .scale(scale: 0.8).combined(with: .opacity),
        removal: .scale(scale: 0.8).combined(with: .opacity)
      ))
  }

  private func archiveDropdownItem(
    icon: String,
    title: String,
    color: Color,
    action: @escaping () -> Void
  ) -> some View {
    Button(action: action) {
      HStack(spacing: 12) {
        Image(systemName: icon)
          .font(.system(size: 12))
          .foregroundStyle(color)
          .frame(width: 16)

        Text(title)
          .font(.system(size: 14, weight: .medium))
          .foregroundStyle(Color("TextPrimary"))
          .lineLimit(1)
          .fixedSize(horizontal: true, vertical: false)

        Spacer(minLength: 8)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 14)
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
    .background(
      Rectangle()
        .fill(Color("TextMuted").opacity(0.1))
        .opacity(0)
    )
    .onHover { isHovered in
      // Add hover effect if needed
    }
  }
}

// MARK: - Archived Habit Card

struct ArchivedHabitCard<Content: View>: View {
  let content: Content
  let padding: CGFloat = 12
  let cornerRadius: CGFloat = 15

  init(@ViewBuilder content: () -> Content) {
    self.content = content()
  }

  var body: some View {
    ZStack {
      // Inset shadow background for archived appearance
      RoundedRectangle(cornerRadius: cornerRadius)
        .fill(Color("BackgroundPrimary"))
        .shadow(
          color: Color("NeumorphicShadowDark").opacity(0.4),
          radius: 3,
          x: 3,
          y: 3
        )
        .shadow(
          color: Color("NeumorphicShadowLight").opacity(0.3),
          radius: 3,
          x: -3,
          y: -3
        )
        // Inset shadow overlay
        .overlay(
          RoundedRectangle(cornerRadius: cornerRadius)
            .stroke(Color("NeumorphicShadowDark").opacity(0.1), lineWidth: 1)
            .shadow(
              color: Color("NeumorphicShadowDark").opacity(0.2),
              radius: 2,
              x: 1,
              y: 1
            )
            .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
            .blendMode(.multiply)
        )

      content
        .padding(padding)
        .opacity(0.7)  // Dim archived content
    }
  }
}

#Preview {
  ArchiveView()
    .modelContainer(for: [Habit.self, HabitRecord.self], inMemory: true)
}
