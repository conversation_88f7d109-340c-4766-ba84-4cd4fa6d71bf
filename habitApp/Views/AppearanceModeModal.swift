import SwiftUI

struct AppearanceModeModal: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(AppearanceManager.self) private var appearanceManager
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
                .padding(.horizontal, 16)
                .padding(.top, 3)
            
            ScrollView {
                VStack(spacing: 20) {
                    
                    
                    // Mode Options
                    LazyVStack(spacing: 16) {
                        ForEach(AppearanceMode.allCases, id: \.self) { mode in
                            AppearanceModeRow(
                                mode: mode,
                                isSelected: appearanceManager.appearanceMode == mode
                            ) {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    appearanceManager.appearanceMode = mode
                                }
                                
                                // Auto-dismiss the sheet after a short delay
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    dismiss()
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    Spacer(minLength: 40)
                }
            }
        }
        .background(Color("BackgroundPrimary"))
        .navigationBarHidden(true)
    }
    
    // MARK: - Header
    
    private var headerView: some View {
        HStack {
            // Back button
            NeumorphicButton(
                icon: "chevron.left",
                size: 40,
                isPressed: false
            ) {
                dismiss()
            }
            
            Spacer()
            
            // Title
            
            Text("Display Mode")
                .font(.system(size: 24, weight: .bold))
                .foregroundStyle(Color("TextPrimary"))
                
                
            
            Spacer()
            
            // Placeholder for alignment
            NeumorphicButton(
                icon: "chevron.left",
                size: 40,
                isPressed: false
            ) {
                // No action - just for spacing
            }
            .opacity(0)
        }
        .padding(.bottom, 24)
    }
}

struct AppearanceModeRow: View {
    let mode: AppearanceMode
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color("BackgroundPrimary"))
                        .frame(width: 48, height: 48)
                        .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 4, x: 4, y: 4)
                        .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 4, x: -4, y: -4)
                    
                    Image(systemName: mode.icon)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(modeColor)
                }
                
                // Title and Description
                VStack(alignment: .leading, spacing: 4) {
                    Text(mode.displayName)
                        .font(.system(size: 18, weight: .semibold, design: .rounded))
                        .foregroundColor(Color("TextPrimary"))
                    
                    Text(modeDescription)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color("TextSecondary"))
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Selection Indicator
                ZStack {
                    Circle()
                        .stroke(isSelected ? modeColor : Color("TextMuted"), lineWidth: 2)
                        .frame(width: 24, height: 24)
                    
                    if isSelected {
                        Circle()
                            .fill(modeColor)
                            .frame(width: 16, height: 16)
                            .scaleEffect(isSelected ? 1.0 : 0.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSelected)
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color("BackgroundPrimary"))
                    .shadow(color: Color("NeumorphicShadowDark").opacity(isSelected ? 0.8 : 0.6), radius: isSelected ? 8 : 6, x: isSelected ? 8 : 6, y: isSelected ? 8 : 6)
                    .shadow(color: Color("NeumorphicShadowLight").opacity(isSelected ? 0.7 : 0.5), radius: isSelected ? 8 : 6, x: isSelected ? -8 : -6, y: isSelected ? -8 : -6)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var modeColor: Color {
        switch mode {
        case .system:
            return Color("Mindfulness")
        case .light:
            return Color("Warning")
        case .dark:
            return Color("Info")
        }
    }
    
    private var modeDescription: String {
        switch mode {
        case .system:
            return "Follows your device settings"
        case .light:
            return "Always use light appearance"
        case .dark:
            return "Always use dark appearance"
        }
    }
}

#Preview {
    AppearanceModeModal()
        .environment(AppearanceManager())
}
