import SwiftUI

// MARK: - Typography Extensions
extension Font {
  static let habitTitle = Font.system(size: 28, weight: .bold, design: .rounded)
  static let habitSubtitle = Font.system(size: 18, weight: .semibold, design: .rounded)
  static let habitBody = Font.system(size: 16, weight: .regular, design: .default)
  static let habitCaption = Font.system(size: 13, weight: .regular, design: .default)
  static let habitButton = Font.system(size: 16, weight: .medium, design: .rounded)
}

// MARK: - Neumorphic View Modifier
struct NeumorphicModifier: ViewModifier {
  var isPressed: Bool = false
  var cornerRadius: CGFloat = 20
  var shadowRadius: CGFloat = 6

  func body(content: Content) -> some View {
    content
      .background(Color.backgroundPrimary)
      .cornerRadius(cornerRadius)
      .shadow(
        color: isPressed ? Color.clear : Color.neumorphicShadowDark.opacity(0.3),
        radius: isPressed ? 0 : shadowRadius,
        x: isPressed ? 0 : shadowRadius,
        y: isPressed ? 0 : shadowRadius
      )
      .shadow(
        color: isPressed ? Color.clear : Color.neumorphicShadowLight.opacity(0.7),
        radius: isPressed ? 0 : shadowRadius,
        x: isPressed ? 0 : -shadowRadius,
        y: isPressed ? 0 : -shadowRadius
      )
      .overlay(
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(Color.clear)
          .shadow(
            color: isPressed ? Color.neumorphicShadowDark.opacity(0.3) : Color.clear,
            radius: isPressed ? 4 : 0,
            x: 0,
            y: 0
          )
          .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
          .shadow(
            color: isPressed ? Color.neumorphicShadowLight.opacity(0.7) : Color.clear,
            radius: isPressed ? 2 : 0,
            x: 0,
            y: 0
          )
          .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
          .blendMode(.overlay)
      )
  }
}

// MARK: - Neumorphic Button Style
struct NeumorphicButtonStyle: ButtonStyle {
  var cornerRadius: CGFloat = 15
  var shadowRadius: CGFloat = 4

  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
      .modifier(
        NeumorphicModifier(
          isPressed: configuration.isPressed,
          cornerRadius: cornerRadius,
          shadowRadius: shadowRadius
        )
      )
      .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
  }
}

// MARK: - Concave Shadow Style (Inset Effect)
struct ConcaveShadowStyle: ViewModifier {
  var cornerRadius: CGFloat = 12
  var shadowThickness: CGFloat = 12
  var darkOpacity: Double = 0.7
  var lightOpacity: Double = 0.4

  func body(content: Content) -> some View {
    ZStack {
      // Base background
      RoundedRectangle(cornerRadius: cornerRadius)
        .fill(Color.backgroundPrimary)

      // Inset shadow effect - Top and Left inner shadows
      VStack(spacing: 0) {
        // Top inner shadow
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.neumorphicShadowDark.opacity(darkOpacity), location: 0),
                .init(color: Color.neumorphicShadowDark.opacity(darkOpacity * 0.6), location: 0.5),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowThickness)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      HStack(spacing: 0) {
        // Left inner shadow
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.neumorphicShadowDark.opacity(darkOpacity), location: 0),
                .init(color: Color.neumorphicShadowDark.opacity(darkOpacity * 0.6), location: 0.5),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowThickness)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Bottom-right light highlight for depth
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(
                  color: Color.neumorphicShadowLight.opacity(lightOpacity * 0.5), location: 0.5),
                .init(color: Color.neumorphicShadowLight.opacity(lightOpacity), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowThickness)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(
                  color: Color.neumorphicShadowLight.opacity(lightOpacity * 0.5), location: 0.5),
                .init(color: Color.neumorphicShadowLight.opacity(lightOpacity), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowThickness)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Content
      content
        .background(Color.clear)
    }
    .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
  }
}

// MARK: - Habit Progress Ring
struct HabitProgressRing: View {
  let progress: Double
  let size: CGFloat
  let lineWidth: CGFloat

  var body: some View {
    ZStack {
      Circle()
        .stroke(Color.textMuted.opacity(0.3), lineWidth: lineWidth)

      Circle()
        .trim(from: 0, to: progress)
        .stroke(
          Color.success,
          style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
        )
        .rotationEffect(.degrees(-90))
        .animation(.easeInOut(duration: 0.5), value: progress)

      Text("\(Int(progress * 100))%")
        .font(.habitCaption)
        .fontWeight(.bold)
        .foregroundColor(Color.textPrimary)
    }
    .frame(width: size, height: size)
  }
}

// MARK: - Habit Level Indicator
struct HabitLevelIndicator: View {
  let level: Int
  let size: CGFloat
  let baseColor: Color

  var levelColor: Color {
    Color.habitLevel(level, baseColor: baseColor)
  }

  var isCompleted: Bool {
    level >= 5
  }

  var body: some View {
    ZStack {
      if level == 0 {
        // Concave/inset styling for no activity
        RoundedRectangle(cornerRadius: size * 0.3)
          .fill(Color.backgroundPrimary)
          .frame(width: size, height: size)
          .shadow(color: Color.neumorphicShadowDark.opacity(0.4), radius: 2, x: 2, y: 2)
          .shadow(color: Color.neumorphicShadowLight.opacity(0.4), radius: 2, x: -2, y: -2)
          .blendMode(.multiply)
      } else {
        // Convex styling for all progress levels
        RoundedRectangle(cornerRadius: size * 0.3)
          .fill(levelColor)
          .frame(width: size, height: size)
          .shadow(color: Color.neumorphicShadowDark.opacity(0.2), radius: 1, x: 1, y: 1)
          .shadow(color: Color.neumorphicShadowLight.opacity(0.3), radius: 1, x: -1, y: -1)
      }

      if isCompleted {
        Image(systemName: "checkmark.circle.fill")
          .font(.system(size: size * 0.4, weight: .bold))
          .foregroundColor(.white)
      }
    }
  }
}

// MARK: - Style Guide View
struct StyleGuideView: View {
  @State private var buttonPressed = false
  @State private var progressValue: Double = 0.67

  var body: some View {
    NavigationView {
      ScrollView {
        VStack(spacing: 32) {
          // Header
          headerSection

          // Colors Section
          colorsSection

          // Typography Section
          typographySection

          // Components Section
          componentsSection

          // Interactive Elements
          interactiveSection

          // Habit Tracking Elements
          habitTrackingSection
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 32)
      }
      .background(Color.backgroundPrimary.ignoresSafeArea())
      .navigationTitle("Style Guide")
      .navigationBarTitleDisplayMode(.large)
    }
  }

  private var headerSection: some View {
    VStack(spacing: 16) {
      HStack {
        HStack(spacing: 12) {
          Image(systemName: "square.grid.3x3.fill")
            .font(.system(size: 28, weight: .medium))
            .foregroundColor(.habitSuccess)

          VStack(alignment: .leading, spacing: 4) {
            Text("HabitApp Design System")
              .font(.habitTitle)
              .foregroundColor(Color.textPrimary)

            Text("SF Pro typography & SF Symbols")
              .font(.habitBody)
              .foregroundColor(Color.textSecondary)
          }
        }
        Spacer()
      }

      Divider()
        .background(Color.textMuted.opacity(0.3))
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier(cornerRadius: 20))
    .padding(.horizontal, 20)
  }

  private var colorsSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      sectionHeader("Colors")

      VStack(spacing: 12) {
        colorRow("Background Primary", color: .backgroundPrimary)
        colorRow("Text Primary", color: Color.textPrimary)
        colorRow("Text Secondary", color: Color.textSecondary)
        colorRow("Text Muted", color: Color.textMuted)

        Divider().padding(.vertical, 8)

        colorRow("Success (Habits)", color: .habitSuccess)
        colorRow("Warning", color: .habitWarning)
        colorRow("Info", color: .habitInfo)
        colorRow("Wellness", color: .habitWellness)
        colorRow("Mindfulness", color: .habitMindfulness)
      }
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier())
    .padding(.horizontal, 20)
  }

  private var typographySection: some View {
    VStack(alignment: .leading, spacing: 16) {
      sectionHeader("Typography")

      VStack(alignment: .leading, spacing: 12) {
        typographyExample("Title", font: .habitTitle, "Main headings and page titles")
        typographyExample("Subtitle", font: .habitSubtitle, "Section headers and important text")
        typographyExample("Body", font: .habitBody, "Main content and descriptions")
        typographyExample("Caption", font: .habitCaption, "Secondary information and labels")
        typographyExample("Button", font: .habitButton, "Interactive elements")
      }
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier())
    .padding(.horizontal, 20)
  }

  private var componentsSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      sectionHeader("Neumorphic Components")

      VStack(spacing: 16) {
        // Card example
        HStack(spacing: 16) {
          Image(systemName: "rectangle.3.group.fill")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(.habitInfo)

          VStack(alignment: .leading, spacing: 8) {
            Text("Neumorphic Card")
              .font(.habitSubtitle)
              .foregroundColor(Color.textPrimary)

            Text("Cards use raised neumorphic styling with soft shadows")
              .font(.habitBody)
              .foregroundColor(Color.textSecondary)
          }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .modifier(NeumorphicModifier())
        .padding(.all, 16)

        // Button examples
        HStack(spacing: 16) {
          Button("Standard Button") {}
            .buttonStyle(NeumorphicButtonStyle())
            .padding(.horizontal, 16)
            .padding(.vertical, 8)

          Button(action: {}) {
            Image(systemName: "plus.circle.fill")
              .font(.system(size: 18, weight: .medium))
              .foregroundColor(.white)
          }
          .buttonStyle(NeumorphicButtonStyle(cornerRadius: 12))
          .frame(width: 40, height: 40)
          .background(Color.habitSuccess)
          .cornerRadius(12)
        }

        // Concave Shadow Example
        VStack(alignment: .leading, spacing: 12) {
          HStack(spacing: 12) {
            Image(systemName: "textformat.alt")
              .font(.system(size: 20, weight: .medium))
              .foregroundColor(.habitMindfulness)

            VStack(alignment: .leading, spacing: 4) {
              Text("Concave Shadow (Inset)")
                .font(.habitSubtitle)
                .foregroundColor(Color.textPrimary)

              Text("Used for text fields and input areas")
                .font(.habitCaption)
                .foregroundColor(Color.textSecondary)
            }
          }

          // Example text field with concave shadow
          Text("Example text input field")
            .font(.habitBody)
            .foregroundColor(Color.textPrimary)
            .padding(16)
            .modifier(ConcaveShadowStyle(cornerRadius: 12, shadowThickness: 12))
            .frame(maxWidth: .infinity, alignment: .leading)
        }
      }
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier())
    .padding(.horizontal, 20)
  }

  private var interactiveSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      sectionHeader("Interactive Elements")

      VStack(spacing: 16) {
        // Progress Ring
        HStack(spacing: 16) {
          HabitProgressRing(progress: progressValue, size: 60, lineWidth: 4)

          VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 8) {
              Image(systemName: "chart.pie.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.habitSuccess)

              Text("Progress Ring")
                .font(.habitSubtitle)
                .foregroundColor(Color.textPrimary)
            }

            Text("Shows completion percentage")
              .font(.habitCaption)
              .foregroundColor(Color.textSecondary)
          }

          Spacer()

          Button("Update") {
            progressValue = Double.random(in: 0.2...1.0)
          }
          .buttonStyle(NeumorphicButtonStyle(cornerRadius: 8, shadowRadius: 2))
          .padding(.horizontal, 12)
          .padding(.vertical, 6)
        }
      }
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier())
    .padding(.horizontal, 20)
  }

  private var habitTrackingSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      sectionHeader("Habit Tracking")

      VStack(spacing: 16) {
        // Habit levels explanation
        HStack(spacing: 12) {
          Image(systemName: "chart.bar.fill")
            .font(.system(size: 20, weight: .medium))
            .foregroundColor(.habitWellness)

          VStack(alignment: .leading, spacing: 8) {
            Text("Completion Levels")
              .font(.habitSubtitle)
              .foregroundColor(Color.textPrimary)

            Text("Multi-level progression system for habits")
              .font(.habitBody)
              .foregroundColor(Color.textSecondary)
          }
        }

        // Level indicators with different base colors
        VStack(spacing: 16) {
          // Green habit example
          HStack(spacing: 16) {
            ForEach(0...5, id: \.self) { level in
              VStack(spacing: 8) {
                HabitLevelIndicator(level: level, size: 32, baseColor: .habitSuccess)

                Text("L\(level)")
                  .font(.habitCaption)
                  .foregroundColor(Color.textMuted)
              }
            }
          }

          // Blue habit example
          HStack(spacing: 16) {
            ForEach(0...5, id: \.self) { level in
              VStack(spacing: 8) {
                HabitLevelIndicator(level: level, size: 32, baseColor: .habitInfo)

                Text("L\(level)")
                  .font(.habitCaption)
                  .foregroundColor(Color.textMuted)
              }
            }
          }
        }
        .frame(maxWidth: .infinity)

        // Level descriptions
        VStack(alignment: .leading, spacing: 4) {
          Text("• Level 0: No activity (background color)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
          Text("• Level 1: Minimal (20% transparency)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
          Text("• Level 2: Light progress (40% transparency)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
          Text("• Level 3: Good progress (60% transparency)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
          Text("• Level 4: Strong progress (80% transparency)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
          Text("• Level 5: Completed (full color with checkmark)")
            .font(.habitCaption)
            .foregroundColor(Color.textSecondary)
        }
      }
    }
    .padding(.all, 20)
    .modifier(NeumorphicModifier())
    .padding(.horizontal, 20)
  }

  private func sectionHeader(_ title: String) -> some View {
    Text(title)
      .font(.habitSubtitle)
      .foregroundColor(Color.textPrimary)
      .frame(maxWidth: .infinity, alignment: .leading)
  }

  private func colorRow(_ name: String, color: Color) -> some View {
    HStack(spacing: 12) {
      RoundedRectangle(cornerRadius: 8)
        .fill(color)
        .frame(width: 32, height: 32)
        .overlay(
          RoundedRectangle(cornerRadius: 8)
            .stroke(Color.textMuted.opacity(0.3), lineWidth: 1)
        )

      Text(name)
        .font(.habitBody)
        .foregroundColor(Color.textPrimary)

      Spacer()
    }
  }

  private func typographyExample(_ name: String, font: Font, _ description: String) -> some View {
    VStack(alignment: .leading, spacing: 4) {
      Text(name)
        .font(font)
        .foregroundColor(Color.textPrimary)

      Text(description)
        .font(.habitCaption)
        .foregroundColor(Color.textSecondary)
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }
}

#Preview {
  StyleGuideView()
}
