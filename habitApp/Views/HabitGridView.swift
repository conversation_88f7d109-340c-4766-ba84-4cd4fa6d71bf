import SwiftUI

struct HabitGridView: View {
  let habit: Habit
  let showLabels: Bool
  let autoScroll: Bool
  let isReadOnly: Bo<PERSON>
  @Binding var scrollTrigger: Date?

  @Environment(\.colorScheme) private var colorScheme
  @Environment(\.modelContext) private var modelContext
  @Environment(AppearanceManager.self) private var appearanceManager
  @Environment(HabitRepository.self) private var repository

  // NEW: HabitViewModel for optimized operations
  @State private var habitViewModel: HabitViewModel?
  @State private var optimizedWeeks: [WeekInfo] = []
  @State private var isLoadingOptimized = false  // Start as false since we have fallback

  init(
    habit: Habit, showLabels: Bool = true, autoScroll: Bool = true,
    scrollTrigger: Binding<Date?> = .constant(nil), isReadOnly: Bool = false
  ) {
    self.habit = habit
    self.showLabels = showLabels
    self.autoScroll = autoScroll
    self.isReadOnly = isReadOnly
    self._scrollTrigger = scrollTrigger
  }

  // Computed property for dynamic habit color
  private var habitColor: String {
    GridColorService.getGridColor(
      for: habit,
      modelContext: modelContext,
      useGridColor: appearanceManager.useGridColor
    )
  }

  var body: some View {
    // Force refresh when habit records change by accessing todayCompletionLevel
    let _ = habit.todayCompletionLevel

    return HStack(alignment: .top, spacing: 0) {
      // Main grid with month labels
      ScrollViewReader { proxy in
        ScrollView(.horizontal, showsIndicators: false) {
          VStack(alignment: .leading, spacing: 2) {
            // Month labels (top)
            if showLabels {
              monthLabelsView
            }

            // Always use optimized grid view which handles fallback internally
            optimizedGridView
          }
        }
        .onAppear {
          Task {
            await initializeOptimizedGrid()
          }

          if autoScroll {
            // Scroll to show recent weeks - multiple attempts for reliability
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
          }
        }
        .onChange(of: scrollTrigger) { _, _ in
          // External trigger to scroll to today's grid
          withAnimation(.easeInOut(duration: 0.5)) {
            proxy.scrollTo("recent", anchor: .trailing)
          }
        }
        .onChange(of: optimizedWeeks.count) { _, _ in
          // Auto-scroll when optimized grid finishes loading
          if autoScroll && !optimizedWeeks.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
          }
        }
        .onChange(of: habit.todayCompletionLevel) { _, _ in
          // Refresh optimized data when today's level changes
          Task {
            await refreshOptimizedGrid()
          }
        }
        .onChange(of: appearanceManager.gridSize) { _, _ in
          // Auto-scroll when grid size changes
          if autoScroll {
            // Multiple attempts with different delays to ensure scroll works
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
              withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo("recent", anchor: .trailing)
              }
            }
          }
        }
        .onChange(of: appearanceManager.weekStartsOnMonday) { _, _ in
          // Regenerate grid when week start day changes
          Task {
            await refreshOptimizedGrid()
          }
        }
      }

      // Day labels (right side)
      if showLabels {
        dayLabelsView
      }
    }
    .padding(.vertical, 8)
  }

  private var monthLabelsView: some View {
    let weeks = weekData
    let monthPositions = calculateMonthPositions(weeks: weeks)
    let cellWidth: CGFloat = appearanceManager.gridSize.cellSize
    let cellSpacing: CGFloat = 1  // Match the actual grid spacing
    let weekWidth = cellWidth + cellSpacing

    return ZStack(alignment: .topLeading) {
      // Invisible background to maintain proper width
      Rectangle()
        .fill(Color.clear)
        .frame(width: CGFloat(weeks.count) * weekWidth - cellSpacing, height: 10)

      // Position each month label precisely
      ForEach(Array(monthPositions.enumerated()), id: \.offset) { index, monthPosition in
        Text(monthPosition.label)
          .font(.system(size: appearanceManager.gridSize.monthLabelFontSize))
          .foregroundStyle(Color("TextMuted"))
          .offset(x: CGFloat(monthPosition.offsetFromStart) * weekWidth, y: 0)
      }
    }
    .padding(.horizontal, 8)  // Match grid padding
  }

  private var dayLabelsView: some View {
    let shouldShowIndicators = appearanceManager.showFrequencyLabels
    let activeDays = getActiveDaysForHabit()
    let orderedDays = DayOfWeek.orderedCases(
      startingWithMonday: appearanceManager.weekStartsOnMonday)
    let cellSize: CGFloat = appearanceManager.gridSize.cellSize

    return VStack(spacing: 1) {  // Match grid cell spacing exactly
      Spacer().frame(height: 12)  // Match month labels space + spacing

      ForEach(Array(orderedDays.enumerated()), id: \.offset) { index, day in
        ZStack {
          Text(day.shortName)
            .font(.system(size: appearanceManager.gridSize.dayLabelFontSize))
            .foregroundStyle(Color("TextMuted"))
            .frame(width: cellSize, height: cellSize)

          if shouldShowIndicators && activeDays.contains(day) {
            Circle()
              .stroke(Color.fromHexOrAsset(habitColor), lineWidth: appearanceManager.gridSize.strokeWidth)
              .frame(width: cellSize - 2, height: cellSize - 2)
              .background(
                Circle()
                  .fill(Color.fromHexOrAsset(habitColor).opacity(0.1))
                  .frame(width: cellSize - 2, height: cellSize - 2)
              )
          }
        }
      }
    }
    .padding(.leading, 2)
  }

  private var gridView: some View {
    let weeks = weekData
    let calendar = Calendar.current
    let today = Date()
    // Find the week containing today
    let currentWeekIndex = weeks.firstIndex(where: { week in
      week.days.contains(where: { calendar.isDate($0.date, inSameDayAs: today) })
    })
    return HStack(spacing: 1) {
      ForEach(weeks, id: \.weekIndex) { week in
        weekColumnView(week: week)
          .id(currentWeekIndex == week.weekIndex ? "recent" : "\(week.weekIndex)")
      }
    }
    .padding(.horizontal, 8)
  }

  private func weekColumnView(week: WeekInfo) -> some View {
    VStack(spacing: 1) {
      ForEach(week.days, id: \.date) { day in
        gridCell(for: day)
      }
    }
  }

  private func gridCell(for day: HabitDay) -> some View {
    let backgroundColor = cellBackgroundColor(for: day)
    let cellSize: CGFloat = appearanceManager.gridSize.cellSize
    let cornerRadius: CGFloat = appearanceManager.gridSize.cornerRadius

    return RoundedRectangle(cornerRadius: cornerRadius)
      .fill(backgroundColor)
      .frame(width: cellSize, height: cellSize)
      .overlay {
        cellOverlayView(for: day)
      }
  }

  private func cellOverlayView(for day: HabitDay) -> some View {
    let cornerRadius: CGFloat = appearanceManager.gridSize.cornerRadius
    let strokeWidth: CGFloat = appearanceManager.gridSize.strokeWidth
    let markerSize: CGFloat = appearanceManager.gridSize.markerSize
    let markerOffset: CGFloat = appearanceManager.gridSize.markerOffset
    let emojiSize: CGFloat = appearanceManager.gridSize.emojiSize

    return ZStack {
      if day.isToday && appearanceManager.showTodayHighlight {
        RoundedRectangle(cornerRadius: cornerRadius)
          .stroke(Color.red, lineWidth: strokeWidth)
      }

      if day.isFirstDayOfMonth && appearanceManager.showMonthMarkers {
        Circle()
          .fill(Color.yellow)
          .frame(width: markerSize, height: markerSize)
          .offset(x: -markerOffset, y: -markerOffset)  // Position at top-left corner
          .shadow(color: .black.opacity(0.3), radius: 0.5)
      }

      if day.isStartDate && appearanceManager.showStartMark {
        Text("🚀")
          .font(.system(size: emojiSize))
          .shadow(color: .white.opacity(0.9), radius: 2)
          .shadow(color: .black.opacity(0.7), radius: 1)
      }
    }
  }

  /// Calculates the background color for a grid cell based on completion level
  /// Implements n-level system where n = habit.timesPerDay
  /// - Level 0: Gray (no activity)
  /// - Levels 1 to n: Habit category color with progressive opacity matching completion ratio
  private func cellBackgroundColor(for day: HabitDay) -> Color {
    if day.isFuture {
      return Color.gray.opacity(0.1)
    }

    let maxLevel = habit.timesPerDay

    if day.level == 0 {
      return Color.gray.opacity(0.15)  // No activity - gray color
    } else {
      // Progressive transparency: level/timesPerDay
      // 1/3 = 0.33, 2/3 = 0.67, 3/3 = 1.0
      let progressRatio = Double(day.level) / Double(maxLevel)
      let opacity = progressRatio  // Use exact ratio, no minimum
      return Color.fromHexOrAsset(habitColor).opacity(opacity)
    }
  }

  private func shadowRadius(for day: HabitDay) -> CGFloat {
    if day.isFuture || day.level == 0 {
      return 0
    }

    let maxLevel = habit.timesPerDay
    let progressRatio = Double(day.level) / Double(maxLevel)

    if day.level == 1 {
      return 0  // Inset shadow handled differently for minimal progress
    } else if progressRatio <= 0.5 {
      return 1  // Light shadow for low-medium progress
    } else {
      return 2  // Strong shadow for high progress
    }
  }

  private func shadowOffset(for day: HabitDay) -> CGFloat {
    if day.isFuture || day.level == 0 {
      return 0
    }

    let maxLevel = habit.timesPerDay
    let progressRatio = Double(day.level) / Double(maxLevel)

    if day.level == 1 {
      return 0  // Inset shadow handled differently for minimal progress
    } else if progressRatio <= 0.5 {
      return 1  // Light offset for low-medium progress
    } else {
      return 2  // Strong offset for high progress
    }
  }

  private func shadowColor(for day: HabitDay, isDark: Bool) -> Color {
    if day.isFuture || day.level == 0 {
      return .clear
    }

    let maxLevel = habit.timesPerDay
    let progressRatio = Double(day.level) / Double(maxLevel)

    let opacity: Double

    if day.level == 1 {
      opacity = isDark ? (colorScheme == .dark ? 0.7 : 0.6) : (colorScheme == .dark ? 0.3 : 0.4)
    } else if progressRatio <= 0.5 {
      opacity = isDark ? (colorScheme == .dark ? 0.6 : 0.4) : (colorScheme == .dark ? 0.2 : 0.3)
    } else {
      opacity = isDark ? (colorScheme == .dark ? 0.8 : 0.6) : (colorScheme == .dark ? 0.4 : 0.5)
    }

    if isDark {
      return colorScheme == .dark
        ? Color.black.opacity(opacity) : Color("NeumorphicShadowDark").opacity(opacity)
    } else {
      return colorScheme == .dark
        ? Color(red: 0.2, green: 0.2, blue: 0.2).opacity(opacity)
        : Color("NeumorphicShadowLight").opacity(opacity)
    }
  }

  // MARK: - Data Generation

  private func getActiveDaysForHabit() -> Set<DayOfWeek> {
    switch habit.frequency {
    case .daily:
      return Set(DayOfWeek.allCases)
    case .weekdays:
      return [.monday, .tuesday, .wednesday, .thursday, .friday]
    case .custom:
      return habit.customDays
    }
  }

  private var monthData: [MonthInfo] {
    return generateMonthData()
  }

  private var weekData: [WeekInfo] {
    return generateWeekData()
  }

  private func generateMonthData() -> [MonthInfo] {
    let calendar = Calendar.current
    let today = Date()
    let endDate = today
    let startDate = calendar.date(byAdding: .day, value: -365, to: endDate) ?? today

    var months: [MonthInfo] = []
    var current = startDate

    while current <= endDate {
      let monthName = calendar.monthSymbols[calendar.component(.month, from: current) - 1]
      let shortName = String(monthName.prefix(3))
      let weeksInMonth = weeksInMonth(for: current, startDate: startDate, endDate: endDate)

      months.append(
        MonthInfo(
          month: calendar.component(.month, from: current),
          label: shortName,
          weeks: weeksInMonth
        ))

      current = calendar.date(byAdding: .month, value: 1, to: current) ?? endDate
    }

    return months
  }

  private func generateWeekData() -> [WeekInfo] {
    let calendar = Calendar.current
    let today = Date()
    let endDate = today
    let startDate = calendar.date(byAdding: .day, value: -365, to: endDate) ?? today

    var weeks: [WeekInfo] = []
    var current = startDate

    // Align to start day based on setting
    let startWeekday = appearanceManager.weekStartsOnMonday ? 2 : 1  // Monday=2, Sunday=1
    while calendar.component(.weekday, from: current) != startWeekday {
      current = calendar.date(byAdding: .day, value: -1, to: current) ?? current
    }

    var weekIndex = 0
    while current <= endDate {
      let days = generateWeekDays(weekStart: current, calendar: calendar, today: today)
      weeks.append(WeekInfo(weekIndex: weekIndex, days: days))
      weekIndex += 1
      current = calendar.date(byAdding: .weekOfYear, value: 1, to: current) ?? endDate
    }

    return weeks
  }

  private func generateWeekDays(weekStart: Date, calendar: Calendar, today: Date) -> [HabitDay] {
    var days: [HabitDay] = []

    for dayOffset in 0..<7 {
      let day = calendar.date(byAdding: .day, value: dayOffset, to: weekStart) ?? weekStart

      let isToday = calendar.isDate(day, inSameDayAs: today)

      // For today, use the reactive computed property to ensure fresh data
      let level: Int
      if isToday {
        level = habit.todayCompletionLevel
      } else {
        let habitRecord = habit.records?.first { record in
          calendar.isDate(record.date, inSameDayAs: day)
        }
        level = Int(habitRecord?.completionLevel ?? 0)
      }

      let isFuture = day > today
      let isStartDate = calendar.isDate(day, inSameDayAs: habit.createdAt)
      let isFirstDayOfMonth = calendar.component(.day, from: day) == 1

      days.append(
        HabitDay(
          date: day,
          level: level,
          isToday: isToday,
          isFuture: isFuture,
          isStartDate: isStartDate,
          isFirstDayOfMonth: isFirstDayOfMonth
        ))
    }

    return days
  }

  // MARK: - Optimized Grid Methods

  private func initializeOptimizedGrid() async {
    // Initialize HabitViewModel with repository from environment
    guard habitViewModel == nil else { return }

    await MainActor.run {
      // Don't show loading state since fallback grid is already working
      habitViewModel = HabitViewModel(repository: repository)
    }

    await loadOptimizedGridData()
  }

  private func refreshOptimizedGrid() async {
    guard habitViewModel != nil else { return }
    await loadOptimizedGridData()
  }

  private func loadOptimizedGridData() async {
    guard let habitViewModel = habitViewModel else { return }

    // Don't set loading state to avoid hiding the working fallback grid

    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    let endDate = today
    let startDate = calendar.date(byAdding: .day, value: -365, to: endDate) ?? today

    // Generate optimized weeks using cached lookups
    var weeks: [WeekInfo] = []
    var current = startDate
    var weekIndex = 0

    // Align to start day based on setting (same as generateWeekData)
    let startWeekday = appearanceManager.weekStartsOnMonday ? 2 : 1  // Monday=2, Sunday=1
    while calendar.component(.weekday, from: current) != startWeekday {
      current = calendar.date(byAdding: .day, value: -1, to: current) ?? current
    }

    while current <= endDate {
      let optimizedDays = await generateOptimizedWeekDays(
        weekStart: current, calendar: calendar, today: today, habitViewModel: habitViewModel)

      weeks.append(WeekInfo(weekIndex: weekIndex, days: optimizedDays))
      weekIndex += 1
      current = calendar.date(byAdding: .weekOfYear, value: 1, to: current) ?? endDate
    }

    await MainActor.run {
      optimizedWeeks = weeks
      isLoadingOptimized = false
    }
  }

  private func generateOptimizedWeekDays(
    weekStart: Date, calendar: Calendar, today: Date, habitViewModel: HabitViewModel
  ) async -> [HabitDay] {
    var days: [HabitDay] = []

    for dayOffset in 0..<7 {
      let day = calendar.date(byAdding: .day, value: dayOffset, to: weekStart) ?? weekStart

      let isToday = calendar.isDate(day, inSameDayAs: today)

      // For today, use the reactive computed property to ensure fresh data
      let level: Int
      if isToday {
        level = habit.todayCompletionLevel
      } else {
        // O(1) optimized lookup instead of O(n) search
        level = await habitViewModel.getCompletionLevel(for: habit.id, on: day)
      }

      let isFuture = day > today
      let isStartDate = calendar.isDate(day, inSameDayAs: habit.createdAt)
      let isFirstDayOfMonth = calendar.component(.day, from: day) == 1

      days.append(
        HabitDay(
          date: day,
          level: level,
          isToday: isToday,
          isFuture: isFuture,
          isStartDate: isStartDate,
          isFirstDayOfMonth: isFirstDayOfMonth
        ))
    }

    return days
  }

  private var optimizedGridView: some View {
    // Use optimized data if available and seems complete
    // Always fall back to weekData during loading or if optimized data is incomplete
    let shouldUseOptimized =
      !optimizedWeeks.isEmpty && optimizedWeeks.contains { !$0.days.isEmpty }
      && optimizedWeeks.count > 10  // More reasonable threshold for ~1 year of data

    let weeks = shouldUseOptimized ? optimizedWeeks : weekData
    let cellWidth: CGFloat = appearanceManager.gridSize.cellSize
    let cellSpacing: CGFloat = 1

    // Find the week containing today for auto-scroll
    let calendar = Calendar.current
    let today = Date()
    let currentWeekIndex = weeks.firstIndex(where: { week in
      week.days.contains(where: { calendar.isDate($0.date, inSameDayAs: today) })
    })

    return HStack(alignment: .top, spacing: 0) {
      ForEach(weeks, id: \.weekIndex) { week in
        VStack(spacing: cellSpacing) {
          ForEach(Array(week.days.enumerated()), id: \.offset) { index, day in
            optimizedGridCell(for: day, cellWidth: cellWidth)
          }
        }
        .padding(.trailing, cellSpacing)
        .id(currentWeekIndex == week.weekIndex ? "recent" : "\(week.weekIndex)")
      }
    }
    .padding(.horizontal, 8)
  }

  private func optimizedGridCell(for day: HabitDay, cellWidth: CGFloat) -> some View {
    let completionRatio =
      habit.timesPerDay > 0 ? Double(day.level) / Double(habit.timesPerDay) : 0.0
    let isCompleted = day.level >= habit.timesPerDay

    return Rectangle()
      .fill(cellColor(for: day, completionRatio: completionRatio, isCompleted: isCompleted))
      .frame(width: cellWidth, height: cellWidth)
      .cornerRadius(appearanceManager.gridSize.cornerRadius)
      .overlay(
        optimizedCellOverlay(for: day, cellWidth: cellWidth)
      )
      .opacity(day.isFuture ? 0.3 : 1.0)
  }

  private func cellColor(for day: HabitDay, completionRatio: Double, isCompleted: Bool) -> Color {
    if day.isFuture {
      return Color.gray.opacity(0.1)
    }

    if day.level == 0 {
      return Color.gray.opacity(0.15)  // Match original cellBackgroundColor
    }

    let baseColor = Color.fromHexOrAsset(habitColor)
    let progressRatio = Double(day.level) / Double(habit.timesPerDay)
    let opacity = progressRatio  // Use exact ratio like original
    return baseColor.opacity(opacity)
  }

  private func optimizedCellOverlay(for day: HabitDay, cellWidth: CGFloat) -> some View {
    let cornerRadius: CGFloat = appearanceManager.gridSize.cornerRadius
    let strokeWidth: CGFloat = appearanceManager.gridSize.strokeWidth
    let markerSize: CGFloat = appearanceManager.gridSize.markerSize
    let markerOffset: CGFloat = appearanceManager.gridSize.markerOffset
    let emojiSize: CGFloat = appearanceManager.gridSize.emojiSize

    return ZStack {
      // Today highlight
      if day.isToday && appearanceManager.showTodayHighlight {
        RoundedRectangle(cornerRadius: cornerRadius)
          .stroke(Color.red, lineWidth: strokeWidth)
      }

      // Month markers
      if day.isFirstDayOfMonth && appearanceManager.showMonthMarkers {
        Circle()
          .fill(Color.yellow)
          .frame(width: markerSize, height: markerSize)
          .offset(x: -markerOffset, y: -markerOffset)  // Position at top-left corner
          .shadow(color: .black.opacity(0.3), radius: 0.5)
      }

      // Start date indicator
      if day.isStartDate && appearanceManager.showStartMark {
        Text("🚀")
          .font(.system(size: emojiSize))
          .shadow(color: .white.opacity(0.9), radius: 2)
          .shadow(color: .black.opacity(0.7), radius: 1)
      }
    }
  }

  private func todayIndicator(for day: HabitDay, cellWidth: CGFloat) -> some View {
    Group {
      if day.isToday && appearanceManager.showTodayHighlight {
        RoundedRectangle(cornerRadius: appearanceManager.gridSize.cornerRadius)
          .stroke(Color.primary, lineWidth: appearanceManager.gridSize.strokeWidth)
      }
    }
  }

  private func weeksInMonth(for date: Date, startDate: Date, endDate: Date) -> Int {
    let calendar = Calendar.current
    let month = calendar.component(.month, from: date)
    let year = calendar.component(.year, from: date)

    guard let monthStart = calendar.date(from: DateComponents(year: year, month: month, day: 1)),
      let monthEnd = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: monthStart)
    else {
      return 4
    }

    let actualStart = max(monthStart, startDate)
    let actualEnd = min(monthEnd, endDate)

    let days = calendar.dateComponents([.day], from: actualStart, to: actualEnd).day ?? 0
    return max(1, (days + 6) / 7)  // Round up to nearest week
  }

  private func calculateMonthPositions(weeks: [WeekInfo]) -> [MonthPosition] {
    let calendar = Calendar.current
    var positions: [MonthPosition] = []
    var seenMonths: Set<String> = []  // Use month-year combination to handle year transitions

    for (weekIndex, week) in weeks.enumerated() {
      for (dayIndex, day) in week.days.enumerated() {
        let dayMonth = calendar.component(.month, from: day.date)
        let dayYear = calendar.component(.year, from: day.date)
        let dayOfMonth = calendar.component(.day, from: day.date)
        let monthYearKey = "\(dayYear)-\(dayMonth)"

        // Check if this is the first day of the month (marked with yellow circle if enabled)
        if day.isFirstDayOfMonth && !seenMonths.contains(monthYearKey) {
          seenMonths.insert(monthYearKey)

          let monthName = calendar.monthSymbols[dayMonth - 1]
          let shortName = String(monthName.prefix(3))

          // Add year suffix for January to distinguish years
          let displayName =
            (dayMonth == 1) ? shortName + "'" + String(dayYear).suffix(2) : shortName

          // Position the label exactly at the week where the first day of month appears
          positions.append(
            MonthPosition(
              label: displayName,
              offsetFromStart: weekIndex,  // Week column containing the 1st day (with yellow circle if enabled)
              totalWidth: 1
            ))
        }
      }
    }

    return positions
  }

  // MARK: - Placeholder Loading View

  private var placeholderGridView: some View {
    HStack(spacing: 1) {
      ForEach(0..<28, id: \.self) { _ in
        placeholderCell
      }
    }
    .id("placeholder-grid")
  }

  private var placeholderCell: some View {
    let cellSize: CGFloat = appearanceManager.gridSize.cellSize

    return Rectangle()
      .fill(Color("TextMuted").opacity(0.1))
      .frame(
        width: cellSize,
        height: cellSize
      )
      .clipShape(RoundedRectangle(cornerRadius: appearanceManager.gridSize.cornerRadius))
      .overlay(
        RoundedRectangle(cornerRadius: appearanceManager.gridSize.cornerRadius)
          .stroke(Color("TextMuted").opacity(0.05), lineWidth: 0.5)
      )
      .shimmer()  // Add a subtle shimmer effect during loading
  }
}

// Add shimmer modifier
extension View {
  func shimmer() -> some View {
    self.overlay(
      Rectangle()
        .fill(
          LinearGradient(
            colors: [.clear, .white.opacity(0.1), .clear],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .rotationEffect(.degrees(30))
        .offset(x: -200)
        .animation(
          .easeInOut(duration: 1.5).repeatForever(autoreverses: false),
          value: UUID()
        )
    )
    .clipped()
  }
}

// MARK: - Supporting Types

struct HabitDay {
  let date: Date
  let level: Int
  let isToday: Bool
  let isFuture: Bool
  let isStartDate: Bool
  let isFirstDayOfMonth: Bool
}

struct WeekInfo {
  let weekIndex: Int
  let days: [HabitDay]
}

struct MonthInfo {
  let month: Int
  let label: String
  let weeks: Int
}

struct MonthPosition {
  let label: String
  let offsetFromStart: Int  // Days from the start of the first week to the first day of this month
  let totalWidth: Int  // Total columns this month spans
}

#Preview("Morning Walk Current Week Debug") {
  let calendar = Calendar.current
  let today = calendar.date(from: DateComponents(year: 2025, month: 7, day: 23)) ?? Date()
  let startDate = calendar.date(byAdding: .day, value: -30, to: today) ?? today

  // Create a sample habit for preview
  let sampleHabit = habitApp.Habit(
    name: "Morning Walk",
    category: .health,
    timesPerDay: 1
  )

  // Generate current week data for display
  let thisWeekData = generateCurrentWeekDebugData(for: sampleHabit, today: today)

  VStack(spacing: 16) {
    Text("Morning Walk - Current Week (Jul 21-27, 2025)")
      .font(.headline)
      .foregroundStyle(Color("TextPrimary"))

    VStack(alignment: .leading, spacing: 4) {
      ForEach(thisWeekData, id: \.date) { dayData in
        HStack {
          Text(dayData.dayName)
            .font(.system(size: 12, weight: .medium))
            .frame(width: 30, alignment: .leading)

          Text(dayData.date)
            .font(.system(size: 12))
            .frame(width: 80, alignment: .leading)

          Rectangle()
            .fill(dayData.level == 0 ? Color.gray.opacity(0.15) : Color.green.opacity(1.0))
            .frame(width: 20, height: 20)
            .cornerRadius(2)
            .overlay(
              Rectangle()
                .stroke(dayData.isToday ? Color.red : Color.clear, lineWidth: 1)
                .cornerRadius(2)
            )

          Text("Level \(dayData.level)")
            .font(.system(size: 12))
            .foregroundStyle(Color("TextSecondary"))

          Text(dayData.status)
            .font(.system(size: 12))
            .foregroundStyle(dayData.level == 0 ? Color("TextMuted") : Color("Success"))
        }
      }
    }
    .padding(.horizontal)

    HabitGridView(habit: sampleHabit)
  }
  .padding()
  .background(Color("BackgroundPrimary"))
}

private func generateCurrentWeekDebugData(for habit: Habit, today: Date) -> [DayDebugData] {
  let calendar = Calendar.current

  // Find Monday of current week (July 21, 2025)
  let todayWeekday = calendar.component(.weekday, from: today)  // Wednesday = 4
  let daysFromMonday = todayWeekday == 1 ? 6 : todayWeekday - 2  // Wednesday = 2 days from Monday
  let thisWeekMonday = calendar.date(byAdding: .day, value: -daysFromMonday, to: today) ?? today

  var weekData: [DayDebugData] = []
  let dayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

  for dayOffset in 0..<7 {
    let currentDate =
      calendar.date(byAdding: .day, value: dayOffset, to: thisWeekMonday) ?? thisWeekMonday
    let dayOfWeek = calendar.component(.weekday, from: currentDate)  // 1=Sunday, 2=Monday
    let isToday = calendar.isDate(currentDate, inSameDayAs: today)

    // Apply Morning Walk generation logic
    let level: Int16
    if dayOfWeek == 1 {  // Sunday rest day
      level = Int16(Double.random(in: 0...1) < 0.3 ? 1 : 0)
    } else {
      level = Int16(Double.random(in: 0...1) < 0.9 ? 1 : 0)
    }

    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "Jul dd"

    weekData.append(
      DayDebugData(
        dayName: dayNames[dayOffset],
        date: dateFormatter.string(from: currentDate),
        level: level,
        status: level == 0 ? "Rest/Skipped" : "Completed ✓",
        isToday: isToday
      ))
  }

  return weekData
}

struct DayDebugData {
  let dayName: String
  let date: String
  let level: Int16
  let status: String
  let isToday: Bool
}

#Preview("Water Habit (8x/day)") {
  let waterHabit = habitApp.Habit(
    name: "Drink Water",
    category: .health,
    timesPerDay: 8
  )

  VStack {
    Text("Water Habit (8x/day)")
      .font(.headline)
      .foregroundStyle(Color("TextPrimary"))
    HabitGridView(habit: waterHabit)
  }
  .padding()
  .background(Color("BackgroundPrimary"))
}

#Preview("Reading Habit (3x/day)") {
  let readingHabit = habitApp.Habit(
    name: "Read Pages",
    category: .learning,
    timesPerDay: 3
  )

  VStack {
    Text("Reading Habit (3x/day)")
      .font(.headline)
      .foregroundStyle(Color("TextPrimary"))
    HabitGridView(habit: readingHabit)
  }
  .padding()
  .background(Color("BackgroundPrimary"))
}
