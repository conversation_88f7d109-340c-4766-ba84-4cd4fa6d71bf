import SwiftData
import SwiftUI

struct CreateHabitView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(AppearanceManager.self) private var appearanceManager
  @Environment(NotificationManager.self) private var notificationManager
  @State private var viewModel: CreateHabitViewModel?
  @State private var showTimePicker = false
  @State private var showExamples = false
  @State private var isEditingTimes = false
  @State private var showCustomCategoryModal = false
  @State private var showEditCategoryModal = false
  @State private var editingCategory: CustomCategory? = nil

  private var canCreateHabit: Bool {
    viewModel?.canCreateHabit ?? false
  }

  var body: some View {
    NavigationView {
      ZStack {
        Color("BackgroundPrimary")
          .ignoresSafeArea()

        ScrollView {
          VStack(spacing: 24) {
            // Header
            headerSection

            // Philosophy Reminder
            philosophySection

            // Category Selection
            categorySection

            // Habit Name
            habitNameSection

            // Quick Examples (shown when category is selected)
            if showExamples {
              examplesSection
            }

            // Times Per Day
            timesPerDaySection

            // Frequency
            frequencySection

            // Reminder Time
            reminderSection

            // Success Tips
            successTipsSection

            // Create Button
            createButton

            Spacer(minLength: 100)
          }
          .padding(.horizontal, 16)
          .padding(.top, 24)
        }

        // Time Picker Modal
        if showTimePicker {
          timePickerModal
        }

        // Custom Category Modal
        if showCustomCategoryModal {
          CustomCategoryModal(isPresented: $showCustomCategoryModal) { customCategory in
            viewModel?.addCustomCategory(customCategory)
          }
        }
        
        // Edit Category Modal
        if showEditCategoryModal {
          CustomCategoryModal(isPresented: $showEditCategoryModal, editingCategory: editingCategory) { updatedCategory in
            viewModel?.updateCustomCategory(updatedCategory)
          }
        }
      }
      .navigationBarHidden(true)
      .onAppear {
        if viewModel == nil {
          viewModel = CreateHabitViewModel(
            context: modelContext, notificationManager: notificationManager)
        }
      }
    }
    .environment(appearanceManager)
  }

  // MARK: - Header Section
  private var headerSection: some View {
    HStack {
      // Back Button
      NeumorphicButton(icon: "arrow.left", size: 40) {
        dismiss()
      }

      Spacer()

      // Title
      VStack(spacing: 2) {
        Text("New Habit")
          .font(.system(size: 20, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text("Create your mini habit")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextSecondary"))
      }

      Spacer()

      // Help Button
      NeumorphicButton(icon: "questionmark.circle", size: 40) {
        // Help action
      }
    }
  }

  // MARK: - Philosophy Section
  private var philosophySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        HStack(spacing: 12) {
          // Icon
          ZStack {
            Circle()
              .fill(Color("BackgroundPrimary"))
              .frame(width: 40, height: 40)
              .shadow(
                color: Color("NeumorphicShadowDark").opacity(0.6),
                radius: 5,
                x: 3,
                y: 3
              )
              .shadow(
                color: Color("NeumorphicShadowLight").opacity(0.5),
                radius: 5,
                x: -3,
                y: -3
              )

            Image(systemName: "leaf")
              .font(.system(size: 14, weight: .medium))
              .foregroundStyle(Color("Wellness"))
          }

          VStack(alignment: .leading, spacing: 4) {
            Text("Mini Habits Philosophy")
              .font(.system(size: 16, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))

            Text("Too small to fail")
              .font(.system(size: 14))
              .foregroundStyle(Color("TextSecondary"))
          }

          Spacer()
        }

        VStack(alignment: .leading, spacing: 8) {
          Text(
            "Start with the smallest possible version. You can always do more, but the goal is consistency!"
          )
          .font(.system(size: 14))
          .foregroundStyle(Color("TextSecondary"))

          HStack(spacing: 8) {
            Image(systemName: "target")
              .font(.system(size: 12, weight: .medium))
              .foregroundStyle(Color("Info"))

            Text("Examples: 1 push-up, 1 page, 1 minute walk")
              .font(.system(size: 12))
              .foregroundStyle(Color("TextMuted"))
          }
        }
      }
    }
  }

  // MARK: - Category Section
  private var categorySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        HStack {
          Text("Choose Category")
            .font(.system(size: 18, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))

          Spacer()
          
          // Edit Category Button (only show if a custom category is selected)
          if case .custom(let customCategory) = viewModel?.selectedCategoryType {
            NeumorphicButton(icon: "pencil", size: 32) {
              editingCategory = customCategory
              showEditCategoryModal = true
            }
          }

          NeumorphicButton(icon: "plus", size: 32) {
            showCustomCategoryModal = true
          }
        }

        LazyVGrid(
          columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8
        ) {
          ForEach(viewModel?.allCategoryTypes ?? [], id: \.id) { categoryType in
            CategoryTypeButton(
              categoryType: categoryType,
              isSelected: (viewModel?.selectedCategoryType?.id ?? "") == categoryType.id,
              action: {
                if (viewModel?.selectedCategoryType?.id ?? "") == categoryType.id {
                  // Deselect if tapping the same category
                  viewModel?.selectedCategoryType = nil
                  showExamples = false
                } else {
                  // Select new category
                  viewModel?.selectedCategoryType = categoryType
                  showExamples = true
                }
              },
              onDelete: { customCategory in
                viewModel?.deleteCustomCategory(customCategory)
              }
            )
          }
        }

        // Instruction note - only show if custom categories exist
        if !(viewModel?.customCategories.isEmpty ?? true) {
          HStack(spacing: 8) {
            Image(systemName: "info.circle")
              .font(.system(size: 12))
              .foregroundStyle(Color("Info"))

            Text("Long press custom categories to delete")
              .font(.system(size: 12))
              .foregroundStyle(Color("TextMuted"))

            Spacer()
          }
          .padding(.top, 8)
        }
      }
    }
  }

  // MARK: - Habit Name Section
  private var habitNameSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        Text("Habit Name")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        ZStack {
          // Base background
          RoundedRectangle(cornerRadius: 12)
            .fill(Color("BackgroundPrimary"))

          // Inset shadow effect - Top and Left inner shadows (thicker for more pronounced effect)
          VStack(spacing: 0) {
            // Top inner shadow - made thicker and more intense
            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                    .init(color: Color.clear, location: 1),
                  ]),
                  startPoint: .top,
                  endPoint: .bottom
                )
              )
              .frame(height: 12)

            Spacer()
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          HStack(spacing: 0) {
            // Left inner shadow - made thicker and more intense
            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                    .init(color: Color.clear, location: 1),
                  ]),
                  startPoint: .leading,
                  endPoint: .trailing
                )
              )
              .frame(width: 12)

            Spacer()
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          // Bottom-right light highlight for depth - made thicker to match top-left shadows
          VStack(spacing: 0) {
            Spacer()

            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                  ]),
                  startPoint: .top,
                  endPoint: .bottom
                )
              )
              .frame(height: 12)
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          HStack(spacing: 0) {
            Spacer()

            Rectangle()
              .fill(
                LinearGradient(
                  gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                  ]),
                  startPoint: .leading,
                  endPoint: .trailing
                )
              )
              .frame(width: 12)
          }
          .clipShape(RoundedRectangle(cornerRadius: 12))

          // Text field
          TextField(
            "e.g., 1 push-up, 1 page, 1 minute walk",
            text: Binding(
              get: { viewModel?.habitName ?? "" },
              set: { viewModel?.habitName = $0 }
            )
          )
          .font(.system(size: 16))
          .foregroundStyle(Color("TextPrimary"))
          .padding(16)
          .background(Color.clear)
        }
        .clipShape(RoundedRectangle(cornerRadius: 12))

        Text(
          viewModel?.selectedCategoryType == nil
            ? "Select a category to see suggestions" : "Keep it small and specific"
        )
        .font(.system(size: 12))
        .foregroundStyle(Color("TextMuted"))
      }
    }
  }

  // MARK: - Examples Section
  private var examplesSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 12) {
        Text("Examples")
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        if let categoryType = viewModel?.selectedCategoryType {
          VStack(spacing: 8) {
            ForEach(categoryType.examples, id: \.self) { example in
              ExampleButton(text: example) {
                viewModel?.habitName = example
              }
            }
          }
        }
      }
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: showExamples)
  }

  // MARK: - Times Per Day Section
  private var timesPerDaySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(spacing: 16) {
        Text("Times per Day")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))
          .frame(maxWidth: .infinity, alignment: .leading)

        HStack(spacing: 16) {
          // Minus Button
          NeumorphicButton(icon: "minus", size: 40) {
            if (viewModel?.timesPerDay ?? 1) > 1 {
              viewModel?.timesPerDay = (viewModel?.timesPerDay ?? 1) - 1
            }
          }

          Spacer()

          // Times Display
          if isEditingTimes {
            TextField(
              "",
              value: Binding(
                get: { viewModel?.timesPerDay ?? 1 },
                set: { viewModel?.timesPerDay = $0 }
              ), format: .number
            )
            .font(.system(size: 24, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))
            .multilineTextAlignment(.center)
            .frame(width: 80, height: 48)
            .background(Color("BackgroundPrimary"))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay {
              RoundedRectangle(cornerRadius: 12)
                .stroke(Color("NeumorphicShadowDark").opacity(0.3), lineWidth: 1)
            }
            .onSubmit {
              isEditingTimes = false
            }
          } else {
            Button {
              isEditingTimes = true
            } label: {
              Text("\(viewModel?.timesPerDay ?? 1)")
                .font(.system(size: 24, weight: .semibold))
                .foregroundStyle(Color("TextPrimary"))
                .frame(width: 80, height: 48)
                .background(Color("BackgroundPrimary"))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(
                  color: Color("NeumorphicShadowDark").opacity(0.6),
                  radius: 6,
                  x: 3,
                  y: 3
                )
                .shadow(
                  color: Color("NeumorphicShadowLight").opacity(0.5),
                  radius: 6,
                  x: -3,
                  y: -3
                )
            }
            .buttonStyle(PlainButtonStyle())
          }

          Spacer()

          // Plus Button
          NeumorphicButton(icon: "plus", size: 40) {
            if (viewModel?.timesPerDay ?? 1) < 99 {
              viewModel?.timesPerDay = (viewModel?.timesPerDay ?? 1) + 1
            }
          }
        }

        Text("How many times you want to do this habit per day")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextMuted"))
          .multilineTextAlignment(.center)
      }
    }
  }

  // MARK: - Frequency Section
  private var frequencySection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        Text("Frequency")
          .font(.system(size: 18, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        // Frequency Options
        HStack(spacing: 8) {
          ForEach(FrequencyType.allCases, id: \.self) { frequency in
            FrequencyButton(
              frequency: frequency,
              isSelected: (viewModel?.selectedFrequency ?? .daily) == frequency
            ) {
              viewModel?.selectedFrequency = frequency
            }
          }
        }

        // Custom Days Selection
        if (viewModel?.selectedFrequency ?? .daily) == .custom {
          customDaysSection
        }
      }
    }
  }

  private var customDaysSection: some View {
    let orderedDays = DayOfWeek.orderedCases(
      startingWithMonday: appearanceManager.weekStartsOnMonday)

    return VStack(alignment: .leading, spacing: 12) {
      Text("Select days of the week:")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextSecondary"))

      HStack(spacing: 0) {
        ForEach(orderedDays, id: \.self) { day in
          DayButton(
            day: day,
            isSelected: (viewModel?.customDays ?? Set()).contains(day)
          ) {
            if (viewModel?.customDays ?? Set()).contains(day) {
              viewModel?.customDays.remove(day)
            } else {
              viewModel?.customDays.insert(day)
            }
          }
          .frame(maxWidth: .infinity)
        }
      }

      Text(
        (viewModel?.customDays ?? Set()).isEmpty
          ? "No days selected" : "\((viewModel?.customDays ?? Set()).count) days selected"
      )
      .font(.system(size: 12))
      .foregroundStyle(Color("TextMuted"))
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.selectedFrequency)
  }

  // MARK: - Reminder Section
  private var reminderSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        HStack {
          Text("Reminder Time")
            .font(.system(size: 18, weight: .semibold))
            .foregroundStyle(Color("TextPrimary"))

          Spacer()

          ToggleSwitch(
            isOn: Binding(
              get: { viewModel?.isReminderEnabled ?? false },
              set: { viewModel?.isReminderEnabled = $0 }
            ))
        }

        if viewModel?.isReminderEnabled ?? false {
          reminderTimeSection
        } else {
          reminderDisabledSection
        }
      }
    }
  }

  private var reminderTimeSection: some View {
    VStack(spacing: 12) {
      // Existing times list
      if let times = viewModel?.notificationTimes, !times.isEmpty {
        ForEach(Array(times.enumerated()), id: \.offset) { index, time in
          HStack {
            HStack(spacing: 12) {
              Image(systemName: "clock")
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(Color("Info"))

              Text(time, style: .time)
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(Color("TextPrimary"))
            }

            Spacer()

            Button {
              viewModel?.removeNotificationTime(at: index)
            } label: {
              Image(systemName: "minus.circle.fill")
                .font(.system(size: 16))
                .foregroundStyle(Color("Warning"))
            }
          }
          .padding(12)
          .background(Color("BackgroundPrimary"))
          .clipShape(RoundedRectangle(cornerRadius: 12))
          .shadow(
            color: Color("NeumorphicShadowDark").opacity(0.4),
            radius: 3,
            x: 2,
            y: 2
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(0.3),
            radius: 3,
            x: -2,
            y: -2
          )
        }
      }

      // Add time button
      Button {
        showTimePicker = true
      } label: {
        HStack {
          HStack(spacing: 12) {
            Image(systemName: "plus.circle")
              .font(.system(size: 18, weight: .medium))
              .foregroundStyle(Color("Success"))

            Text(
              viewModel?.notificationTimes.isEmpty == true
                ? "Set reminder time" : "Add another time"
            )
            .font(.system(size: 16, weight: .medium))
            .foregroundStyle(Color("TextPrimary"))
          }

          Spacer()

          Image(systemName: "chevron.right")
            .font(.system(size: 14, weight: .medium))
            .foregroundStyle(Color("TextSecondary"))
        }
        .padding(16)
        .background(Color("BackgroundPrimary"))
        .clipShape(RoundedRectangle(cornerRadius: 15))
        .shadow(
          color: Color("NeumorphicShadowDark").opacity(0.6),
          radius: 5,
          x: 3,
          y: 3
        )
        .shadow(
          color: Color("NeumorphicShadowLight").opacity(0.5),
          radius: 5,
          x: -3,
          y: -3
        )
      }
      .buttonStyle(PlainButtonStyle())

      Text("Tap to add notification times for this habit")
        .font(.system(size: 12))
        .foregroundStyle(Color("TextMuted"))
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.isReminderEnabled)
  }

  private var reminderDisabledSection: some View {
    VStack(spacing: 8) {
      Image(systemName: "bell.slash")
        .font(.system(size: 24))
        .foregroundStyle(Color("TextMuted"))

      Text("Reminders are disabled")
        .font(.system(size: 14))
        .foregroundStyle(Color("TextMuted"))
    }
    .frame(maxWidth: .infinity)
    .padding(16)
    .background(Color("BackgroundPrimary"))
    .clipShape(RoundedRectangle(cornerRadius: 12))
    .overlay {
      RoundedRectangle(cornerRadius: 12)
        .stroke(Color("NeumorphicShadowDark").opacity(0.3), lineWidth: 1)
    }
    .transition(.move(edge: .top).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: viewModel?.isReminderEnabled)
  }

  // MARK: - Success Tips Section
  private var successTipsSection: some View {
    NeumorphicCard(padding: 20) {
      VStack(alignment: .leading, spacing: 16) {
        Text("Success Tips")
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))

        VStack(spacing: 12) {
          SuccessTip(
            icon: "checkmark",
            iconColor: Color("Success"),
            text: "Start ridiculously small - you can always do more"
          )

          SuccessTip(
            icon: "link",
            iconColor: Color("Info"),
            text: "Link to an existing habit for better consistency"
          )

          SuccessTip(
            icon: "trophy",
            iconColor: Color("Warning"),
            text: "Celebrate completion - even small wins matter"
          )
        }
      }
    }
  }

  // MARK: - Create Button
  private var createButton: some View {
    Button {
      createHabit()
    } label: {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 16)
          .fill(canCreateHabit ? Color("Success") : Color("BackgroundPrimary"))

        // Button content
        HStack(spacing: 8) {
          Image(systemName: "plus")
            .font(.system(size: 16, weight: .semibold))

          Text("Create Habit")
            .font(.system(size: 16, weight: .semibold))
        }
        .foregroundStyle(canCreateHabit ? .white : Color("TextPrimary"))
      }
      .frame(maxWidth: .infinity)
      .frame(height: 128)
      .clipShape(RoundedRectangle(cornerRadius: 16))
      // Convex shadows when not clickable (matching FrequencyButton style)
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(canCreateHabit ? 0 : 0.6),
        radius: canCreateHabit ? 0 : 8,
        x: canCreateHabit ? 0 : 4,
        y: canCreateHabit ? 0 : 4
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(canCreateHabit ? 0 : 0.5),
        radius: canCreateHabit ? 0 : 8,
        x: canCreateHabit ? 0 : -4,
        y: canCreateHabit ? 0 : -4
      )
      // Green glow effect when clickable
      .shadow(
        color: canCreateHabit ? Color("Success").opacity(0.4) : Color.clear,
        radius: 15,
        x: 0,
        y: 4
      )
    }
    .buttonStyle(PlainButtonStyle())
    .disabled(!canCreateHabit)
    .animation(.easeInOut(duration: 0.2), value: canCreateHabit)
  }

  // MARK: - Time Picker Modal
  private var timePickerModal: some View {
    ZStack {
      // Background overlay
      Color.black.opacity(0.3)
        .ignoresSafeArea()
        .onTapGesture {
          showTimePicker = false
        }

      VStack {
        Spacer()

        // Time picker content
        VStack(spacing: 24) {
          // Header
          HStack {
            Text("Set Reminder Time")
              .font(.system(size: 18, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))

            Spacer()

            NeumorphicButton(icon: "xmark", size: 32) {
              showTimePicker = false
            }
          }

          // Time picker
          DatePicker(
            "Reminder Time",
            selection: Binding(
              get: { viewModel?.reminderTime ?? Date() },
              set: { viewModel?.reminderTime = $0 }
            ),
            displayedComponents: .hourAndMinute
          )
          .datePickerStyle(.wheel)
          .labelsHidden()

          // Confirm button
          Button {
            // Add the time to notification times instead of just setting reminderTime
            viewModel?.addNotificationTime(viewModel?.reminderTime ?? Date())
            showTimePicker = false
          } label: {
            Text("Add Reminder Time")
              .font(.system(size: 16, weight: .semibold))
              .foregroundStyle(Color("TextPrimary"))
              .frame(maxWidth: .infinity)
              .frame(height: 44)
              .background(Color("BackgroundPrimary"))
              .clipShape(RoundedRectangle(cornerRadius: 16))
              .shadow(
                color: Color("NeumorphicShadowDark").opacity(0.6),
                radius: 5,
                x: 3,
                y: 3
              )
              .shadow(
                color: Color("NeumorphicShadowLight").opacity(0.5),
                radius: 5,
                x: -3,
                y: -3
              )
          }
          .buttonStyle(PlainButtonStyle())
        }
        .padding(24)
        .background(Color("BackgroundPrimary"))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
      }
    }
    .transition(.move(edge: .bottom).combined(with: .opacity))
    .animation(.easeInOut(duration: 0.3), value: showTimePicker)
  }

  // MARK: - Actions
  private func createHabit() {
    Task {
      let success = await viewModel?.createHabit() ?? false
      if success {
        dismiss()
      }
    }
  }
}

// MARK: - Supporting Views and Enums

enum HabitCategory: String, CaseIterable {
  case health = "Health"
  case fitness = "Fitness"
  case mindfulness = "Mindfulness"
  case productivity = "Productivity"
  case wellness = "Wellness"
  case learning = "Learning"
  case nutrition = "Nutrition"
  case sleep = "Sleep"

  var icon: String {
    switch self {
    case .health: return "heart.fill"
    case .fitness: return "figure.walk"
    case .mindfulness: return "brain.head.profile"
    case .productivity: return "checkmark.circle.fill"
    case .wellness: return "leaf.fill"
    case .learning: return "book.fill"
    case .nutrition: return "fork.knife"
    case .sleep: return "moon.fill"
    }
  }

  var color: String {
    switch self {
    case .health: return "Success"
    case .fitness: return "Warning"
    case .mindfulness: return "Mindfulness"
    case .productivity: return "Info"
    case .wellness: return "Wellness"
    case .learning: return "Info"
    case .nutrition: return "Success"
    case .sleep: return "TextSecondary"
    }
  }

  var examples: [String] {
    switch self {
    case .health:
      return ["Take 1 vitamin", "Drink 1 glass of water", "1 deep breath"]
    case .fitness:
      return ["1 push-up", "1 minute walk", "1 squat"]
    case .mindfulness:
      return ["1 minute meditation", "3 deep breaths", "1 gratitude thought"]
    case .productivity:
      return ["Write 1 sentence", "Read 1 page", "Clear 1 email"]
    case .wellness:
      return ["Stretch 1 muscle", "Smile once", "1 positive affirmation"]
    case .learning:
      return ["Learn 1 new word", "Watch 1 tutorial", "Read 1 paragraph"]
    case .nutrition:
      return ["Eat 1 piece of fruit", "Take 1 healthy bite", "Avoid 1 snack"]
    case .sleep:
      return ["Put phone away 1 minute early", "Prepare bed", "1 relaxing breath"]
    }
  }
}

enum FrequencyType: String, CaseIterable {
  case daily = "Daily"
  case weekdays = "Weekdays"
  case custom = "Custom"
}

enum DayOfWeek: String, CaseIterable {
  case monday = "Mon"
  case tuesday = "Tue"
  case wednesday = "Wed"
  case thursday = "Thu"
  case friday = "Fri"
  case saturday = "Sat"
  case sunday = "Sun"

  var shortName: String {
    switch self {
    case .monday: return "M"
    case .tuesday: return "T"
    case .wednesday: return "W"
    case .thursday: return "T"
    case .friday: return "F"
    case .saturday: return "S"
    case .sunday: return "S"
    }
  }

  // MARK: - Week Ordering Utilities

  static func orderedCases(startingWithMonday: Bool) -> [DayOfWeek] {
    return startingWithMonday ? mondayFirst() : sundayFirst()
  }

  static func mondayFirst() -> [DayOfWeek] {
    return [.monday, .tuesday, .wednesday, .thursday, .friday, .saturday, .sunday]
  }

  static func sundayFirst() -> [DayOfWeek] {
    return [.sunday, .monday, .tuesday, .wednesday, .thursday, .friday, .saturday]
  }

  var calendarWeekday: Int {
    // Maps to Calendar.component(.weekday) values
    // Sunday = 1, Monday = 2, etc.
    switch self {
    case .sunday: return 1
    case .monday: return 2
    case .tuesday: return 3
    case .wednesday: return 4
    case .thursday: return 5
    case .friday: return 6
    case .saturday: return 7
    }
  }

  static func from(weekday: Int) -> DayOfWeek {
    // Converts Calendar.component(.weekday) values to DayOfWeek
    // Sunday = 1, Monday = 2, etc.
    switch weekday {
    case 1: return .sunday
    case 2: return .monday
    case 3: return .tuesday
    case 4: return .wednesday
    case 5: return .thursday
    case 6: return .friday
    case 7: return .saturday
    default: return .sunday  // fallback
    }
  }
}

struct CategoryButton: View {
  let category: HabitCategory
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 15)
          .fill(Color("BackgroundPrimary"))

        // Concave inner shadows when selected
        if isSelected {
          concaveInnerShadows
        }

        // Button content
        HStack(spacing: 12) {
          Image(systemName: category.icon)
            .font(.system(size: 16, weight: .medium))
            .foregroundStyle(Color(category.color))
            .frame(width: 20, height: 20)
            .symbolRenderingMode(.monochrome)

          Text(category.rawValue)
            .font(.system(size: 14, weight: isSelected ? .bold : .medium))
            .foregroundStyle(isSelected ? Color(category.color) : Color("TextPrimary"))
            .lineLimit(1)
            .minimumScaleFactor(0.8)

          Spacer(minLength: 0)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
      }
      .frame(height: 44)
      .clipShape(RoundedRectangle(cornerRadius: 15))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
        radius: 5,
        x: isSelected ? 0 : 3,
        y: isSelected ? 0 : 3
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
        radius: 5,
        x: isSelected ? 0 : -3,
        y: isSelected ? 0 : -3
      )
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isSelected ? 0.98 : 1.0)
    .animation(.easeInOut(duration: 0.15), value: isSelected)
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 15)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 15)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 15)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 15)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))
    }
    .allowsHitTesting(false)
  }
}

struct CategoryTypeButton: View {
  let categoryType: CategoryType
  let isSelected: Bool
  let action: () -> Void
  let onDelete: ((CustomCategory) -> Void)?
  @State private var showDeleteOption = false

  init(
    categoryType: CategoryType, isSelected: Bool, action: @escaping () -> Void,
    onDelete: ((CustomCategory) -> Void)? = nil
  ) {
    self.categoryType = categoryType
    self.isSelected = isSelected
    self.action = action
    self.onDelete = onDelete
  }

  var isCustomCategory: Bool {
    if case .custom = categoryType {
      return true
    }
    return false
  }

  var body: some View {
    ZStack {
      // Main button content (not wrapped in Button to avoid gesture conflicts)
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 15)
          .fill(Color("BackgroundPrimary"))

        // Concave inner shadows when selected
        if isSelected {
          concaveInnerShadows
        }

        // Button content
        HStack(spacing: 12) {
          // Icon - handle both custom categories with hex colors and predefined categories
          Group {
            if case .custom(let customCategory) = categoryType {
              Image(systemName: categoryType.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(Color(hex: customCategory.colorHex) ?? Color("TextPrimary"))
            } else {
              Image(systemName: categoryType.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(Color(categoryType.color))
            }
          }
          .frame(width: 20, height: 20)
          .symbolRenderingMode(.monochrome)

          Text(categoryType.name)
            .font(.system(size: 14, weight: isSelected ? .bold : .medium))
            .foregroundStyle(
              isSelected ? getSelectedColor(for: categoryType) : Color("TextPrimary")
            )
            .lineLimit(1)
            .minimumScaleFactor(0.8)

          Spacer(minLength: 0)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
      }
      .frame(height: 44)
      .clipShape(RoundedRectangle(cornerRadius: 15))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
        radius: 5,
        x: isSelected ? 0 : 3,
        y: isSelected ? 0 : 3
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
        radius: 5,
        x: isSelected ? 0 : -3,
        y: isSelected ? 0 : -3
      )
      .scaleEffect(isSelected ? 0.98 : 1.0)
      .animation(.easeInOut(duration: 0.15), value: isSelected)
      .onTapGesture {
        if showDeleteOption {
          showDeleteOption = false
        } else {
          action()
        }
      }
      .onLongPressGesture(minimumDuration: 0.5) {
        if isCustomCategory {
          withAnimation(.easeInOut(duration: 0.2)) {
            showDeleteOption = true
          }
        }
      }

      // Delete button overlay - only show for custom categories when delete option is visible
      if isCustomCategory && showDeleteOption {
        VStack {
          HStack {
            Spacer()
            Button {
              if case .custom(let customCategory) = categoryType {
                onDelete?(customCategory)
              }
              withAnimation(.easeInOut(duration: 0.2)) {
                showDeleteOption = false
              }
            } label: {
              Image(systemName: "minus.circle.fill")
                .font(.system(size: 20))
                .foregroundStyle(.white)
                .background(Circle().fill(Color.red))
            }
            .offset(x: 8, y: -8)
          }
          Spacer()
        }
        .transition(.opacity.combined(with: .scale))
      }
    }
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 15)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 15)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 15)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 15)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))
    }
    .allowsHitTesting(false)
  }

  private func getSelectedColor(for categoryType: CategoryType) -> Color {
    switch categoryType {
    case .predefined(let habitCategory):
      // Use existing predefined colors from HabitCategory
      switch habitCategory {
      case .health: return Color("Success")
      case .fitness: return Color("Warning")
      case .mindfulness: return Color("Mindfulness")
      case .productivity: return Color("Info")
      case .wellness: return Color("Wellness")
      case .learning: return Color("Info")
      case .nutrition: return Color("Success")
      case .sleep: return Color("TextSecondary")
      }
    case .custom(let customCategory):
      // Use hex color from custom category
      return Color(hex: customCategory.colorHex) ?? Color.gray
    }
  }
}

struct ExampleButton: View {
  let text: String
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      HStack {
        Text(text)
          .font(.system(size: 14))
          .foregroundStyle(Color("TextSecondary"))

        Spacer()

        Image(systemName: "arrow.up.left")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextMuted"))
      }
      .padding(.horizontal, 12)
      .padding(.vertical, 8)
      .background(Color("BackgroundPrimary"))
      .clipShape(RoundedRectangle(cornerRadius: 8))
      .overlay {
        RoundedRectangle(cornerRadius: 8)
          .stroke(Color("NeumorphicShadowDark").opacity(0.2), lineWidth: 1)
      }
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct FrequencyButton: View {
  let frequency: FrequencyType
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 15)
          .fill(Color("BackgroundPrimary"))

        // Concave inner shadows when selected
        if isSelected {
          concaveInnerShadows
        }

        // Button text
        Text(frequency.rawValue)
          .font(.system(size: 14, weight: .medium))
          .foregroundStyle(Color("TextPrimary"))
          .frame(maxWidth: .infinity)
          .padding(.vertical, 12)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
        radius: 3,
        x: isSelected ? 0 : 2,
        y: isSelected ? 0 : 2
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
        radius: 3,
        x: isSelected ? 0 : -2,
        y: isSelected ? 0 : -2
      )
    }
    .buttonStyle(PlainButtonStyle())
    .animation(.easeInOut(duration: 0.15), value: isSelected)
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 12)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 12)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 12)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 12)
      }
      .clipShape(RoundedRectangle(cornerRadius: 15))
    }
    .allowsHitTesting(false)
  }
}

struct DayButton: View {
  let day: DayOfWeek
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: 8)
          .fill(Color("BackgroundPrimary"))

        // Concave inner shadows when selected
        if isSelected {
          concaveInnerShadows
        }

        // Button text
        Text(day.shortName)
          .font(.system(size: 12, weight: isSelected ? .bold : .medium))
          .foregroundStyle(isSelected ? Color("Success") : Color("TextPrimary"))
      }
      .frame(width: 32, height: 32)
      .clipShape(RoundedRectangle(cornerRadius: 8))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
        radius: 2,
        x: isSelected ? 0 : 1,
        y: isSelected ? 0 : 1
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
        radius: 2,
        x: isSelected ? 0 : -1,
        y: isSelected ? 0 : -1
      )
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isSelected ? 0.95 : 1.0)
    .animation(.easeInOut(duration: 0.15), value: isSelected)
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 8)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 8))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 8)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: 8))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 8)
      }
      .clipShape(RoundedRectangle(cornerRadius: 8))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: 8)
      }
      .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    .allowsHitTesting(false)
  }
}

struct ToggleSwitch: View {
  @Binding var isOn: Bool

  var body: some View {
    Button {
      isOn.toggle()
    } label: {
      ZStack {
        // Background
        RoundedRectangle(cornerRadius: 12)
          .fill(isOn ? Color("Success") : Color("BackgroundPrimary"))
          .frame(width: 48, height: 24)
          .shadow(
            color: Color("NeumorphicShadowDark").opacity(isOn ? 0.3 : 0.6),
            radius: 3,
            x: isOn ? 1 : 2,
            y: isOn ? 1 : 2
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(isOn ? 0.1 : 0.5),
            radius: 3,
            x: isOn ? -1 : -2,
            y: isOn ? -1 : -2
          )

        // Knob
        Circle()
          .fill(isOn ? .white : Color("TextMuted"))
          .frame(width: 20, height: 20)
          .shadow(
            color: isOn ? Color.black.opacity(0.3) : Color("NeumorphicShadowDark").opacity(0.6),
            radius: isOn ? 1 : 2,
            x: isOn ? 0 : 1,
            y: isOn ? 1 : 1
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(isOn ? 0 : 0.5),
            radius: isOn ? 0 : 2,
            x: isOn ? 0 : -1,
            y: isOn ? 0 : -1
          )
          .offset(x: isOn ? 12 : -12)
      }
    }
    .buttonStyle(PlainButtonStyle())
    .animation(.easeInOut(duration: 0.3), value: isOn)
  }
}

struct SuccessTip: View {
  let icon: String
  let iconColor: Color
  let text: String

  var body: some View {
    HStack(alignment: .top, spacing: 12) {
      ZStack {
        Circle()
          .fill(Color("BackgroundPrimary"))
          .frame(width: 24, height: 24)
          .shadow(
            color: Color("NeumorphicShadowDark").opacity(0.6),
            radius: 2,
            x: 1,
            y: 1
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(0.5),
            radius: 2,
            x: -1,
            y: -1
          )

        Image(systemName: icon)
          .font(.system(size: 10, weight: .medium))
          .foregroundStyle(iconColor)
      }

      Text(text)
        .font(.system(size: 14))
        .foregroundStyle(Color("TextSecondary"))
        .fixedSize(horizontal: false, vertical: true)

      Spacer()
    }
  }
}

struct NeumorphicTextField: View {
  @Binding var text: String
  let placeholder: String

  var body: some View {
    TextField(placeholder, text: $text)
      .font(.system(size: 16))
      .foregroundStyle(Color("TextPrimary"))
      .padding(16)
      .background(Color("BackgroundPrimary"))
      .clipShape(RoundedRectangle(cornerRadius: 12))
      .overlay {
        RoundedRectangle(cornerRadius: 12)
          .stroke(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.3), location: 0),
                .init(color: Color.clear, location: 0.5),
                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 1),
              ]),
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            ),
            lineWidth: 1
          )
      }
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(0.6),
        radius: 6,
        x: 3,
        y: 3
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(0.5),
        radius: 6,
        x: -3,
        y: -3
      )
      .overlay {
        RoundedRectangle(cornerRadius: 12)
          .fill(Color("BackgroundPrimary"))
          .shadow(
            color: Color("NeumorphicShadowDark").opacity(0.6),
            radius: 3,
            x: 2,
            y: 2
          )
          .shadow(
            color: Color("NeumorphicShadowLight").opacity(0.5),
            radius: 3,
            x: -2,
            y: -2
          )
          .blendMode(.multiply)
      }
  }
}

#Preview {
  CreateHabitView()
}
