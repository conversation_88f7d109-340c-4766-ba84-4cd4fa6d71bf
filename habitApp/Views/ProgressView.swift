import SwiftUI

struct ProgressView: View {
  var body: some View {
    ZStack {
      Color("BackgroundPrimary")
        .ignoresSafeArea()

      VStack(spacing: 20) {
        Image(systemName: "chart.line.uptrend.xyaxis")
          .font(.system(size: 48))
          .foregroundStyle(Color("TextMuted"))

        Text("Progress")
          .font(.system(size: 24, weight: .bold))
          .foregroundStyle(Color("TextPrimary"))

        Text("Coming Soon")
          .font(.system(size: 16))
          .foregroundStyle(Color("TextSecondary"))
      }
    }
  }
}

#Preview {
  ProgressView()
}
