import SwiftUI
import SwiftData

struct CalendarRecordsView: View {
    let habit: Habit
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(AppearanceManager.self) private var appearanceManager
    
    @State private var viewModel: CalendarRecordsViewModel?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color("BackgroundPrimary")
                    .ignoresSafeArea()
                
                VStack(spacing: 20) {
                    // Header
                    headerView
                        .padding(.horizontal, 20)
                        .padding(.top, 16)
                        .padding(.bottom, 8)
                    
                    // Month Navigation
                    monthNavigationView
                        .padding(.horizontal, 20)
                        .padding(.bottom, 8)
                    
                    // Calendar Content
                    NeumorphicCard(padding: 12, cornerRadius: 20, style: .convex) {
                        VStack(spacing: 8) {
                            // Weekday labels
                            weekdayLabelsView
                            
                            // Calendar grid
                            calendarGridView
                        }
                    }
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 16)
                    
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            if viewModel == nil {
                viewModel = CalendarRecordsViewModel(habit: habit, modelContext: modelContext, appearanceManager: appearanceManager)
            }
        }
        .onChange(of: appearanceManager.weekStartsOnMonday) { _, _ in
            // Update viewModel reference when setting changes
            viewModel?.appearanceManager = appearanceManager
        }
    }
    
    // MARK: - Header
    
    private var headerView: some View {
        HStack {
            // Title
            VStack(alignment: .leading, spacing: 4) {
                Text("Edit Records")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundStyle(Color("TextPrimary"))
                
                Text(habit.name)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundStyle(Color("TextSecondary"))
            }
            
            Spacer()
            
            // Close button
            NeumorphicButton(icon: "xmark", size: 32) {
                dismiss()
            }
        }
    }
    
    // MARK: - Month Navigation
    
    private var monthNavigationView: some View {
        HStack {
            // Previous month button
            NeumorphicButton(icon: "chevron.left", size: 36) {
                viewModel?.goToPreviousMonth()
            }
            
            Spacer()
            
            // Month/Year display
            Text(viewModel?.currentMonthName ?? "")
                .font(.system(size: 16, weight: .semibold))
                .foregroundStyle(Color("TextPrimary"))
            
            Spacer()
            
            // Next month button
            NeumorphicButton(icon: "chevron.right", size: 36) {
                viewModel?.goToNextMonth()
            }
        }
    }
    
    // MARK: - Weekday Labels
    
    private var weekdayLabelsView: some View {
        let orderedDays = DayOfWeek.orderedCases(
            startingWithMonday: appearanceManager.weekStartsOnMonday)
        
        return HStack(spacing: 0) {
            ForEach(orderedDays, id: \.self) { day in
                Text(day.shortName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundStyle(Color("TextSecondary"))
                    .frame(maxWidth: .infinity)
            }
        }
    }
    
    // MARK: - Calendar Grid
    
    private var calendarGridView: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 6), count: 7), spacing: 6) {
            if let viewModel = viewModel {
                ForEach(viewModel.calendarDays, id: \.date) { calendarDay in
                    CalendarDayCell(
                        calendarDay: calendarDay,
                        habit: habit,
                        onTap: { date in
                            viewModel.toggleDateLevel(date)
                        }
                    )
                }
            }
        }
    }
}

#Preview("Calendar Records - Single Check Habit") {
    let sampleHabit = Habit(
        name: "Morning Walk",
        category: .fitness,
        timesPerDay: 1
    )
    
    CalendarRecordsView(habit: sampleHabit)
        .modelContainer(for: [Habit.self, HabitRecord.self], inMemory: true)
}

#Preview("Calendar Records - Multi Check Habit") {
    let sampleHabit = Habit(
        name: "Drink Water",
        category: .health,
        timesPerDay: 8
    )
    
    CalendarRecordsView(habit: sampleHabit)
        .modelContainer(for: [Habit.self, HabitRecord.self], inMemory: true)
}
