import SwiftUI

struct HabitRowView: View {
  let habit: Habit
  @Binding var showingHabitMenu: String?
  @Binding var scrollTrigger: Date?
  let onToggle: (Habit) -> Void
  let onEdit: (Habit) -> Void
  let onEditRecords: (Habit) -> Void
  let onArchive: (Habit) -> Void

  @Environment(\.colorScheme) private var colorScheme
  @Environment(\.modelContext) private var modelContext
  @Environment(HabitRepository.self) private var repository
  @Environment(AppearanceManager.self) private var appearanceManager

  var body: some View {
    HabitCard(isCompleted: habit.isCompletedToday) {
      VStack(spacing: 8) {
        // Habit header
        HStack(spacing: 8) {
          // Checkbox
          habitCheckbox

          // Habit info
          VStack(alignment: .leading, spacing: 2) {
            Text(habit.name)
                .font(.system(size: 18, weight: habit.isScheduledForToday ? .bold : .medium))
              .foregroundStyle(habitTitleColor)

            Text(habit.subtitle)
              .font(.system(size: 11))
              .foregroundStyle(Color("TextSecondary"))
          }

          Spacer()

          // Menu button
          NeumorphicButton(
            icon: "ellipsis",
            size: 32,
            isPressed: showingHabitMenu == habit.id.uuidString
          ) {
            withAnimation(.easeInOut(duration: 0.2)) {
              showingHabitMenu = showingHabitMenu == habit.id.uuidString ? nil : habit.id.uuidString
            }
          }
        }

        // Habit grid
        HabitGridView(
          habit: habit,
          showLabels: true,
          autoScroll: true,
          scrollTrigger: $scrollTrigger,
          isReadOnly: false
        )
      }
    }
    .overlay(alignment: .topTrailing) {
      // Dropdown menu as true overlay (doesn't affect card layout)
      if showingHabitMenu == habit.id.uuidString {
        habitDropdownMenu
          .offset(x: -8, y: 45)
          .zIndex(1000)  // Ensure it appears above everything
      }
    }
    .zIndex(showingHabitMenu == habit.id.uuidString ? 999 : 0)
  }

  private var habitCheckbox: some View {
    ProgressCheckButton(habit: habit, date: Date(), scale: 1.2)
  }

  private var habitDropdownMenu: some View {
    NeumorphicCard(cornerRadius: 12) {
      VStack(spacing: 0) {
        habitDropdownItem(
          icon: habit.isCompletedToday ? "arrow.uturn.backward" : "checkmark",
          title: habit.isCompletedToday ? "Undone" : "Mark Done",
          color: Color("Success")
        ) {
          // Mark Done/Undone logic: Always set to complete (timesPerDay) or reset (0)
          Task {
            do {
              let newLevel: Int16 = habit.isCompletedToday ? 0 : Int16(habit.timesPerDay)
              try await repository.addOrUpdateRecord(
                for: habit.id,
                date: Date(),
                completionLevel: newLevel
              )
              print("✅ Menu action completed: \(habit.name) set to level \(newLevel)")
            } catch {
              print("❌ Error in menu toggle for \(habit.name): \(error)")
            }
          }
          showingHabitMenu = nil
        }

        habitDropdownItem(
          icon: "pencil",
          title: "Edit Habit",
          color: Color("Info")
        ) {
          onEdit(habit)
          showingHabitMenu = nil
        }

        habitDropdownItem(
          icon: "calendar",
          title: "Edit Records",
          color: Color("Info")
        ) {
          onEditRecords(habit)
          showingHabitMenu = nil
        }

        habitDropdownItem(
          icon: "archivebox",
          title: "Archive",
          color: Color("Warning")
        ) {
          onArchive(habit)
          showingHabitMenu = nil
        }
      }
    }
    .frame(minWidth: 160)  // Use minimum width instead of fixed width
    .fixedSize(horizontal: true, vertical: false)  // Allow horizontal expansion to fit content
    .background(
      // Additional background to ensure visibility
      RoundedRectangle(cornerRadius: 12)
        .fill(Color("BackgroundPrimary"))
        .shadow(color: Color.black.opacity(0.3), radius: 8, x: 0, y: 4)
    )
    .transition(
      .asymmetric(
        insertion: .scale(scale: 0.8).combined(with: .opacity),
        removal: .scale(scale: 0.8).combined(with: .opacity)
      ))
  }

  private func habitDropdownItem(
    icon: String,
    title: String,
    color: Color,
    action: @escaping () -> Void
  ) -> some View {
    Button(action: action) {
      HStack(spacing: 12) {
        Image(systemName: icon)
          .font(.system(size: 12))
          .foregroundStyle(color)
          .frame(width: 16)

        Text(title)
          .font(.system(size: 14, weight: .medium))
          .foregroundStyle(Color("TextPrimary"))
          .lineLimit(1)  // Ensure single line
          .fixedSize(horizontal: true, vertical: false)  // Prevent text wrapping

        Spacer(minLength: 8)  // Minimum spacing at the end
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 14)  // Slightly more vertical padding for better touch targets
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
    .background(
      Rectangle()
        .fill(Color("TextMuted").opacity(0.1))
        .opacity(0)
    )
    .onHover { isHovered in
      // Add hover effect if needed
    }
  }

  /// Determines the color for the habit title based on schedule and grid color settings
  private var habitTitleColor: Color {
    if habit.isScheduledForToday && appearanceManager.useGridColor {
      // Use category color when scheduled for today AND grid color is enabled
      let colorName = GridColorService.getGridColor(
        for: habit,
        modelContext: modelContext,
        useGridColor: appearanceManager.useGridColor
      )
      return Color.fromHexOrAsset(colorName)
    } else {
      // Use subtitle color when not scheduled or grid color is disabled
      return Color("TextSecondary")
    }
  }

  private var habitModelForDashboard: Habit? {
    // No longer needed - pass habit directly to HabitGridView
    return habit
  }

  // Remove generateSampleRecords - use real data from habit.records
}

#Preview {
  struct PreviewWrapper: View {
    @State var showingMenu: String? = nil
    @State var scrollTrigger: Date? = nil
    var body: some View {
      // Create a sample habit for preview
      let sampleHabit = Habit(
        name: "Morning Walk",
        category: .fitness,
        timesPerDay: 1,
        frequency: .daily
      )

      HabitRowView(
        habit: sampleHabit,
        showingHabitMenu: $showingMenu,
        scrollTrigger: $scrollTrigger,
        onToggle: { _ in },
        onEdit: { _ in },
        onEditRecords: { _ in },
        onArchive: { _ in }
      )
      .padding()
      .background(Color("BackgroundPrimary"))
    }
  }
  return PreviewWrapper()
}
