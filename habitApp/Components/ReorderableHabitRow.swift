import SwiftUI

struct ReorderableHabitRow: View {
    let habit: Habit
    
    @Environment(\.modelContext) private var modelContext
    @Environment(AppearanceManager.self) private var appearanceManager
    
    var body: some View {
        HStack(spacing: 12) {
            // Drag Handle - more prominent
            VStack(spacing: 3) {
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                }
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                }
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                    Circle()
                        .fill(Color("TextSecondary"))
                        .frame(width: 3, height: 3)
                }
            }
            .frame(width: 20, height: 20)
            .padding(.leading, 4)
            
            // Category Icon with Color
            ZStack {
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.fromHexOrAsset(GridColorService.getGridColor(for: habit, modelContext: modelContext, useGridColor: true)))
                    .frame(width: 40, height: 40)
                    .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 3, x: 3, y: 3)
                    .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 3, x: -3, y: -3)
                
                Image(systemName: GridColorService.getHabitIcon(for: habit, modelContext: modelContext))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            
            // Habit Name and Info
            VStack(alignment: .leading, spacing: 4) {
                Text(habit.name)
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .foregroundColor(Color("TextPrimary"))
                    .lineLimit(1)
                
                Text(habit.category.rawValue)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color("TextSecondary"))
            }
            
            Spacer()
            
            // Visual indicator that this row is draggable
            Image(systemName: "arrow.up.arrow.down")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color("TextMuted").opacity(0.6))
                .padding(.trailing, 4)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color("BackgroundPrimary"))
                .shadow(color: Color("NeumorphicShadowDark").opacity(0.6), radius: 6, x: 6, y: 6)
                .shadow(color: Color("NeumorphicShadowLight").opacity(0.5), radius: 6, x: -6, y: -6)
        )
    }
}

#Preview {
    VStack(spacing: 12) {
        ReorderableHabitRow(habit: Habit(name: "Morning Walk", category: .fitness))
        ReorderableHabitRow(habit: Habit(name: "Read 1 Page", category: .learning))
        ReorderableHabitRow(habit: Habit(name: "Drink Water", category: .health))
    }
    .padding()
    .background(Color("BackgroundPrimary"))
}
