import SwiftUI

struct ArchiveConfirmationDialog: ViewModifier {
    let isPresented: Binding<Bool>
    let habitName: String
    let onConfirm: () -> Void
    
    func body(content: Content) -> some View {
        content
            .alert("Archive Habit", isPresented: isPresented) {
                <PERSON><PERSON>("Cancel", role: .cancel) {
                    // <PERSON><PERSON> will dismiss automatically
                }
                <PERSON><PERSON>("Archive", role: .destructive) {
                    onConfirm()
                }
            } message: {
                Text("Are you sure you want to archive \"\(habitName)\"? You can restore it later from the Archive section.")
            }
    }
}

extension View {
    func archiveConfirmationDialog(
        isPresented: Binding<Bool>,
        habitName: String,
        onConfirm: @escaping () -> Void
    ) -> some View {
        modifier(ArchiveConfirmationDialog(
            isPresented: isPresented,
            habitName: habitName,
            onConfirm: onConfirm
        ))
    }
}