import SwiftUI
import UIKit

// Helper function to validate SF Symbol availability
// Updated symbols in July 2024 to ensure iOS 17.0+ compatibility
// Replaced invalid symbols: pizza→birthday.cake.fill, hamburger→takeoutbag.and.cup.and.straw.fill, 
// taco→mug.fill, birthday.cake→gift.fill, cookie→circle.fill, snowcone→snow,
// ferry.fill→sailboat.fill, submarine→water.waves, compass.drawing→compass,
// seal→drop.fill, tornado→wind.snow, hurricane→cloud.rain
private func isValidSystemIcon(_ name: String) -> Bool {
    return UIImage(systemName: name) != nil
}

struct IconPickerView: View {
    @Binding var selectedIcon: String
    @State private var selectedCategory: IconCategory = .popular
    @State private var isExpanded: Bool = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with expand button
            HStack {
                Text("Choose Icon:")
                    .font(.system(size: 14))
                    .foregroundStyle(Color("TextSecondary"))
                
                Spacer()
                
                But<PERSON> {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: isExpanded ? "compress.alt" : "expand.alt")
                            .font(.system(size: 10))
                        Text(isExpanded ? "Collapse" : "Expand")
                            .font(.system(size: 10))
                    }
                    .foregroundStyle(Color("TextSecondary"))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color("BackgroundPrimary"))
                    .clipShape(RoundedRectangle(cornerRadius: 6))
                    .shadow(
                        color: Color("NeumorphicShadowDark").opacity(0.6),
                        radius: 2,
                        x: 1,
                        y: 1
                    )
                    .shadow(
                        color: Color("NeumorphicShadowLight").opacity(0.5),
                        radius: 2,
                        x: -1,
                        y: -1
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Category tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(IconCategory.allCases, id: \.self) { category in
                        IconCategoryButton(
                            category: category,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, 4)
            }
            
            // Icons grid
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(selectedCategory.icons, id: \.self) { icon in
                        IconButton(
                            icon: icon,
                            isSelected: selectedIcon == icon,
                            action: {
                                selectedIcon = icon
                            }
                        )
                    }
                }
                .padding(4)
            }
            .frame(maxHeight: isExpanded ? 250 : 180)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
}

struct IconCategoryButton: View {
    let category: IconCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("BackgroundPrimary"))
                
                if isSelected {
                    categoryButtonConcaveInnerShadows
                }
                
                Text(category.displayName)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                    .foregroundStyle(isSelected ? Color("Info") : Color("TextPrimary"))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .lineLimit(1)
            }
            .frame(height: 28)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
                radius: 2,
                x: isSelected ? 0 : 1,
                y: isSelected ? 0 : 1
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
                radius: 2,
                x: isSelected ? 0 : -1,
                y: isSelected ? 0 : -1
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    @ViewBuilder
    private var categoryButtonConcaveInnerShadows: some View {
        ZStack {
            // Top inner shadow
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.9), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 6)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Left inner shadow
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.9), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 6)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Bottom right highlights
            VStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 6)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            HStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 6)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .allowsHitTesting(false)
    }
}

struct IconButton: View {
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("BackgroundPrimary"))
                
                if isSelected {
                    concaveInnerShadows
                }
                
                Image(systemName: isValidSystemIcon(icon) ? icon : "questionmark.circle")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundStyle(isSelected ? Color("Info") : Color("TextPrimary"))
            }
            .frame(width: 40, height: 40)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
                radius: 3,
                x: isSelected ? 0 : 2,
                y: isSelected ? 0 : 2
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
                radius: 3,
                x: isSelected ? 0 : -2,
                y: isSelected ? 0 : -2
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    @ViewBuilder
    private var concaveInnerShadows: some View {
        ZStack {
            // Top inner shadow
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Left inner shadow
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Bottom right highlights
            VStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            HStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .allowsHitTesting(false)
    }
}


#Preview {
    @Previewable @State var selectedIcon = "star"
    
    return VStack {
        IconPickerView(selectedIcon: $selectedIcon)
            .padding()
        
        Text("Selected: \(selectedIcon)")
            .font(.caption)
    }
    .background(Color("BackgroundPrimary"))
}