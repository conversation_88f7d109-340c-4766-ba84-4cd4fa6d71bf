import SwiftUI
import UIKit

struct CustomCategoryModal: View {
    @Binding var isPresented: Bool
    let onCategoryCreated: (CustomCategory) -> Void
    let editingCategory: CustomCategory?
    
    @State private var categoryName: String = ""
    @State private var selectedIcon: String = "star"
    @State private var selectedColor: String = "#FF6B6B"
    @State private var selectedCategory: IconCategory = .popular
    @State private var isValid: Bool = false
    
    private var isEditMode: Bool {
        editingCategory != nil
    }
    
    // Convenience initializer for create mode
    init(isPresented: Binding<Bool>, onCategoryCreated: @escaping (CustomCategory) -> Void) {
        self._isPresented = isPresented
        self.onCategoryCreated = onCategoryCreated
        self.editingCategory = nil
    }
    
    // Full initializer for edit mode
    init(isPresented: Binding<Bool>, editingCategory: CustomCategory?, onCategoryCreated: @escaping (CustomCategory) -> Void) {
        self._isPresented = isPresented
        self.onCategoryCreated = onCategoryCreated
        self.editingCategory = editingCategory
    }
    
    private var canCreateCategory: Bool {
        !categoryName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !selectedIcon.isEmpty &&
        !selectedColor.isEmpty
    }
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }
            
            VStack {
                Spacer()
                
                // Modal content
                VStack(spacing: 16) {
                    // Header
                    header
                    
                    // Category Name Input  
                    categoryNameSection
                    
                    // Color Selection (moved above icon section)
                    colorSection
                    
                    // Icon Selection
                    iconSection
                    
                    // Create Button
                    createButton
                }
                .padding(24)
                .background(Color("BackgroundPrimary"))
                .clipShape(RoundedRectangle(cornerRadius: 20))
                .shadow(
                    color: Color("NeumorphicShadowDark").opacity(0.6),
                    radius: 15,
                    x: 0,
                    y: -5
                )
                .shadow(
                    color: Color("NeumorphicShadowLight").opacity(0.5),
                    radius: 15,
                    x: 0,
                    y: 5
                )
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
        }
        .transition(.move(edge: .bottom).combined(with: .opacity))
        .animation(.easeInOut(duration: 0.3), value: isPresented)
        .onChange(of: categoryName) { _, _ in validateForm() }
        .onChange(of: selectedIcon) { _, _ in validateForm() }
        .onChange(of: selectedColor) { _, _ in validateForm() }
        .onAppear {
            resetForm()
        }
    }
    
    // MARK: - Header
    private var header: some View {
        HStack {
            Text(isEditMode ? "Edit Category" : "Create Custom Category")
                .font(.system(size: 18, weight: .semibold))
                .foregroundStyle(Color("TextPrimary"))
            
            Spacer()
            
            NeumorphicButton(icon: "xmark", size: 32) {
                dismissModal()
            }
        }
    }
    
    // MARK: - Category Name Section
    private var categoryNameSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Category Name:")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextSecondary"))
            
            ZStack {
                // Base background
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color("BackgroundPrimary"))
                
                // Inset shadows
                VStack(spacing: 0) {
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                    .init(color: Color.clear, location: 1),
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(height: 10)
                    Spacer()
                }
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                HStack(spacing: 0) {
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: Color("NeumorphicShadowDark").opacity(0.7), location: 0),
                                    .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                    .init(color: Color.clear, location: 1),
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: 10)
                    Spacer()
                }
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                // Bottom-right highlights
                VStack(spacing: 0) {
                    Spacer()
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: Color.clear, location: 0),
                                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(height: 10)
                }
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                HStack(spacing: 0) {
                    Spacer()
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: Color.clear, location: 0),
                                    .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                    .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: 10)
                }
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                // Text field
                TextField("Enter category name", text: $categoryName)
                    .font(.system(size: 16))
                    .foregroundStyle(Color("TextPrimary"))
                    .padding(16)
                    .background(Color.clear)
                    .lineLimit(1)
                    .onChange(of: categoryName) { _, newValue in
                        // Limit to 20 characters
                        if newValue.count > 20 {
                            categoryName = String(newValue.prefix(20))
                        }
                    }
            }
            .frame(height: 48)
            .clipShape(RoundedRectangle(cornerRadius: 12))
            
            HStack {
                Text("\(categoryName.count)/20")
                    .font(.system(size: 12))
                    .foregroundStyle(Color("TextMuted"))
                
                Spacer()
                
                if !categoryName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 12))
                            .foregroundStyle(Color("Success"))
                        Text("Valid")
                            .font(.system(size: 12))
                            .foregroundStyle(Color("Success"))
                    }
                }
            }
        }
    }
    
    // MARK: - Icon Section
    private var iconSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Choose Icon:")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextSecondary"))
            
            // Category tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(IconCategory.allCases, id: \.self) { category in
                        CustomIconCategoryButton(
                            category: category,
                            isSelected: selectedCategory == category,
                            selectedColor: selectedColor
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, 4)
            }
            
            // Icons grid
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(selectedCategory.icons, id: \.self) { icon in
                        CustomIconButton(
                            icon: icon,
                            isSelected: selectedIcon == icon,
                            selectedColor: selectedColor,
                            action: {
                                selectedIcon = icon
                            }
                        )
                    }
                }
                .padding(4)
            }
            .frame(maxHeight: 200)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
    
    // MARK: - Color Section  
    private var colorSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Choose Color:")
                .font(.system(size: 14))
                .foregroundStyle(Color("TextSecondary"))
            
            // Colors grid with vertical scrolling
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(predefinedColors, id: \.hex) { color in
                        ColorButton(
                            color: color,
                            isSelected: selectedColor == color.hex,
                            action: {
                                selectedColor = color.hex
                            }
                        )
                    }
                }
                .padding(4)
            }
            .frame(maxHeight: 150)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
    
    // Predefined colors
    private let predefinedColors: [CategoryColor] = [
        // Row 1 - Reds
        CategoryColor(hex: "#FF6B6B", name: "Coral Red"),
        CategoryColor(hex: "#FF5252", name: "Bright Red"), 
        CategoryColor(hex: "#F44336", name: "Red"),
        CategoryColor(hex: "#E91E63", name: "Pink"),
        CategoryColor(hex: "#9C27B0", name: "Purple"),
        CategoryColor(hex: "#673AB7", name: "Deep Purple"),
        
        // Row 2 - Blues  
        CategoryColor(hex: "#3F51B5", name: "Indigo"),
        CategoryColor(hex: "#2196F3", name: "Blue"),
        CategoryColor(hex: "#03A9F4", name: "Light Blue"),
        CategoryColor(hex: "#00BCD4", name: "Cyan"),
        CategoryColor(hex: "#009688", name: "Teal"),
        CategoryColor(hex: "#4CAF50", name: "Green"),
        
        // Row 3 - Greens
        CategoryColor(hex: "#8BC34A", name: "Light Green"),
        CategoryColor(hex: "#CDDC39", name: "Lime"),
        CategoryColor(hex: "#FFEB3B", name: "Yellow"),
        CategoryColor(hex: "#FFC107", name: "Amber"),
        CategoryColor(hex: "#FF9800", name: "Orange"),
        CategoryColor(hex: "#FF5722", name: "Deep Orange"),
        
        // Row 4 - Extended colors
        CategoryColor(hex: "#795548", name: "Brown"),
        CategoryColor(hex: "#9E9E9E", name: "Grey"),
        CategoryColor(hex: "#607D8B", name: "Blue Grey"),
        CategoryColor(hex: "#FFD93D", name: "Banana Yellow"),
        CategoryColor(hex: "#6BCF7F", name: "Mint Green"),
        CategoryColor(hex: "#4D8FAC", name: "Steel Blue"),
        
        // Row 5 - More options
        CategoryColor(hex: "#A8E6CF", name: "Mint"),
        CategoryColor(hex: "#FFD3A5", name: "Peach"),
        CategoryColor(hex: "#FD9C85", name: "Salmon"),
        CategoryColor(hex: "#C9A96E", name: "Gold"),
        CategoryColor(hex: "#B4A7D6", name: "Lavender"),
        CategoryColor(hex: "#F7B6D3", name: "Rose")
    ]
    
    // MARK: - Create Button
    private var createButton: some View {
        Button {
            createCategory()
        } label: {
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(canCreateCategory ? Color("Success") : Color("BackgroundPrimary"))
                
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text(isEditMode ? "Update Category" : "Create Category")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundStyle(canCreateCategory ? .white : Color("TextPrimary"))
            }
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(canCreateCategory ? 0 : 0.6),
                radius: canCreateCategory ? 0 : 6,
                x: canCreateCategory ? 0 : 3,
                y: canCreateCategory ? 0 : 3
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(canCreateCategory ? 0 : 0.5),
                radius: canCreateCategory ? 0 : 6,
                x: canCreateCategory ? 0 : -3,
                y: canCreateCategory ? 0 : -3
            )
            .shadow(
                color: canCreateCategory ? Color("Success").opacity(0.4) : Color.clear,
                radius: 10,
                x: 0,
                y: 4
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canCreateCategory)
        .animation(.easeInOut(duration: 0.2), value: canCreateCategory)
    }
    
    // MARK: - Actions
    private func validateForm() {
        isValid = canCreateCategory
    }
    
    private func resetForm() {
        if let editingCategory = editingCategory {
            // Pre-populate with existing category data
            categoryName = editingCategory.name
            selectedIcon = editingCategory.iconName
            selectedColor = editingCategory.colorHex
            selectedCategory = CustomCategory.getIconCategory(for: editingCategory.iconName) ?? .popular
        } else {
            // Reset to defaults for create mode
            categoryName = ""
            selectedIcon = "star"
            selectedColor = "#FF6B6B"
            selectedCategory = .popular
        }
        isValid = false
    }
    
    private func dismissModal() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isPresented = false
        }
    }
    
    private func createCategory() {
        guard canCreateCategory else { return }
        
        let trimmedName = categoryName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if let editingCategory = editingCategory {
            // Update existing category
            editingCategory.name = trimmedName
            editingCategory.iconName = selectedIcon
            editingCategory.colorHex = selectedColor
            onCategoryCreated(editingCategory)
        } else {
            // Create new category
            let newCategory = CustomCategory(
                name: trimmedName,
                iconName: selectedIcon,
                colorHex: selectedColor
            )
            onCategoryCreated(newCategory)
        }
        
        dismissModal()
    }
}

// MARK: - Custom Components for Color Theming

struct CustomIconCategoryButton: View {
    let category: IconCategory
    let isSelected: Bool
    let selectedColor: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("BackgroundPrimary"))
                
                if isSelected {
                    categoryButtonConcaveInnerShadows
                }
                
                Text(category.displayName)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                    .foregroundStyle(isSelected ? Color(hex: selectedColor) ?? Color("Info") : Color("TextPrimary"))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .lineLimit(1)
            }
            .frame(height: 28)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
                radius: 2,
                x: isSelected ? 0 : 1,
                y: isSelected ? 0 : 1
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
                radius: 2,
                x: isSelected ? 0 : -1,
                y: isSelected ? 0 : -1
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    @ViewBuilder
    private var categoryButtonConcaveInnerShadows: some View {
        ZStack {
            // Top inner shadow
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.9), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 6)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Left inner shadow
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.9), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 6)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Bottom right highlights
            VStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 6)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            HStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 6)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .allowsHitTesting(false)
    }
}

struct CustomIconButton: View {
    let icon: String
    let isSelected: Bool
    let selectedColor: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("BackgroundPrimary"))
                
                if isSelected {
                    concaveInnerShadows
                }
                
                Image(systemName: isValidSystemIcon(icon) ? icon : "questionmark.circle")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundStyle(isSelected ? Color(hex: selectedColor) ?? Color("Info") : Color("TextPrimary"))
            }
            .frame(width: 40, height: 40)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
                radius: 3,
                x: isSelected ? 0 : 2,
                y: isSelected ? 0 : 2
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
                radius: 3,
                x: isSelected ? 0 : -2,
                y: isSelected ? 0 : -2
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    @ViewBuilder
    private var concaveInnerShadows: some View {
        ZStack {
            // Top inner shadow
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Left inner shadow
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Bottom right highlights
            VStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            HStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .allowsHitTesting(false)
    }
}

// Helper function to validate SF Symbol availability
private func isValidSystemIcon(_ name: String) -> Bool {
    return UIImage(systemName: name) != nil
}

#Preview {
    @Previewable @State var isPresented = true
    
    return CustomCategoryModal(isPresented: $isPresented) { category in
        print("Created category: \(category.name)")
    }
    .background(Color("BackgroundPrimary"))
}