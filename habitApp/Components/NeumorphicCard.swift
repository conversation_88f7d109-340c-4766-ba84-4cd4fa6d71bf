import SwiftUI

enum NeumorphicStyle {
  case convex  // Raised/elevated appearance
  case concave  // Inset/pressed appearance
}

struct NeumorphicCard<Content: View>: View {
  let content: Content
  let padding: CGFloat
  let cornerRadius: CGFloat
  let style: NeumorphicStyle

  init(
    padding: CGFloat = 12,
    cornerRadius: CGFloat = 20,
    style: NeumorphicStyle = .convex,
    @ViewBuilder content: () -> Content
  ) {
    self.content = content()
    self.padding = padding
    self.cornerRadius = cornerRadius
    self.style = style
  }

  var body: some View {
    ZStack {
      // Base background with concave shadows if needed
      if style == .concave {
        ZStack {
          RoundedRectangle(cornerRadius: cornerRadius)
            .fill(Color("BackgroundPrimary"))

          concaveInnerShadows
        }
      } else {
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(Color("BackgroundPrimary"))
      }

      // Content with padding (on top of shadows)
      content
        .padding(padding)
    }
    .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    .shadow(
      color: style == .convex ? Color("NeumorphicShadowDark").opacity(0.8) : Color.clear,
      radius: max(12, cornerRadius * 0.6),
      x: max(8, cornerRadius * 0.4),
      y: max(8, cornerRadius * 0.4)
    )
    .shadow(
      color: style == .convex ? Color("NeumorphicShadowLight").opacity(0.7) : Color.clear,
      radius: max(12, cornerRadius * 0.6),
      x: -max(8, cornerRadius * 0.4),
      y: -max(8, cornerRadius * 0.4)
    )
  }

  @ViewBuilder
  private var convexOuterShadows: some View {
    // For convex cards, we need outer shadows that extend beyond the card boundaries
    RoundedRectangle(cornerRadius: cornerRadius)
      .fill(Color.clear)
      .background(
        ZStack {
          // Bottom-right dark shadow (extends outward)
          RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
              RadialGradient(
                gradient: Gradient(stops: [
                  .init(color: Color("NeumorphicShadowDark").opacity(0.6), location: 0.3),
                  .init(color: Color("NeumorphicShadowDark").opacity(0.3), location: 0.7),
                  .init(color: Color.clear, location: 1.0),
                ]),
                center: UnitPoint(x: 0.8, y: 0.8),
                startRadius: 0,
                endRadius: max(20, cornerRadius * 1.2)
              )
            )
            .offset(x: max(4, cornerRadius * 0.2), y: max(4, cornerRadius * 0.2))

          // Top-left light highlight (extends outward)
          RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
              RadialGradient(
                gradient: Gradient(stops: [
                  .init(color: Color("NeumorphicShadowLight").opacity(0.8), location: 0.3),
                  .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 0.7),
                  .init(color: Color.clear, location: 1.0),
                ]),
                center: UnitPoint(x: 0.2, y: 0.2),
                startRadius: 0,
                endRadius: max(20, cornerRadius * 1.2)
              )
            )
            .offset(x: -max(4, cornerRadius * 0.2), y: -max(4, cornerRadius * 0.2))
        }
      )
      .allowsHitTesting(false)
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: max(15, cornerRadius * 0.75))

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: max(15, cornerRadius * 0.75))

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: max(15, cornerRadius * 0.75))
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: max(15, cornerRadius * 0.75))
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    }
    .allowsHitTesting(false)
  }

  @ViewBuilder
  private var convexEffect: some View {
    ZStack {
      // Base background
      RoundedRectangle(cornerRadius: cornerRadius)
        .fill(Color("BackgroundPrimary"))

      // Bottom edge shadow - Dark gradient from bottom edge (for raised effect)
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowDark").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: max(15, cornerRadius * 0.75))
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Right edge shadow - Dark gradient from right edge (for raised effect)
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowDark").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: max(15, cornerRadius * 0.75))
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Top edge highlight - Light gradient from top edge (for raised effect)
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowLight").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: max(15, cornerRadius * 0.75))

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Left edge highlight - Light gradient from left edge (for raised effect)
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowLight").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: max(15, cornerRadius * 0.75))

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    }
    .allowsHitTesting(false)
  }

}

struct HabitCard<Content: View>: View {
  let content: Content
  let isCompleted: Bool
  let cornerRadius: CGFloat

  init(
    isCompleted: Bool,
    cornerRadius: CGFloat = 15,
    @ViewBuilder content: () -> Content
  ) {
    self.content = content()
    self.isCompleted = isCompleted
    self.cornerRadius = cornerRadius
  }

  var body: some View {
    NeumorphicCard(
      padding: 12,
      cornerRadius: cornerRadius,
      style: isCompleted ? .convex : .concave
    ) {
      content
    }
  }
}

#Preview {
  VStack(spacing: 16) {
    // Convex card (raised)
    NeumorphicCard(style: .convex) {
      VStack(alignment: .leading) {
        Text("Convex Card")
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))
        Text("Raised appearance")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextSecondary"))
      }
    }

    // Concave card (inset)
    NeumorphicCard(style: .concave) {
      VStack(alignment: .leading) {
        Text("Concave Card")
          .font(.system(size: 16, weight: .semibold))
          .foregroundStyle(Color("TextPrimary"))
        Text("Inset/pressed appearance")
          .font(.system(size: 12))
          .foregroundStyle(Color("TextSecondary"))
      }
    }

    // Completed habit card
    HabitCard(isCompleted: true) {
      Text("Completed Habit")
        .font(.system(size: 14, weight: .medium))
        .foregroundStyle(Color("TextPrimary"))
    }

    // Incomplete habit card
    HabitCard(isCompleted: false) {
      Text("Incomplete Habit")
        .font(.system(size: 14, weight: .medium))
        .foregroundStyle(Color("TextPrimary"))
    }
  }
  .padding()
  .background(Color("BackgroundPrimary"))
}
