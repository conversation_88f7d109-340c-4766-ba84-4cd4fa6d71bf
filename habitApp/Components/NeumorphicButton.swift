import SwiftUI
import UIKit

struct NeumorphicButton: View {
  let icon: String
  let size: CGFloat
  let isPressed: Bool
  let action: () -> Void

  @State private var isPressedState = false

  init(
    icon: String,
    size: CGFloat = 40,
    isPressed: Bool = false,
    action: @escaping () -> Void
  ) {
    self.icon = icon
    self.size = size
    self.isPressed = isPressed
    self.action = action
  }

  var body: some View {
    Image(systemName: icon)
      .font(.system(size: size * 0.35, weight: .medium))
      .foregroundStyle(Color("TextPrimary"))
      .frame(width: size, height: size)
      .background(Color("BackgroundPrimary"))
      .clipShape(RoundedRectangle(cornerRadius: size * 0.375))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isPressedState || isPressed ? 0 : 0.6),
        radius: size * 0.125,
        x: size * 0.125,
        y: size * 0.125
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isPressedState || isPressed ? 0 : 0.5),
        radius: size * 0.125,
        x: -size * 0.125,
        y: -size * 0.125
      )
      .overlay {
        if isPressedState || isPressed {
          concaveInnerShadows
        }
      }
      .scaleEffect(isPressedState || isPressed ? 0.98 : 1.0)
      .animation(.easeInOut(duration: 0.15), value: isPressedState)
      .gesture(
        DragGesture(minimumDistance: 0)
          .onChanged { _ in
            if !isPressedState {
              isPressedState = true
            }
          }
          .onEnded { _ in
            action()
            isPressedState = false
          }
      )
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    let cornerRadius = size * 0.375
    let shadowSize = size * 0.3

    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowSize)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowSize)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowSize)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowSize)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    }
    .allowsHitTesting(false)
  }
}

struct NeumorphicGreenButton: View {
  let icon: String
  let size: CGFloat
  let action: () -> Void

  @State private var isPressedState = false

  init(
    icon: String,
    size: CGFloat = 40,
    action: @escaping () -> Void
  ) {
    self.icon = icon
    self.size = size
    self.action = action
  }

  var body: some View {
    Button(action: {
      // Add haptic feedback for the create button
      let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
      impactFeedback.impactOccurred()
      
      action()
    }) {
      Image(systemName: icon)
        .font(.system(size: size * 0.35, weight: .medium))
        .foregroundStyle(.white)
        .frame(width: size, height: size)
        .background(Color("Success"))
        .clipShape(RoundedRectangle(cornerRadius: size * 0.5))
        .shadow(
          color: Color("NeumorphicShadowDark").opacity(isPressedState ? 0 : 0.6),
          radius: size * 0.125,
          x: size * 0.125,
          y: size * 0.125
        )
        .shadow(
          color: Color("NeumorphicShadowLight").opacity(isPressedState ? 0 : 0.5),
          radius: size * 0.125,
          x: -size * 0.125,
          y: -size * 0.125
        )
        .overlay {
          if isPressedState {
            RoundedRectangle(cornerRadius: size * 0.5)
              .fill(Color("Success"))
              .shadow(
                color: Color("Success").opacity(0.3),
                radius: size * 0.075,
                x: size * 0.075,
                y: size * 0.075
              )
              .shadow(
                color: Color.white.opacity(0.1),
                radius: size * 0.075,
                x: -size * 0.075,
                y: -size * 0.075
              )
              .blendMode(.multiply)
          }
        }
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isPressedState ? 0.95 : 1.0)
    .offset(y: isPressedState ? 1 : -1)
    .animation(.easeInOut(duration: 0.15), value: isPressedState)
    .onLongPressGesture(
      minimumDuration: 0,
      maximumDistance: .infinity,
      pressing: { pressing in
        isPressedState = pressing
      },
      perform: {}
    )
  }
}

struct NeumorphicConcaveButton: View {
  let icon: String
  let text: String
  let size: CGFloat
  let isSelected: Bool
  let action: () -> Void

  init(
    icon: String = "",
    text: String,
    size: CGFloat = 40,
    isSelected: Bool = true,
    action: @escaping () -> Void
  ) {
    self.icon = icon
    self.text = text
    self.size = size
    self.isSelected = isSelected
    self.action = action
  }

  var body: some View {
    Button(action: action) {
      ZStack {
        // Base background
        RoundedRectangle(cornerRadius: size * 0.375)
          .fill(Color("BackgroundPrimary"))

        // Concave inner shadows when selected
        if isSelected {
          concaveInnerShadows
        }

        // Button content
        HStack(spacing: 8) {
          if !icon.isEmpty {
            Image(systemName: icon)
              .font(.system(size: size * 0.3, weight: .medium))
              .foregroundStyle(isSelected ? Color("Success") : Color("TextPrimary"))
          }

          Text(text)
            .font(.system(size: size * 0.35, weight: isSelected ? .bold : .medium))
            .foregroundStyle(isSelected ? Color("Success") : Color("TextPrimary"))
        }
        .padding(.horizontal, size * 0.4)
        .padding(.vertical, size * 0.3)
      }
      .frame(height: size)
      .clipShape(RoundedRectangle(cornerRadius: size * 0.375))
      .shadow(
        color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
        radius: size * 0.125,
        x: isSelected ? 0 : size * 0.075,
        y: isSelected ? 0 : size * 0.075
      )
      .shadow(
        color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
        radius: size * 0.125,
        x: isSelected ? 0 : -size * 0.075,
        y: isSelected ? 0 : -size * 0.075
      )
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isSelected ? 0.98 : 1.0)
    .animation(.easeInOut(duration: 0.15), value: isSelected)
  }

  @ViewBuilder
  private var concaveInnerShadows: some View {
    let cornerRadius = size * 0.375
    let shadowSize = size * 0.3

    ZStack {
      // Top inner shadow - Dark gradient from top edge
      VStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowSize)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Left inner shadow - Dark gradient from left edge
      HStack(spacing: 0) {
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                .init(color: Color.clear, location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowSize)

        Spacer()
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Bottom light highlight - Light gradient from bottom edge
      VStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: shadowSize)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))

      // Right light highlight - Light gradient from right edge
      HStack(spacing: 0) {
        Spacer()

        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(stops: [
                .init(color: Color.clear, location: 0),
                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .frame(width: shadowSize)
      }
      .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    }
    .allowsHitTesting(false)
  }
}

#Preview {
  VStack(spacing: 20) {
    NeumorphicButton(icon: "gear") {
      print("Settings tapped")
    }

    NeumorphicGreenButton(icon: "plus") {
      print("Add tapped")
    }

    NeumorphicButton(icon: "ellipsis", size: 32) {
      print("Menu tapped")
    }

    NeumorphicConcaveButton(icon: "checkmark", text: "Selected", size: 44, isSelected: true) {
      print("Concave button tapped")
    }

    NeumorphicConcaveButton(text: "Unselected", size: 44, isSelected: false) {
      print("Normal button tapped")
    }
  }
  .padding()
  .background(Color("BackgroundPrimary"))
}
