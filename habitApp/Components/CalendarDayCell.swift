import SwiftUI

struct CalendarDayCell: View {
    let calendarDay: CalendarDay
    let habit: Habit
    let onTap: (Date) -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    @Environment(AppearanceManager.self) private var appearanceManager
    
    var body: some View {
        Button {
            if canInteract {
                onTap(calendarDay.date)
            }
        } label: {
            ZStack {
                // Enhanced neumorphic background
                neumorphicBackground
                
                // Content overlay
                ZStack {
                    // Day number
                    Text("\(calendarDay.dayNumber)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundStyle(textColor)
                    
                    // Rocket icon for start date
                    if calendarDay.isStartDate && appearanceManager.showStartMark {
                        Text("🚀")
                            .font(.system(size: 12))
                            .shadow(color: .white.opacity(0.9), radius: 2)
                            .shadow(color: .black.opacity(0.7), radius: 1)
                    }
                }
                
                // Today border
                if calendarDay.isToday {
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red, lineWidth: 2)
                        .frame(width: 44, height: 44)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canInteract)
        .scaleEffect(canInteract ? 1.0 : 0.95)
        .animation(.easeInOut(duration: 0.2), value: calendarDay.level)
    }
    
    // MARK: - Computed Properties
    
    @ViewBuilder
    private var neumorphicBackground: some View {
        let hasNoRecords = calendarDay.level == 0 && canInteract
        let isConvex = calendarDay.level > 1 && canInteract
        
        ZStack {
            // Base background
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .frame(width: 44, height: 44)
            
            if hasNoRecords {
                // Concave (pressed/inset) effect for dates with no records (level = 0)
                concaveEffects
            } else if calendarDay.level == 1 {
                // Flat effect for level 1
                flatEffects
            } else if isConvex {
                // Convex (raised) effect for levels 2+
                convexEffects
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(
            color: Color("NeumorphicShadowDark").opacity(hasNoRecords ? 0 : 0.6),
            radius: 2,
            x: hasNoRecords ? 0 : 1,
            y: hasNoRecords ? 0 : 1
        )
        .shadow(
            color: Color("NeumorphicShadowLight").opacity(hasNoRecords ? 0 : 0.5),
            radius: 2,
            x: hasNoRecords ? 0 : -1,
            y: hasNoRecords ? 0 : -1
        )
    }
    
    @ViewBuilder
    private var concaveEffects: some View {
        ZStack {
            // Top inner shadow - Dark gradient from top edge
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 12)

                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))

            // Left inner shadow - Dark gradient from left edge
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.5), location: 0.4),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 12)

                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))

            // Bottom light highlight - Light gradient from bottom edge
            VStack(spacing: 0) {
                Spacer()

                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 12)
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))

            // Right light highlight - Light gradient from right edge
            HStack(spacing: 0) {
                Spacer()

                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.3), location: 0.6),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.6), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 12)
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .allowsHitTesting(false)
    }
    
    @ViewBuilder
    private var convexEffects: some View {
        ZStack {
            // Outer shadows for raised effect
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .frame(width: 44, height: 44)
                .shadow(
                    color: Color("NeumorphicShadowDark").opacity(0.4),
                    radius: 4,
                    x: 2,
                    y: 2
                )
                .shadow(
                    color: Color("NeumorphicShadowLight").opacity(0.8),
                    radius: 4,
                    x: -2,
                    y: -2
                )
            
            // Highlight gradient for convex appearance
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color("NeumorphicShadowLight").opacity(0.3),
                            Color.clear,
                            Color("NeumorphicShadowDark").opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 44, height: 44)
        }
    }
    
    @ViewBuilder
    private var flatEffects: some View {
        // Subtle shadow for flat but interactive state
        RoundedRectangle(cornerRadius: 12)
            .fill(backgroundColor)
            .frame(width: 44, height: 44)
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(0.15),
                radius: 2,
                x: 1,
                y: 1
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(0.3),
                radius: 2,
                x: -1,
                y: -1
            )
    }
    
    private var canInteract: Bool {
        calendarDay.isInCurrentMonth && !calendarDay.isFuture
    }
    
    private var backgroundColor: Color {
        if !calendarDay.isInCurrentMonth {
            return Color("BackgroundPrimary").opacity(0.3)
        }
        
        if calendarDay.isFuture {
            return Color.gray.opacity(0.1)
        }
        
        if calendarDay.level == 0 {
            return Color.gray.opacity(0.15) // No activity - gray
        } else {
            // Progressive completion using Success color
            let maxLevel = habit.timesPerDay
            let progressRatio = Double(calendarDay.level) / Double(maxLevel)
            return Color("Success").opacity(progressRatio)
        }
    }
    
    private var textColor: Color {
        if !calendarDay.isInCurrentMonth {
            return Color("TextMuted").opacity(0.5)
        }
        
        if calendarDay.isFuture {
            return Color("TextMuted")
        }
        
        if calendarDay.level == 0 {
            return Color("TextPrimary")
        } else {
            // White text on colored background for better contrast
            let maxLevel = habit.timesPerDay
            let progressRatio = Double(calendarDay.level) / Double(maxLevel)
            return progressRatio > 0.5 ? Color.white : Color("TextPrimary")
        }
    }
}

#Preview("Calendar Day States") {
    let sampleHabit = Habit(name: "Test", category: .health, timesPerDay: 3)
    let today = Date()
    let calendar = Calendar.current
    
    VStack(spacing: 16) {
        Text("Calendar Day Cell States")
            .font(.headline)
        
        HStack(spacing: 16) {
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 15,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: false,
                        isStartDate: false,
                        level: 0
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Level 0\n(No activity)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 16,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: false,
                        isStartDate: false,
                        level: 1
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Level 1\n(1/3 progress)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 17,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: false,
                        isStartDate: false,
                        level: 2
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Level 2\n(2/3 progress)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 18,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: false,
                        isStartDate: false,
                        level: 3
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Level 3\n(Completed)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
        }
        
        HStack(spacing: 16) {
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 19,
                        isInCurrentMonth: true,
                        isToday: true,
                        isFuture: false,
                        isStartDate: false,
                        level: 2
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Today\n(Red border)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 20,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: false,
                        isStartDate: true,
                        level: 3
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Start Date\n(🚀 icon)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: calendar.date(byAdding: .day, value: 1, to: today) ?? today,
                        dayNumber: 21,
                        isInCurrentMonth: true,
                        isToday: false,
                        isFuture: true,
                        isStartDate: false,
                        level: 0
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Future\n(Disabled)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
            
            VStack {
                CalendarDayCell(
                    calendarDay: CalendarDay(
                        date: today,
                        dayNumber: 22,
                        isInCurrentMonth: false,
                        isToday: false,
                        isFuture: false,
                        isStartDate: false,
                        level: 0
                    ),
                    habit: sampleHabit,
                    onTap: { _ in }
                )
                Text("Other Month\n(Faded)")
                    .font(.caption)
                    .multilineTextAlignment(.center)
            }
        }
    }
    .padding()
    .background(Color("BackgroundPrimary"))
}
