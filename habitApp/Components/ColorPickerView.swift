import SwiftUI

struct ColorPickerView: View {
    @Binding var selectedColor: String
    @State private var isExpanded: Bool = false
    
    private let predefinedColors: [CategoryColor] = [
        // Row 1 - Reds
        CategoryColor(hex: "#FF6B6B", name: "Coral Red"),
        CategoryColor(hex: "#FF5252", name: "<PERSON> Red"), 
        CategoryColor(hex: "#F44336", name: "Red"),
        CategoryColor(hex: "#E91E63", name: "Pink"),
        CategoryColor(hex: "#9C27B0", name: "<PERSON>"),
        CategoryColor(hex: "#673AB7", name: "<PERSON> Purple"),
        
        // Row 2 - Blues  
        CategoryColor(hex: "#3F51B5", name: "Indigo"),
        CategoryColor(hex: "#2196F3", name: "Blue"),
        CategoryColor(hex: "#03A9F4", name: "Light Blue"),
        CategoryColor(hex: "#00BCD4", name: "<PERSON><PERSON>"),
        CategoryColor(hex: "#009688", name: "Tea<PERSON>"),
        CategoryColor(hex: "#4CAF50", name: "<PERSON>"),
        
        // Row 3 - Greens
        CategoryColor(hex: "#8BC34A", name: "Light Green"),
        CategoryColor(hex: "#CDDC39", name: "Lime"),
        CategoryColor(hex: "#FFEB3B", name: "Yellow"),
        CategoryColor(hex: "#FFC107", name: "Amber"),
        CategoryColor(hex: "#FF9800", name: "Orange"),
        CategoryColor(hex: "#FF5722", name: "Deep Orange"),
        
        // Row 4 - Extended colors
        CategoryColor(hex: "#795548", name: "Brown"),
        CategoryColor(hex: "#9E9E9E", name: "Grey"),
        CategoryColor(hex: "#607D8B", name: "Blue Grey"),
        CategoryColor(hex: "#FFD93D", name: "Banana Yellow"),
        CategoryColor(hex: "#6BCF7F", name: "Mint Green"),
        CategoryColor(hex: "#4D8FAC", name: "Steel Blue"),
        
        // Row 5 - More options when expanded
        CategoryColor(hex: "#A8E6CF", name: "Mint"),
        CategoryColor(hex: "#FFD3A5", name: "Peach"),
        CategoryColor(hex: "#FD9C85", name: "Salmon"),
        CategoryColor(hex: "#C9A96E", name: "Gold"),
        CategoryColor(hex: "#B4A7D6", name: "Lavender"),
        CategoryColor(hex: "#F7B6D3", name: "Rose")
    ]
    
    private var visibleColors: [CategoryColor] {
        isExpanded ? predefinedColors : Array(predefinedColors.prefix(18))
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with expand button
            HStack {
                Text("Choose Color:")
                    .font(.system(size: 14))
                    .foregroundStyle(Color("TextSecondary"))
                
                Spacer()
                
                Text("\(predefinedColors.count) colors available")
                    .font(.system(size: 12))
                    .foregroundStyle(Color("TextMuted"))
                
                Button {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: isExpanded ? "compress.alt" : "expand.alt")
                            .font(.system(size: 10))
                        Text(isExpanded ? "Collapse" : "Expand")
                            .font(.system(size: 10))
                    }
                    .foregroundStyle(Color("TextSecondary"))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color("BackgroundPrimary"))
                    .clipShape(RoundedRectangle(cornerRadius: 6))
                    .shadow(
                        color: Color("NeumorphicShadowDark").opacity(0.6),
                        radius: 2,
                        x: 1,
                        y: 1
                    )
                    .shadow(
                        color: Color("NeumorphicShadowLight").opacity(0.5),
                        radius: 2,
                        x: -1,
                        y: -1
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Colors grid
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(visibleColors, id: \.hex) { color in
                        ColorButton(
                            color: color,
                            isSelected: selectedColor == color.hex,
                            action: {
                                selectedColor = color.hex
                            }
                        )
                    }
                }
                .padding(4)
            }
            .frame(maxHeight: isExpanded ? 220 : 130)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
}

struct ColorButton: View {
    let color: CategoryColor
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("BackgroundPrimary"))
                
                if isSelected {
                    concaveInnerShadows
                }
                
                Circle()
                    .fill(Color(hex: color.hex) ?? Color.gray)
                    .frame(width: 24, height: 24)
                    .overlay {
                        if isSelected {
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                                .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                        }
                    }
            }
            .frame(width: 40, height: 40)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(
                color: Color("NeumorphicShadowDark").opacity(isSelected ? 0 : 0.6),
                radius: 3,
                x: isSelected ? 0 : 2,
                y: isSelected ? 0 : 2
            )
            .shadow(
                color: Color("NeumorphicShadowLight").opacity(isSelected ? 0 : 0.5),
                radius: 3,
                x: isSelected ? 0 : -2,
                y: isSelected ? 0 : -2
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    @ViewBuilder
    private var concaveInnerShadows: some View {
        ZStack {
            // Top inner shadow
            VStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Left inner shadow
            HStack(spacing: 0) {
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color("NeumorphicShadowDark").opacity(0.8), location: 0),
                                .init(color: Color("NeumorphicShadowDark").opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
                Spacer()
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Bottom right highlights
            VStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(height: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            HStack(spacing: 0) {
                Spacer()
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.clear, location: 0),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.2), location: 0.5),
                                .init(color: Color("NeumorphicShadowLight").opacity(0.4), location: 1),
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .allowsHitTesting(false)
    }
}

struct CategoryColor {
    let hex: String
    let name: String
    
    var color: Color {
        Color(hex: hex) ?? Color.gray
    }
}

// Color extension to support hex initialization
extension Color {
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    @Previewable @State var selectedColor = "#FF6B6B"
    
    return VStack {
        ColorPickerView(selectedColor: $selectedColor)
            .padding()
        
        HStack {
            Text("Selected:")
                .font(.caption)
            Circle()
                .fill(Color(hex: selectedColor) ?? Color.gray)
                .frame(width: 20, height: 20)
            Text(selectedColor)
                .font(.caption)
        }
    }
    .background(Color("BackgroundPrimary"))
}