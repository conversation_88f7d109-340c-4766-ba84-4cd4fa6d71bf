import SwiftUI
import UIKit

struct ProgressCheckButton: View {
    let habit: Habit
    let date: Date
    let scale: CGFloat
    @State private var currentLevel: Int16 = 0
    @State private var isUpdating = false
    @State private var displayLevel: Int16 = 0  // For instant UI updates
    @Environment(\.modelContext) private var context
    @Environment(HabitRepository.self) private var repository
    @Environment(\.colorScheme) private var colorScheme
    @Environment(AppearanceManager.self) private var appearanceManager
    
    init(habit: Habit, date: Date, scale: CGFloat = 1.0) {
        self.habit = habit
        self.date = date
        self.scale = scale
    }
    
    // Track habit's todayCompletionLevel to detect external changes
    private var habitTodayLevel: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // Only use habit's reactive property if the date is today
        if calendar.isDate(date, inSameDayAs: today) {
            return habit.todayCompletionLevel
        } else {
            // For other dates, rely on our cached currentLevel
            return Int(currentLevel)
        }
    }
    
    var body: some View {
        Button(action: toggleCompletion) {
            ZStack {
                // SINGLE SOURCE OF TRUTH - all calculations based on displayLevel only
                let progress = Double(displayLevel) / Double(habit.timesPerDay)
                let hasProgress = displayLevel > 0
                let progressOpacity = habit.timesPerDay > 0 ? progress : 0.0
                
                // SHARED color object for perfect sync
                let successColor = Color("Success")
                
                // Both ring and background tied to same state and timing
                let backgroundColor: Color = hasProgress ? 
                    successColor.opacity(progressOpacity) : 
                    (colorScheme == .dark ? Color(red: 150/255, green: 150/255, blue: 150/255) : Color("BackgroundPrimary"))
                
                let ringStrokeColor = hasProgress ? successColor : successColor.opacity(0.0)
                
                // Background ring (always visible but subtle)
                Circle()
                    .stroke(Color("TextMuted").opacity(0.2), lineWidth: 2 * scale)
                    .frame(width: 38 * scale, height: 38 * scale)
                
                // Progress ring - using SHARED color object for perfect sync
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(ringStrokeColor, style: StrokeStyle(lineWidth: 2 * scale, lineCap: .round))
                    .frame(width: 38 * scale, height: 38 * scale)
                    .rotationEffect(.degrees(-90)) // Start from top
                
                // Checkbox background - using pre-calculated values
                Circle()
                    .fill(backgroundColor)
                    .frame(width: 32 * scale, height: 32 * scale)
                    .overlay {
                        // Add inner shadow for depth
                        if displayLevel > 0 {
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.black.opacity(0.2),
                                            Color.clear,
                                            Color.white.opacity(0.3)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                                .blendMode(.multiply)
                        }
                    }
                
                // Checkmark, progress counter, or category icon - unified logic for all habits
                if displayLevel >= Int16(habit.timesPerDay) {
                    // Show checkmark when at or above completion threshold
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundStyle(.white)
                } else if displayLevel > 0 {
                    // Show progress number when partially complete
                    Text("\(displayLevel)")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundStyle(.white)
                } else {
                    // When displayLevel == 0, show habit category icon
                    let iconColor = appearanceManager.useGridColor ? 
                        Color.fromHexOrAsset(GridColorService.getGridColor(for: habit, modelContext: context, useGridColor: true)) : 
                        (colorScheme == .dark ? .white : .black)
                    
                    Image(systemName: GridColorService.getHabitIcon(for: habit, modelContext: context))
                        .font(.system(size: 18, weight: .bold))
                        .foregroundStyle(iconColor)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(0.9 * scale)
        .disabled(isUpdating)
        .task(id: "\(habit.id)-\(Calendar.current.startOfDay(for: date))") {
            await loadCurrentLevel()
        }
        .onChange(of: habitTodayLevel) { oldValue, newValue in
            // Update currentLevel when habit's data changes externally (e.g., from menu actions)
            let calendar = Calendar.current
            let today = calendar.startOfDay(for: Date())
            
            if calendar.isDate(date, inSameDayAs: today) && Int16(newValue) != currentLevel {
                currentLevel = Int16(newValue)
                displayLevel = Int16(newValue)  // Sync display level
            }
        }
    }
    
    private func toggleCompletion() {
        // Prevent multiple rapid taps
        guard !isUpdating else { return }
        
        let newLevel: Int16
        
        if currentLevel >= Int16(habit.timesPerDay) {
            // Reset to 0 if at max
            newLevel = 0
            // Single light vibration for unchecking
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        } else {
            // Increment by 1
            newLevel = currentLevel + 1
            
            // Check if this increment will complete the habit
            if newLevel >= Int16(habit.timesPerDay) {
                // Double heavy vibration for completion
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    impactFeedback.impactOccurred()
                }
            } else {
                // Single light vibration for progress
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        }
        
        // FORCE SINGLE ATOMIC UPDATE - all visual elements update together
        withAnimation(.none) {
            displayLevel = newLevel
        }
        currentLevel = newLevel
        isUpdating = true
        
        // Background task for persistence
        Task {
            do {
                try await repository.addOrUpdateRecord(
                    for: habit.id,
                    date: date,
                    completionLevel: newLevel
                )
            } catch {
                // Revert on error with instant feedback
                await MainActor.run {
                    let revertedLevel = currentLevel == 0 ? Int16(habit.timesPerDay) : currentLevel - 1
                    displayLevel = revertedLevel  // Instant visual revert
                    currentLevel = revertedLevel
                }
                print("Error updating habit completion: \(error)")
            }
            
            await MainActor.run {
                isUpdating = false
            }
        }
    }
    
    private func loadCurrentLevel() async {
        if let record = await repository.getRecord(for: habit.id, on: date) {
            await MainActor.run {
                currentLevel = record.completionLevel
                displayLevel = record.completionLevel  // Sync display level
            }
        } else {
            await MainActor.run {
                currentLevel = 0
                displayLevel = 0  // Sync display level
            }
        }
    }
    
    // MARK: - Style Helpers
    
    private var instantCheckboxColor: Color {
        if displayLevel == 0 {
            return colorScheme == .dark ? 
                Color(red: 150/255, green: 150/255, blue: 150/255) : 
                Color("BackgroundPrimary")
        } else {
            return Color("Success").opacity(instantProgressOpacity)
        }
    }
    
    private var instantProgressOpacity: Double {
        guard habit.timesPerDay > 0 else { return 0.0 }
        let progress = Double(displayLevel) / Double(habit.timesPerDay)
        return progress // Use exact ratio, same as grid logic
    }
    
    /// Determines the color for the category icon when task level is 0
    private var instantCategoryIconColor: Color {
        if appearanceManager.useGridColor {
            // Use category color when grid color setting is ON
            return Color.fromHexOrAsset(GridColorService.getGridColor(for: habit, modelContext: context, useGridColor: true))
        } else {
            // Use black/white based on color scheme when grid color setting is OFF
            return colorScheme == .dark ? .white : .black
        }
    }
    
    // Legacy computed properties (kept for compatibility)
    private var progressiveCheckboxColor: Color {
        if currentLevel == 0 {
            return colorScheme == .dark ? 
                Color(red: 150/255, green: 150/255, blue: 150/255) : 
                Color("BackgroundPrimary")
        } else {
            return Color("Success").opacity(progressOpacity)
        }
    }
    
    private var progressOpacity: Double {
        guard habit.timesPerDay > 0 else { return 0.0 }
        let progress = Double(currentLevel) / Double(habit.timesPerDay)
        return progress // Use exact ratio, same as grid logic
    }
    
    /// Determines the color for the category icon when task level is 0
    private var categoryIconColor: Color {
        if appearanceManager.useGridColor {
            // Use category color when grid color setting is ON
            return Color.fromHexOrAsset(GridColorService.getGridColor(for: habit, modelContext: context, useGridColor: true))
        } else {
            // Use black/white based on color scheme when grid color setting is OFF
            return colorScheme == .dark ? .white : .black
        }
    }
}

#Preview("Progress Check Button") {
    let sampleHabit = Habit(
        name: "Morning Walk",
        category: .fitness,
        timesPerDay: 3,
        frequency: .daily
    )
    
    VStack(spacing: 20) {
        ProgressCheckButton(habit: sampleHabit, date: Date())
        ProgressCheckButton(habit: sampleHabit, date: Date(), scale: 1.2)
        ProgressCheckButton(habit: sampleHabit, date: Date(), scale: 1.5)
    }
    .padding()
    .background(Color("BackgroundPrimary"))
}
