import Foundation
import SwiftData


@Model
final class CustomCategory {
    var id: UUID = UUID()
    var name: String = ""
    var iconName: String = "star"
    var colorHex: String = "#FF6B6B"
    var createdAt: Date = Date()
    
    init(name: String, iconName: String, colorHex: String) {
        self.name = name
        self.iconName = iconName
        self.colorHex = colorHex
        self.createdAt = Date()
    }
    
    // MARK: - Computed Properties
    
    var displayColor: String {
        // Map hex colors to app's color names for consistency
        switch colorHex.uppercased() {
        case "#FF6B6B", "#FF383C": return "Success"
        case "#4ECDC4", "#00C8B3": return "Wellness"  
        case "#45B7D1", "#0088FF": return "Info"
        case "#96CEB4": return "Success"
        case "#FFEAA7", "#FFD93D": return "Warning"
        case "#DDA0DD", "#CB30E0": return "Mindfulness"
        case "#98D8C8": return "Wellness"
        case "#F7DC6F": return "Warning"
        case "#BB8FCE": return "Mindfulness"
        case "#85C1E9": return "Info"
        default: return "TextPrimary"
        }
    }
    
    // For category grid display consistency
    var categoryType: CategoryType {
        return .custom(self)
    }
    
    // MARK: - Icon Category Support
    
    static func getAllAvailableIcons() -> [String: [String]] {
        var categorizedIcons: [String: [String]] = [:]
        for category in IconCategory.allCases {
            categorizedIcons[category.displayName] = category.icons
        }
        return categorizedIcons
    }
    
    static func getIconCategory(for iconName: String) -> IconCategory? {
        return IconCategory.allCases.first { category in
            category.icons.contains(iconName)
        }
    }
    
    static func getAllIconsFlat() -> [String] {
        return IconCategory.allCases.flatMap { $0.icons }
    }
}

// MARK: - Category Type Enum

enum CategoryType: Hashable {
    case predefined(HabitCategory)
    case custom(CustomCategory)
    
    var id: String {
        switch self {
        case .predefined(let category):
            return "predefined_\(category.rawValue)"
        case .custom(let category):
            return "custom_\(category.id.uuidString)"
        }
    }
    
    var name: String {
        switch self {
        case .predefined(let category):
            return category.rawValue
        case .custom(let category):
            return category.name
        }
    }
    
    var icon: String {
        switch self {
        case .predefined(let category):
            return category.icon
        case .custom(let category):
            return category.iconName
        }
    }
    
    var color: String {
        switch self {
        case .predefined(let category):
            return category.color
        case .custom(let category):
            return category.displayColor
        }
    }
    
    var examples: [String] {
        switch self {
        case .predefined(let category):
            return category.examples
        case .custom:
            return ["Create your own mini habit!", "Keep it small and consistent", "Build the habit first"]
        }
    }
    
    // For storing in Habit model
    var storageValue: String {
        switch self {
        case .predefined(let category):
            return category.rawValue
        case .custom(let category):
            return "custom_\(category.id.uuidString)"
        }
    }
    
    static func == (lhs: CategoryType, rhs: CategoryType) -> Bool {
        lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}