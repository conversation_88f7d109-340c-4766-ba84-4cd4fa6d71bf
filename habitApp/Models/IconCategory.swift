import Foundation

enum IconCategory: CaseIterable {
    case popular
    case general
    case nature
    case tech
    case finance
    case people
    case travel
    case shapes
    case arrows
    case input
    case health
    case leisure
    
    var displayName: String {
        switch self {
        case .popular: return "Popular"
        case .shapes: return "Shapes"
        case .arrows: return "Arrows"
        case .input: return "Input"
        case .tech: return "Tech"
        case .nature: return "Nature"
        case .finance: return "Finance"
        case .general: return "General"
        case .people: return "People"
        case .travel: return "Travel"
        case .health: return "Health"
        case .leisure: return "Leisure"
        }
    }
    
    var icons: [String] {
        switch self {
        case .popular:
            return ["star.fill", "star", "checkmark", "xmark", "bell", "bell.fill", "heart", "heart.fill", "exclamationmark.triangle", "exclamationmark", "exclamationmark.bubble", "exclamationmark.bubble.fill", "exclamationmark.circle", "exclamationmark.circle.fill", "exclamationmark.icloud", "exclamationmark.icloud.fill", "exclamationmark.octagon", "exclamationmark.octagon.fill", "exclamationmark.square", "exclamationmark.square.fill", "exclamationmark.triangle.fill", "info", "info.circle", "info.circle.fill", "questionmark", "questionmark.circle", "questionmark.circle.fill", "questionmark.diamond", "questionmark.diamond.fill", "questionmark.square", "questionmark.square.fill", "questionmark.video", "questionmark.video.fill", "questionmark.video.fill.rtl", "questionmark.video.rtl", "smiley", "smiley.fill", "sparkles", "xmark.circle", "xmark.circle.fill", "xmark.icloud", "xmark.icloud.fill", "xmark.octagon", "xmark.octagon.fill", "xmark.rectangle", "xmark.rectangle.fill", "xmark.seal", "xmark.seal.fill", "xmark.square", "xmark.square.fill"]
            
        case .shapes:
            return ["circle", "square", "circle.fill", "square.fill", "arrowtriangle.up.fill", "arrowtriangle.right.fill", "arrowtriangle.down.fill", "arrowtriangle.left.fill", "rhombus", "rhombus.fill", "triangle", "triangle.fill", "triangle.lefthalf.fill", "triangle.righthalf.fill"]
            
        case .arrows:
            return ["arrow.left", "arrow.up", "arrow.right", "arrow.down", "arrow.left.and.right", "arrow.up.left", "arrow.up.right", "arrow.down.right", "arrow.down.left", "arrow.counterclockwise", "arrow.clockwise", "arrow.up.arrow.down", "arrow.up.to.line.alt", "arrow.down.to.line.alt", "arrow.turn.right.up", "arrow.turn.right.down", "arrow.turn.down.left", "arrow.turn.down.right", "arrow.turn.left.up", "arrow.turn.left.down", "arrow.turn.up.left", "arrow.turn.up.right", "arrow.2.circlepath", "arrow.2.circlepath.circle", "arrow.2.circlepath.circle.fill", "arrow.2.squarepath", "arrow.3.trianglepath", "arrow.clockwise.circle", "arrow.clockwise.circle.fill", "arrow.clockwise.icloud", "arrow.clockwise.icloud.fill", "arrow.counterclockwise.circle", "arrow.counterclockwise.circle.fill", "arrow.counterclockwise.icloud", "arrow.counterclockwise.icloud.fill", "arrow.down.circle", "arrow.down.circle.fill", "arrow.down.doc", "arrow.down.doc.fill", "arrow.down.left.circle", "arrow.down.left.circle.fill", "arrow.down.left.square", "arrow.down.left.square.fill", "arrow.down.left.video", "arrow.down.left.video.fill", "arrow.down.right.and.arrow.up.left", "arrow.down.right.circle", "arrow.down.right.circle.fill", "arrow.down.right.square", "arrow.down.right.square.fill", "arrow.down.square", "arrow.down.square.fill", "arrow.down.to.line", "arrow.left.and.right.circle", "arrow.left.and.right.circle.fill", "arrow.left.and.right.square", "arrow.left.and.right.square.fill", "arrow.left.circle", "arrow.left.circle.fill", "arrow.left.square", "arrow.left.square.fill", "arrow.left.to.line", "arrow.left.to.line.alt", "arrow.merge", "arrow.right.arrow.left", "arrow.right.arrow.left.circle", "arrow.right.arrow.left.circle.fill", "arrow.right.arrow.left.square", "arrow.right.arrow.left.square.fill", "arrow.right.circle", "arrow.right.circle.fill", "arrow.right.square", "arrow.right.square.fill", "arrow.right.to.line", "arrow.right.to.line.alt", "arrow.swap", "arrow.up.and.down", "arrow.up.and.down.circle", "arrow.up.and.down.circle.fill", "arrow.up.and.down.square", "arrow.up.and.down.square.fill", "arrow.up.arrow.down.circle", "arrow.up.arrow.down.circle.fill", "arrow.up.arrow.down.square", "arrow.up.arrow.down.square.fill", "arrow.up.bin", "arrow.up.bin.fill", "arrow.up.circle", "arrow.up.circle.fill", "arrow.up.doc", "arrow.up.doc.fill", "arrow.up.left.and.arrow.down.right", "arrow.up.left.circle", "arrow.up.left.circle.fill", "arrow.up.left.square", "arrow.up.left.square.fill", "arrow.up.right.circle.fill", "arrow.up.right.diamond", "arrow.up.right.diamond.fill", "arrow.up.right.square", "arrow.up.right.square.fill", "arrow.up.right.video", "arrow.up.right.video.fill", "arrow.up.square", "arrow.up.square.fill", "arrow.up.to.line", "arrow.upright.circle", "arrow.uturn.down", "arrow.uturn.down.circle", "arrow.uturn.down.circle.fill", "arrow.uturn.down.square", "arrow.uturn.down.square.fill", "arrow.uturn.left", "arrow.uturn.left.circle", "arrow.uturn.left.circle.fill", "arrow.uturn.left.square", "arrow.uturn.left.square.fill", "arrow.uturn.right", "arrow.uturn.right.circle", "arrow.uturn.right.circle.fill", "arrow.uturn.right.square", "arrow.uturn.right.square.fill", "arrow.uturn.up", "arrow.uturn.up.circle", "arrow.uturn.up.circle.fill", "arrow.uturn.up.square", "arrow.uturn.up.square.fill", "arrowshape.turn.up.left", "arrowshape.turn.up.left.2", "arrowshape.turn.up.left.2.fill", "arrowshape.turn.up.left.circle", "arrowshape.turn.up.left.circle.fill", "arrowshape.turn.up.left.fill", "arrowshape.turn.up.right", "arrowshape.turn.up.right.circle", "arrowshape.turn.up.right.circle.fill", "arrowshape.turn.up.right.fill", "arrowtriangle.down", "arrowtriangle.down.circle", "arrowtriangle.down.circle.fill", "arrowtriangle.down.square", "arrowtriangle.down.square.fill", "arrowtriangle.left", "arrowtriangle.left.circle", "arrowtriangle.left.circle.fill", "arrowtriangle.left.square", "arrowtriangle.left.square.fill", "arrowtriangle.right", "arrowtriangle.right.circle", "arrowtriangle.right.circle.fill", "arrowtriangle.right.square", "arrowtriangle.right.square.fill", "arrowtriangle.up", "arrowtriangle.up.circle", "arrowtriangle.up.circle.fill", "arrowtriangle.up.square", "arrowtriangle.up.square.fill"]
            
        case .input:
            return ["shift", "capslock", "control", "projective", "command", "option", "delete.right", "clear", "delete.left", "alt", "helm", "escape", "return", "eject", "1.circle", "2.circle", "3.circle", "4.circle", "5.circle", "6.circle", "7.circle", "8.circle", "9.circle", "19.circle", "a.circle", "b.circle", "c.circle", "d.circle", "e.circle", "f.circle", "g.circle", "h.circle", "i.circle", "j.circle", "k.circle", "l.circle", "m.circle", "n.circle", "o.circle", "p.circle", "q.circle", "r.circle", "s.circle", "t.circle", "u.circle", "v.circle", "w.circle", "x.circle", "y.circle", "z.circle", "0.circle", "19.circle.fill", "0.circle.fill", "1.circle.fill", "2.circle.fill", "3.circle.fill", "4.circle.fill", "5.circle.fill", "6.circle.fill", "7.circle.fill", "8.circle.fill", "9.circle.fill", "a.square", "b.square", "c.square", "d.square", "e.square", "f.square", "g.square", "h.square", "i.square", "j.square", "k.square", "l.square", "m.square", "n.square", "o.square", "p.square", "q.square", "r.square", "s.square", "t.square", "u.square", "v.square", "w.square", "x.square", "y.square", "z.square", "a.circle.fill", "b.circle.fill", "c.circle.fill", "d.circle.fill", "e.circle.fill", "f.circle.fill", "g.circle.fill", "h.circle.fill", "i.circle.fill", "j.circle.fill", "k.circle.fill", "l.circle.fill", "m.circle.fill", "n.circle.fill", "o.circle.fill", "p.circle.fill", "q.circle.fill", "r.circle.fill", "s.circle.fill", "t.circle.fill", "u.circle.fill", "v.circle.fill", "w.circle.fill", "x.circle.fill", "y.circle.fill", "z.circle.fill", "a.square.fill", "b.square.fill", "c.square.fill", "d.square.fill", "e.square.fill", "f.square.fill", "g.square.fill", "h.square.fill", "i.square.fill", "j.square.fill", "k.square.fill", "l.square.fill", "m.square.fill", "n.square.fill", "o.square.fill", "p.square.fill", "q.square.fill", "r.square.fill", "s.square.fill", "t.square.fill", "u.square.fill", "v.square.fill", "w.square.fill", "x.square.fill", "y.square.fill", "z.square.fill", "00.circle", "00.circle.fill", "0.square", "00.square", "0.square.fill", "00.square.fill", "01.circle", "01.circle.fill", "1.square", "01.square", "1.square.fill", "01.square.fill", "02.circle", "02.circle.fill", "2.square", "02.square", "2.square.fill", "02.square.fill", "03.circle", "03.circle.fill", "3.square", "03.square", "3.square.fill", "03.square.fill", "4.alt.circle", "4.alt.circle.fill", "4.alt.square", "4.alt.square.fill", "04.circle", "04.circle.fill", "4.square", "04.square", "4.square.fill", "04.square.fill", "05.circle", "05.circle.fill", "5.square", "05.square", "5.square.fill", "05.square.fill", "6.alt.circle", "6.alt.circle.fill", "6.alt.square", "6.alt.square.fill", "06.circle", "06.circle.fill", "6.square", "06.square", "6.square.fill", "06.square.fill", "07.circle", "07.circle.fill", "7.square", "07.square", "7.square.fill", "07.square.fill", "08.circle", "08.circle.fill", "8.square", "08.square", "8.square.fill", "08.square.fill", "9.alt.circle", "9.alt.circle.fill", "9.alt.square", "9.alt.square.fill", "09.circle", "09.circle.fill", "9.square", "09.square", "9.square.fill", "09.square.fill", "10.circle", "10.circle.fill", "10.square", "10.square.fill", "11.circle", "11.circle.fill", "11.square", "11.square.fill", "12.circle", "12.circle.fill", "12.square", "12.square.fill", "13.circle", "13.circle.fill", "13.square", "13.square.fill", "14.circle", "14.circle.fill", "14.square", "14.square.fill", "15.circle", "15.circle.fill", "15.square", "15.square.fill", "16.circle", "16.circle.fill", "16.square", "16.square.fill", "17.circle", "17.circle.fill", "17.square", "17.square.fill", "18.circle", "18.circle.fill", "18.square", "18.square.fill", "19.square", "19.square.fill", "20.circle", "20.circle.fill", "20.square", "20.square.fill", "21.circle", "21.circle.fill", "21.square", "21.square.fill", "22.circle", "22.circle.fill", "22.square", "22.square.fill", "23.circle", "23.circle.fill", "23.square", "23.square.fill", "24.circle", "24.circle.fill", "24.square", "24.square.fill", "25.circle", "25.circle.fill", "25.square", "25.square.fill", "26.circle", "26.circle.fill", "26.square", "26.square.fill", "27.circle", "27.circle.fill", "27.square", "27.square.fill", "28.circle", "28.circle.fill", "28.square", "28.square.fill", "29.circle", "29.circle.fill", "29.square", "29.square.fill", "30.circle", "30.circle.fill", "30.square", "30.square.fill", "31.circle", "31.circle.fill", "31.square", "31.square.fill", "32.circle", "32.circle.fill", "32.square", "32.square.fill", "33.circle", "33.circle.fill", "33.square", "33.square.fill", "34.circle", "34.circle.fill", "34.square", "34.square.fill", "35.circle", "35.circle.fill", "35.square", "35.square.fill", "36.circle", "36.circle.fill", "36.square", "36.square.fill", "37.circle", "37.circle.fill", "37.square", "37.square.fill", "38.circle", "38.circle.fill", "38.square", "38.square.fill", "39.circle", "39.circle.fill", "39.square", "39.square.fill", "40.circle", "40.circle.fill", "40.square", "40.square.fill", "41.circle", "41.circle.fill", "41.square", "41.square.fill", "42.circle", "42.circle.fill", "42.square", "42.square.fill", "43.circle", "43.circle.fill", "43.square", "43.square.fill", "44.circle", "44.circle.fill", "44.square", "44.square.fill", "45.circle", "45.circle.fill", "45.square", "45.square.fill", "46.circle", "46.circle.fill", "46.square", "46.square.fill", "47.circle", "47.circle.fill", "47.square", "47.square.fill", "48.circle", "48.circle.fill", "48.square", "48.square.fill", "49.circle", "49.circle.fill", "49.square", "49.square.fill", "50.circle", "50.circle.fill", "50.square", "50.square.fill", "clear.fill", "capslock.fill", "eject.fill", "plus.circle", "minus.circle", "multiply.circle", "divide", "divide.circle", "divide.circle.fill", "divide.square", "divide.square.fill", "equal", "equal.circle", "equal.circle.fill", "equal.square", "equal.square.fill", "greaterthan", "greaterthan.circle", "greaterthan.circle.fill", "greaterthan.square", "greaterthan.square.fill", "lessthan", "lessthan.circle", "lessthan.circle.fill", "lessthan.square", "lessthan.square.fill", "minus.circle.fill", "minus.magnifyingglass", "minus.rectangle", "minus.rectangle.fill", "minus.slash.plus", "minus.square", "minus.square.fill", "multiply.circle.fill", "multiply.square", "multiply.square.fill", "plus", "plus.circle.fill", "plus.magnifyingglass", "plus.rectangle", "plus.rectangle.fill", "plus.rectangle.on.rectangle", "plus.rectangle.on.rectangle.fill", "plus.slash.minus", "plus.square", "plus.square.fill", "plus.square.on.square", "plus.square.on.square.fill", "plusminus", "plusminus.circle", "plusminus.circle.fill", "x.squareroot", "asterisk.circle", "asterisk.circle.fill"]
            
        case .tech:
            return ["airplayaudio", "airplayvideo", "antenna.radiowaves.left.and.right", "app", "app.badge", "app.badge.fill", "app.fill", "arkit", "backward", "backward.end", "backward.end.alt", "backward.end.alt.fill", "backward.end.fill", "backward.fill", "badge.plus.radiowaves.right", "barcode", "barcode.viewfinder", "battery.0", "battery.25", "battery.100", "bolt", "bolt.fill", "bolt.horizontal", "bolt.horizontal.circle", "bolt.horizontal.circle.fill", "bolt.horizontal.fill", "bolt.horizontal.icloud", "bolt.horizontal.icloud.fill", "bolt.slash", "bolt.slash.fill", "camera", "camera.fill", "camera.on.rectangle", "camera.on.rectangle.fill", "camera.rotate", "camera.rotate.fill", "camera.viewfinder", "clock", "clock.fill", "cursor.rays", "desktopcomputer", "dial", "dial.fill", "dot.circle", "dot.circle.fill", "dot.radiowaves.left.and.right", "dot.radiowaves.right", "dot.square", "dot.square.fill", "ear", "faceid", "film", "film.fill", "forward", "forward.end", "forward.end.alt", "forward.end.alt.fill", "forward.end.fill", "forward.fill", "gamecontroller", "gamecontroller.fill", "gear", "gobackward", "gobackward.10", "gobackward.10.ar", "gobackward.15", "gobackward.15.ar", "gobackward.30", "gobackward.30.ar", "gobackward.45", "gobackward.45.ar", "gobackward.60", "gobackward.60.ar", "gobackward.75", "gobackward.75.ar", "gobackward.90", "gobackward.90.ar", "gobackward.minus", "goforward", "goforward.10", "goforward.10.ar", "goforward.15", "goforward.15.ar", "goforward.30", "goforward.30.ar", "goforward.45", "goforward.45.ar", "goforward.60", "goforward.60.ar", "goforward.75", "goforward.75.ar", "goforward.90", "goforward.90.ar", "goforward.plus", "headphones", "hourglass", "hourglass.bottomhalf.fill", "hourglass.tophalf.fill", "icloud", "icloud.and.arrow.down", "icloud.and.arrow.down.fill", "icloud.and.arrow.up", "icloud.and.arrow.up.fill", "icloud.circle", "icloud.circle.fill", "icloud.fill", "icloud.slash", "icloud.slash.fill", "keyboard", "keyboard.chevron.compact.down", "light.max", "light.min", "livephoto", "livephoto.play", "livephoto.slash", "lock", "lock.circle", "lock.circle.fill", "lock.fill", "lock.icloud", "lock.icloud.fill", "lock.open", "lock.open.fill", "lock.rotation", "lock.rotation.open", "lock.slash", "lock.slash.fill", "macwindow", "mic", "mic.circle", "mic.circle.fill", "mic.fill", "mic.slash", "mic.slash.fill", "music.mic", "pause", "pause.circle", "pause.circle.fill", "pause.fill", "pause.rectangle", "pause.rectangle.fill", "personalhotspot", "phone", "phone.arrow.down.left", "phone.arrow.down.left.fill", "phone.arrow.right", "phone.arrow.right.fill", "phone.arrow.up.right", "phone.arrow.up.right.fill", "phone.badge.plus", "phone.badge.plus.fill", "phone.circle", "phone.circle.fill", "phone.down", "phone.down.circle", "phone.down.circle.fill", "phone.down.fill", "phone.fill", "photo", "photo.fill", "photo.on.rectangle", "photo.on.rectangle.fill", "play", "play.circle", "play.circle.fill", "play.fill", "play.rectangle", "play.rectangle.fill", "playpause", "playpause.fill", "power", "printer", "printer.fill", "qrcode", "qrcode.viewfinder", "radiowaves.left", "radiowaves.right", "rays", "realtimetext", "recordingtape", "repeat", "repeat.1", "rosette", "rotate.left", "rotate.left.fill", "rotate.right", "rotate.right.fill", "safari", "safari.fill", "shuffle", "slowmo", "speedometer", "stopwatch", "stopwatch.fill", "teletype", "timelapse", "timer", "tuningfork", "tv", "tv.circle", "tv.circle.fill", "tv.fill", "tv.music.note", "tv.music.note.fill", "uiwindow.split.2x1", "video", "video.badge.plus", "video.badge.plus.fill", "video.circle", "video.circle.fill", "video.fill", "video.slash", "video.slash.fill", "view.2d", "view.3d", "viewfinder", "viewfinder.circle", "viewfinder.circle.fill", "volume", "volume.1", "volume.1.fill", "volume.2", "volume.2.fill", "volume.3", "volume.3.fill", "volume.fill", "volume.slash", "volume.slash.fill", "volume.slash.fill.rtl", "volume.slash.rtl", "volume.zzz", "volume.zzz.fill", "waveform", "waveform.path", "waveform.path.badge.minus", "waveform.path.badge.plus", "waveform.path.ecg", "wifi", "wifi.exclamationmark", "wifi.slash", "zzz"]
            
        case .nature:
            return ["ant", "ant.fill", "moon.fill", "moon", "moon.circle", "moon.circle.fill", "moon.stars", "moon.stars.fill", "moon.zzz", "moon.zzz.fill", "cloud", "cloud.bolt", "cloud.bolt.fill", "cloud.bolt.rain", "cloud.bolt.rain.fill", "cloud.drizzle", "cloud.drizzle.fill", "cloud.fill", "cloud.fog", "cloud.fog.fill", "cloud.hail", "cloud.hail.fill", "cloud.heavyrain", "cloud.heavyrain.fill", "cloud.moon", "cloud.moon.bolt", "cloud.moon.bolt.fill", "cloud.moon.fill", "cloud.moon.rain", "cloud.moon.rain.fill", "cloud.rain", "cloud.rain.fill", "cloud.sleet", "cloud.sleet.fill", "cloud.snow", "cloud.snow.fill", "cloud.sun", "cloud.sun.bolt", "cloud.sun.bolt.fill", "cloud.sun.fill", "cloud.sun.rain", "cloud.sun.rain.fill", "hurricane", "leaf.arrow.circlepath", "snow", "sun.dust", "sun.dust.fill", "sun.haze", "sun.haze.fill", "sun.max", "sun.max.fill", "sun.min", "sun.min.fill", "sunrise", "sunrise.fill", "sunset", "sunset.fill", "tornado", "tropicalstorm", "umbrella", "umbrella.fill", "wind", "wind.snow"]
            
        case .finance:
            return ["australsign.circle", "australsign.circle.fill", "australsign.square", "australsign.square.fill", "bahtsign.circle", "bahtsign.circle.fill", "bahtsign.square", "bahtsign.square.fill", "bitcoinsign.circle", "bitcoinsign.circle.fill", "bitcoinsign.square", "bitcoinsign.square.fill", "cedisign.circle", "cedisign.circle.fill", "cedisign.square", "cedisign.square.fill", "centsign.circle", "centsign.circle.fill", "centsign.square", "centsign.square.fill", "chart.bar", "chart.bar.fill", "chart.pie", "chart.pie.fill", "coloncurrencysign.circle", "coloncurrencysign.circle.fill", "coloncurrencysign.square", "coloncurrencysign.square.fill", "creditcard", "creditcard.fill", "cruzeirosign.circle", "cruzeirosign.circle.fill", "cruzeirosign.square", "cruzeirosign.square.fill", "dollarsign.circle", "dollarsign.circle.fill", "dollarsign.square", "dollarsign.square.fill", "dongsign.circle", "dongsign.circle.fill", "dongsign.square", "dongsign.square.fill", "eurosign.circle", "eurosign.circle.fill", "eurosign.square", "eurosign.square.fill", "florinsign.circle", "florinsign.circle.fill", "florinsign.square", "florinsign.square.fill", "francsign.circle", "francsign.circle.fill", "francsign.square", "francsign.square.fill", "guaranisign.circle", "guaranisign.circle.fill", "guaranisign.square", "guaranisign.square.fill", "hryvniasign.circle", "hryvniasign.circle.fill", "hryvniasign.square", "hryvniasign.square.fill", "indianrupeesign.circle", "indianrupeesign.circle.fill", "indianrupeesign.square", "indianrupeesign.square.fill", "kipsign.circle", "kipsign.circle.fill", "kipsign.square", "kipsign.square.fill", "larisign.circle", "larisign.circle.fill", "larisign.square", "larisign.square.fill", "lirasign.circle", "lirasign.circle.fill", "lirasign.square", "lirasign.square.fill", "manatsign.circle", "manatsign.circle.fill", "manatsign.square", "manatsign.square.fill", "millsign.circle", "millsign.circle.fill", "millsign.square", "millsign.square.fill", "nairasign.circle", "nairasign.circle.fill", "nairasign.square", "nairasign.square.fill", "percent", "pesetasign.circle", "pesetasign.circle.fill", "pesetasign.square", "pesetasign.square.fill", "pesosign.circle", "pesosign.circle.fill", "pesosign.square", "pesosign.square.fill", "rublesign.circle", "rublesign.circle.fill", "rublesign.square", "rublesign.square.fill", "rupeesign.circle", "rupeesign.circle.fill", "rupeesign.square", "rupeesign.square.fill", "sheqelsign.circle", "sheqelsign.circle.fill", "sheqelsign.square", "sheqelsign.square.fill", "sterlingsign.circle", "sterlingsign.circle.fill", "sterlingsign.square", "sterlingsign.square.fill", "tengesign.circle", "tengesign.circle.fill", "tengesign.square", "tengesign.square.fill", "tugriksign.circle", "tugriksign.circle.fill", "tugriksign.square", "tugriksign.square.fill", "turkishlirasign.circle", "turkishlirasign.circle.fill", "turkishlirasign.square", "turkishlirasign.square.fill", "wonsign.circle", "wonsign.circle.fill", "wonsign.square", "wonsign.square.fill", "yensign.circle", "yensign.circle.fill", "yensign.square", "yensign.square.fill"]
            
        case .general:
            return ["a", "bookmark", "bookmark.fill", "briefcase", "briefcase.fill", "calendar", "calendar.badge.minus", "calendar.badge.plus", "checkmark.circle", "checkmark.circle.fill", "checkmark.rectangle", "checkmark.rectangle.fill", "checkmark.seal", "checkmark.seal.fill", "checkmark.square", "checkmark.square.fill", "chevron.compact.down", "chevron.compact.left", "chevron.compact.right", "chevron.compact.up", "chevron.down", "chevron.down.circle", "chevron.down.circle.fill", "chevron.down.square", "chevron.down.square.fill", "chevron.left", "chevron.left.2", "chevron.left.circle", "chevron.left.circle.fill", "chevron.left.slash.chevron.right", "chevron.left.square", "chevron.left.square.fill", "chevron.right", "chevron.right.2", "chevron.right.circle", "chevron.right.circle.fill", "chevron.right.square", "chevron.right.square.fill", "chevron.up", "chevron.up.chevron.down", "chevron.up.circle", "chevron.up.circle.fill", "chevron.up.square", "chevron.up.square.fill", "circle.bottomthird.split", "circle.grid.3x3", "circle.grid.3x3.fill", "circle.grid.hex", "circle.grid.hex.fill", "circle.lefthalf.fill", "circle.righthalf.fill", "doc", "doc.append", "doc.fill", "doc.on.clipboard", "doc.on.clipboard.fill", "doc.on.doc", "doc.on.doc.fill", "doc.plaintext", "doc.richtext", "doc.text", "doc.text.fill", "doc.text.magnifyingglass", "doc.text.viewfinder", "drop.triangle", "drop.triangle.fill", "ellipsis", "ellipsis.circle", "ellipsis.circle.fill", "envelope", "envelope.badge", "envelope.badge.fill", "envelope.circle", "envelope.circle.fill", "envelope.fill", "envelope.open", "envelope.open.fill", "eye", "eye.fill", "eye.slash", "eye.slash.fill", "eyedropper", "eyedropper.full", "eyedropper.halffull", "eyeglasses", "flag", "flag.fill", "flag.slash", "flag.slash.fill", "folder", "folder.badge.minus", "folder.badge.minus.fill", "folder.badge.person.crop", "folder.badge.person.crop.fill", "folder.badge.plus", "folder.badge.plus.fill", "folder.circle", "folder.circle.fill", "folder.fill", "function", "fx", "gauge", "gauge.badge.minus", "gauge.badge.plus", "gift", "gift.fill", "globe", "grid", "grid.circle", "grid.circle.fill", "hammer", "hammer.fill", "hand.draw", "hand.draw.fill", "hand.point.left", "hand.point.left.fill", "hand.point.right", "hand.point.right.fill", "hand.raised", "hand.raised.fill", "hand.raised.slash", "hand.raised.slash.fill", "hand.thumbsdown", "hand.thumbsdown.fill", "hand.thumbsup", "hand.thumbsup.fill", "hare", "hare.fill", "house", "house.fill", "lasso", "link", "link.circle", "link.circle.fill", "link.icloud", "link.icloud.fill", "list.bullet", "list.bullet.below.rectangle", "list.bullet.indent", "list.dash", "list.number", "list.number.rtl", "magnifyingglass", "magnifyingglass.circle", "magnifyingglass.circle.fill", "music.house", "music.house.fill", "music.note", "music.note.list", "nosign", "number", "number.circle", "number.circle.fill", "number.square", "number.square.fill", "paintbrush", "paintbrush.fill", "paperclip", "paperplane", "paperplane.fill", "pencil", "pencil.and.ellipsis.rectangle", "pencil.and.outline", "pencil.circle", "pencil.circle.fill", "pencil.slash", "pencil.tip", "pencil.tip.crop.circle", "pencil.tip.crop.circle.badge.minus", "pencil.tip.crop.circle.badge.plus", "pin", "pin.fill", "pin.slash", "pin.slash.fill", "purchased", "purchased.circle", "purchased.circle.fill", "quote.bubble", "quote.bubble.fill", "rectangle", "rectangle.3.offgrid", "rectangle.3.offgrid.fill", "rectangle.and.arrow.up.right.and.arrow.down.left", "rectangle.and.arrow.up.right.and.arrow.down.left.slash", "rectangle.and.paperclip", "rectangle.badge.checkmark", "rectangle.badge.checkmark.fill", "rectangle.badge.xmark", "rectangle.badge.xmark.fill", "rectangle.compress.vertical", "rectangle.dock", "rectangle.expand.vertical", "rectangle.fill", "rectangle.grid.1x2", "rectangle.grid.1x2.fill", "rectangle.grid.2x2", "rectangle.grid.2x2.fill", "rectangle.grid.3x2", "rectangle.grid.3x2.fill", "rectangle.on.rectangle", "rectangle.on.rectangle.angled", "rectangle.on.rectangle.angled.fill", "rectangle.on.rectangle.fill", "rectangle.split.3x1", "rectangle.split.3x1.fill", "rectangle.split.3x3", "rectangle.split.3x3.fill", "rectangle.stack", "rectangle.stack.badge.minus", "rectangle.stack.badge.minus.fill", "rectangle.stack.badge.plus", "rectangle.stack.badge.plus.fill", "rectangle.stack.fill", "rectangle.stack.person.crop", "rectangle.stack.person.crop.fill", "ring.circle", "ring.circle.fill", "scissors", "scope", "scribble", "selection.pin.in.out", "shield", "shield.fill", "shield.lefthalf.fill", "shift.fill", "sidebar.left", "sidebar.right", "signature", "skew", "slash.circle", "slash.circle.fill", "slider.horizontal.3", "slider.horizontal.below.rectangle", "square.and.arrow.down", "square.and.arrow.down.fill", "square.and.arrow.down.on.square", "square.and.arrow.down.on.square.fill", "square.and.arrow.up", "square.and.arrow.up.fill", "square.and.arrow.up.on.square", "square.and.arrow.up.on.square.fill", "square.and.line.vertical.and.square", "square.and.line.vertical.and.square.fill", "square.and.pencil", "square.fill.and.line.vertical.and.square", "square.fill.and.line.vertical.square.fill", "square.grid.2x2", "square.grid.2x2.fill", "square.grid.3x2", "square.grid.3x2.fill", "square.grid.4x3.fill", "square.lefthalf.fill", "square.on.circle", "square.on.circle.fill", "square.on.square", "square.on.square.fill", "square.righthalf.fill", "square.split.1x2", "square.split.1x2.fill", "square.split.2x1", "square.split.2x1.fill", "square.split.2x2", "square.split.2x2.fill", "square.stack", "square.stack.3d.down.dottedline", "square.stack.3d.down.right", "square.stack.3d.down.right.fill", "square.stack.3d.up", "square.stack.3d.up.fill", "square.stack.3d.up.slash", "square.stack.3d.up.slash.fill", "square.stack.fill", "squares.below.rectangle", "star.circle", "star.circle.fill", "star.lefthalf.fill", "star.slash", "star.slash.fill", "strikethrough", "sum", "t.bubble", "t.bubble.fill", "table", "table.badge.more", "table.badge.more.fill", "table.fill", "tag", "tag.fill", "text.aligncenter", "text.alignleft", "text.alignright", "text.append", "text.badge.checkmark", "text.badge.minus", "text.badge.plus", "text.badge.star", "text.badge.xmark", "text.bubble", "text.bubble.fill", "text.chevron.left", "text.chevron.right", "text.cursor", "text.insert", "text.justify", "text.justifyleft", "text.justifyright", "text.quote", "textbox", "textformat", "textformat.123", "textformat.abc", "textformat.abc.dottedunderline", "textformat.alt", "textformat.size", "textformat.subscript", "textformat.superscript", "trash", "trash.circle", "trash.circle.fill", "trash.fill", "trash.slash", "trash.slash.fill", "tray", "tray.2", "tray.2.fill", "tray.and.arrow.down", "tray.and.arrow.down.fill", "tray.and.arrow.up", "tray.and.arrow.up.fill", "tray.fill", "tray.full", "tray.full.fill", "underline", "wand.and.rays", "wand.and.rays.inverse", "wand.and.stars", "wand.and.stars.inverse", "wrench", "wrench.fill"]
            
        case .people:
            return ["person", "person.2.square.stack", "person.2.square.stack.fill", "person.and.person", "person.and.person.fill", "person.badge.minus", "person.badge.minus.fill", "person.badge.plus", "person.badge.plus.fill", "person.circle", "person.circle.fill", "person.crop.circle", "person.crop.circle.badge.checkmark", "person.crop.circle.badge.checkmark.fill", "person.crop.circle.badge.exclam", "person.crop.circle.badge.exclam.fill", "person.crop.circle.badge.minus", "person.crop.circle.badge.minus.fill", "person.crop.circle.badge.plus", "person.crop.circle.badge.plus.fill", "person.crop.circle.badge.xmark", "person.crop.circle.badge.xmark.fill", "person.crop.circle.fill", "person.crop.rectangle", "person.crop.rectangle.fill", "person.crop.square", "person.crop.square.fill", "person.fill", "person.icloud", "person.icloud.fill"]
            
        case .travel:
            return ["airplane", "car.fill", "location", "location.circle", "location.circle.fill", "location.fill", "location.north", "location.north.fill", "location.north.line", "location.north.line.fill", "location.slash", "location.slash.fill", "map", "map.fill", "mappin", "mappin.and.ellipse", "mappin.slash"]
            
        case .health:
            return ["bandage", "bandage.fill", "bed.double", "bed.double.fill", "burn", "thermometer", "thermometer.snowflake", "thermometer.sun", "staroflife", "staroflife.fill"]
            
        case .leisure:
            return ["suit.spade.fill", "suit.heart", "suit.diamond", "suit.club.fill", "suit.spade", "suit.heart.fill", "suit.diamond.fill", "suit.club", "guitars", "memories", "memories.badge.minus", "memories.badge.plus", "metronome", "pano", "pano.fill", "tortoise", "tortoise.fill"]
        }
    }
    
    var needsVerticalScroll: Bool {
        return icons.count > 18
    }
}