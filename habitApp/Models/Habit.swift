import Foundation
import SwiftData

@Model
final class Habit {
  var id: UUID = UUID()
  var name: String = ""
  var categoryRawValue: String = ""  // Store enum as string for CloudKit
  var timesPerDay: Int = 1
  var frequencyRawValue: String = "daily"  // Store enum as string
  var customDaysRawValue: [String] = []  // Store enum array as strings
  var reminderEnabled: Bool = false
  var reminderTime: Date? = nil
  var notificationTimesData: Data? = nil  // Store multiple notification times as Data for CloudKit
  var createdAt: Date = Date()
  var displayOrder: Int = 0  // Display order for habit reordering, CloudKit compatible
  var isArchived: Bool = false  // Archive status for CloudKit compatibility
  var archivedAt: Date? = nil  // Archive timestamp for CloudKit compatibility

  @Relationship(deleteRule: .cascade, inverse: \HabitRecord.habit)
  var records: [HabitRecord]? = []

  init(
    name: String, category: HabitCategory, timesPerDay: Int = 1, frequency: FrequencyType = .daily,
    customDays: Set<DayOfWeek> = [], reminderEnabled: Bool = false, reminderTime: Date? = nil
  ) {
    self.name = name
    self.categoryRawValue = category.rawValue
    self.timesPerDay = timesPerDay
    self.frequencyRawValue = frequency.rawValue
    self.customDaysRawValue = customDays.map { $0.rawValue }
    self.reminderEnabled = reminderEnabled
    self.reminderTime = reminderTime
    self.createdAt = Date()
  }

  // MARK: - Computed Properties

  var category: HabitCategory {
    HabitCategory(rawValue: categoryRawValue) ?? .health
  }

  var frequency: FrequencyType {
    FrequencyType(rawValue: frequencyRawValue) ?? .daily
  }

  var customDays: Set<DayOfWeek> {
    Set(customDaysRawValue.compactMap { DayOfWeek(rawValue: $0) })
  }

  var notificationTimes: [Date] {
    get {
      guard let data = notificationTimesData else {
        return reminderTime != nil ? [reminderTime!] : []
      }
      return (try? JSONDecoder().decode([Date].self, from: data)) ?? []
    }
    set {
      notificationTimesData = try? JSONEncoder().encode(newValue)
      // Keep backward compatibility
      reminderTime = newValue.first
      reminderEnabled = !newValue.isEmpty
    }
  }

  var isCompletedToday: Bool {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())

    return records?.contains { record in
      calendar.isDate(record.date, inSameDayAs: today) && record.completionLevel >= timesPerDay
    } ?? false
  }

  var todayCompletionLevel: Int {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())

    return Int(
      records?.first { record in
        calendar.isDate(record.date, inSameDayAs: today)
      }?.completionLevel ?? 0)
  }

  var subtitle: String {
    if isCompletedToday {
      return timesPerDay == 1 ? "Completed" : "\(timesPerDay) times completed"
    } else {
      let remaining = timesPerDay - todayCompletionLevel
      if remaining == timesPerDay {
        return timesPerDay == 1 ? "Not started" : "\(timesPerDay) times remaining"
      } else {
        return "\(remaining) more time\(remaining == 1 ? "" : "s")"
      }
    }
  }

  // MARK: - Archive Helper Methods

  func archive() {
    isArchived = true
    archivedAt = Date()
  }

  func restore() {
    isArchived = false
    archivedAt = nil
  }

  var archiveDateString: String {
    guard let archivedAt = archivedAt else { return "" }
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    return formatter.string(from: archivedAt)
  }

  // MARK: - Scheduling Helper Methods

  func isScheduledFor(date: Date) -> Bool {
    let calendar = Calendar.current
    let weekday = calendar.component(.weekday, from: date)

    switch frequency {
    case .daily:
      return true
    case .weekdays:
      // Monday = 2, Tuesday = 3, ..., Friday = 6 in Calendar.current.component(.weekday)
      return weekday >= 2 && weekday <= 6
    case .custom:
      let dayOfWeek = DayOfWeek.from(weekday: weekday)
      return customDays.contains(dayOfWeek)
    }
  }

  var isScheduledForToday: Bool {
    return isScheduledFor(date: Date())
  }
}

@Model
final class HabitRecord {
  var date: Date = Date()
  var completionLevel: Int16 = 0

  @Relationship var habit: Habit?

  init(date: Date, completionLevel: Int16 = 0, habit: Habit? = nil) {
    self.date = Calendar.current.startOfDay(for: date)  // Normalize to start of day
    self.completionLevel = completionLevel
    self.habit = habit
  }
}
