import SwiftUI
import SwiftData

@MainActor
class GridColorService {
    /// Resolves the appropriate grid color for a habit based on appearance settings
    /// - Parameters:
    ///   - habit: The habit to get color for
    ///   - modelContext: SwiftData context for accessing custom categories
    ///   - useGridColor: Whether grid color feature is enabled
    /// - Returns: Color asset name to use in the grid
    static func getGridColor(for habit: Habit, 
                            modelContext: ModelContext, 
                            useGridColor: Bool) -> String {
        // If grid color feature is disabled, use default green
        if !useGridColor {
            return "Success"
        }
        
        // Check if habit uses custom category
        if habit.categoryRawValue.hasPrefix("custom_") {
            return getCustomCategoryColor(habit: habit, modelContext: modelContext)
        } else {
            // Use predefined category color
            return habit.category.color
        }
    }
    
    /// Resolves color for habits using custom categories
    /// - Parameters:
    ///   - habit: Habit with custom category
    ///   - modelContext: SwiftData context for database access
    /// - Returns: Custom category color hex or fallback to default
    private static func getCustomCategoryColor(habit: Habit, 
                                              modelContext: ModelContext) -> String {
        // Extract UUID from "custom_UUID" format
        let uuidString = String(habit.categoryRawValue.dropFirst(7))
        
        guard let uuid = UUID(uuidString: uuidString) else {
            // Invalid UUID format, fallback to default
            return "Success"
        }
        
        // Query for custom category with matching ID
        let descriptor = FetchDescriptor<CustomCategory>(
            predicate: #Predicate<CustomCategory> { category in
                category.id == uuid
            }
        )
        
        do {
            let customCategories = try modelContext.fetch(descriptor)
            if let customCategory = customCategories.first {
                // Return the hex color directly instead of trying to map it
                return customCategory.colorHex
            } else {
                // Custom category not found (possibly deleted), fallback
                return "Success"
            }
        } catch {
            // Database error, fallback to default
            print("Error fetching custom category: \(error)")
            return "Success"
        }
    }
    
    /// Resolves the appropriate icon for a habit
    /// - Parameters:
    ///   - habit: The habit to get icon for
    ///   - modelContext: SwiftData context for accessing custom categories
    /// - Returns: SF Symbol name to use
    static func getHabitIcon(for habit: Habit, modelContext: ModelContext) -> String {
        // Check if habit uses custom category
        if habit.categoryRawValue.hasPrefix("custom_") {
            return getCustomCategoryIcon(habit: habit, modelContext: modelContext)
        } else {
            // Use predefined category icon
            return habit.category.icon
        }
    }
    
    /// Resolves icon for habits using custom categories
    /// - Parameters:
    ///   - habit: Habit with custom category
    ///   - modelContext: SwiftData context for database access
    /// - Returns: Custom category icon or fallback to default
    private static func getCustomCategoryIcon(habit: Habit, modelContext: ModelContext) -> String {
        // Extract UUID from "custom_UUID" format
        let uuidString = String(habit.categoryRawValue.dropFirst(7))
        
        guard let uuid = UUID(uuidString: uuidString) else {
            // Invalid UUID format, fallback to default
            return "star"
        }
        
        // Query for custom category with matching ID
        let descriptor = FetchDescriptor<CustomCategory>(
            predicate: #Predicate<CustomCategory> { category in
                category.id == uuid
            }
        )
        
        do {
            let customCategories = try modelContext.fetch(descriptor)
            if let customCategory = customCategories.first {
                return customCategory.iconName
            } else {
                // Custom category not found (possibly deleted), fallback
                return "star"
            }
        } catch {
            // Database error, fallback to default
            print("Error fetching custom category: \(error)")
            return "star"
        }
    }
}
