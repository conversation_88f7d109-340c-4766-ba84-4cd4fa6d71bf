import Foundation
@preconcurrency import UserNotifications
import Swift<PERSON>

@MainActor
@Observable
class NotificationManager: NSObject, UNUserNotificationCenterDelegate {
    private let userDefaults = UserDefaults.standard
    private let maxNotifications = 64 // iOS limit
    
    var authorizationStatus: UNAuthorizationStatus = .notDetermined
    var isAuthorized: Bool { authorizationStatus == .authorized }
    
    override init() {
        super.init()
        Task {
            await checkAuthorizationStatus()
            await setupNotificationCategories()
            await setupNotificationDelegate()
        }
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() async {
        let center = UNUserNotificationCenter.current()
        
        do {
            let granted = try await center.requestAuthorization(options: [.alert, .badge, .sound])
            authorizationStatus = granted ? .authorized : .denied
            
            if granted {
                await setupNotificationCategories()
            }
        } catch {
            print("Authorization request failed: \(error)")
            authorizationStatus = .denied
        }
    }
    
    func checkAuthorizationStatus() async {
        let center = UNUserNotificationCenter.current()
        let settings = await center.notificationSettings()
        authorizationStatus = settings.authorizationStatus
    }
    
    // MARK: - Notification Categories and Actions
    
    private func setupNotificationCategories() async {
        let center = UNUserNotificationCenter.current()
        
        let completeAction = UNNotificationAction(
            identifier: "COMPLETE_ACTION",
            title: "Mark Complete",
            options: [.foreground]
        )
        
        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE_ACTION",
            title: "Snooze 5 min",
            options: []
        )
        
        let habitCategory = UNNotificationCategory(
            identifier: "HABIT_REMINDER",
            actions: [completeAction, snoozeAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        center.setNotificationCategories([habitCategory])
    }
    
    private func setupNotificationDelegate() async {
        let center = UNUserNotificationCenter.current()
        center.delegate = self
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    nonisolated func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case "COMPLETE_ACTION":
            if let habitIdString = userInfo["habitId"] as? String,
               let habitId = UUID(uuidString: habitIdString) {
                Task { @MainActor in
                    await handleCompleteAction(habitId: habitId)
                }
            }
        case "SNOOZE_ACTION":
            if let habitIdString = userInfo["habitId"] as? String,
               let habitId = UUID(uuidString: habitIdString) {
                Task { @MainActor in
                    await handleSnoozeAction(habitId: habitId)
                }
            }
        default:
            break
        }
        
        completionHandler()
    }
    
    nonisolated func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .badge, .sound])
    }
    
    // MARK: - Notification Actions
    
    private func handleCompleteAction(habitId: UUID) async {
        // This would need access to the habit repository to mark the habit as complete
        // For now, we'll post a notification that can be handled by the app
        NotificationCenter.default.post(name: .habitCompletedFromNotification, object: habitId)
    }
    
    private func handleSnoozeAction(habitId: UUID) async {
        // Schedule a new notification 5 minutes from now
        guard let habit = await getHabit(by: habitId) else { return }
        
        let snoozeTime = Date().addingTimeInterval(5 * 60) // 5 minutes
        await scheduleNotification(
            for: habit,
            at: snoozeTime,
            identifier: "\(habitId.uuidString)-snooze-\(Date().timeIntervalSince1970)"
        )
    }
    
    private func getHabit(by id: UUID) async -> Habit? {
        // This would need to be implemented with proper context access
        // For now, return nil - this can be improved later
        return nil
    }
    
    // MARK: - Habit Notification Scheduling
    
    func scheduleNotificationsForHabit(_ habit: Habit) async {
        // Check and request authorization if needed
        if authorizationStatus != .authorized {
            await requestAuthorization()
        }
        
        guard isAuthorized else { 
            print("Notification permission not granted for habit: \(habit.name)")
            return 
        }
        
        // Remove existing notifications for this habit
        await cancelNotificationsForHabit(habitId: habit.id)
        
        // Get notification times from habit
        let notificationTimes = getNotificationTimesForHabit(habit)
        guard !notificationTimes.isEmpty else { 
            print("No notification times set for habit: \(habit.name)")
            return 
        }
        
        print("Scheduling notifications for habit: \(habit.name)")
        print("Notification times: \(notificationTimes)")
        print("Frequency: \(habit.frequency)")
        
        // Get the days this habit should be active
        let activeDays = getActiveDaysForHabit(habit)
        print("Active days: \(activeDays)")
        
        // Schedule notifications for the next 30 days to work within iOS limits
        let calendar = Calendar.current
        let today = Date()
        let scheduleUntil = calendar.date(byAdding: .day, value: 30, to: today) ?? today
        
        var scheduledCount = 0
        var currentDate = today
        
        while currentDate <= scheduleUntil && scheduledCount < maxNotifications {
            let weekday = calendar.component(.weekday, from: currentDate)
            let dayOfWeek = DayOfWeek.fromWeekday(weekday)
            
            if activeDays.contains(dayOfWeek) {
                for (index, time) in notificationTimes.enumerated() {
                    guard scheduledCount < maxNotifications else { break }
                    
                    var dateComponents = calendar.dateComponents([.year, .month, .day], from: currentDate)
                    let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
                    dateComponents.hour = timeComponents.hour
                    dateComponents.minute = timeComponents.minute
                    
                    if let notificationDate = calendar.date(from: dateComponents),
                       notificationDate > Date() { // Only schedule future notifications
                        
                        await scheduleNotification(
                            for: habit,
                            at: notificationDate,
                            identifier: "\(habit.id.uuidString)-\(currentDate.timeIntervalSince1970)-\(index)"
                        )
                        scheduledCount += 1
                    }
                }
            }
            
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        print("Total notifications scheduled for \(habit.name): \(scheduledCount)")
    }
    
    private func scheduleNotification(for habit: Habit, at date: Date, identifier: String) async {
        let center = UNUserNotificationCenter.current()
        
        let content = UNMutableNotificationContent()
        content.title = habit.name
        content.body = getNotificationMessage(for: habit)
        content.sound = .default
        content.categoryIdentifier = "HABIT_REMINDER"
        content.userInfo = [
            "habitId": habit.id.uuidString,
            "habitName": habit.name
        ]
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )
        
        do {
            try await center.add(request)
            print("✅ Successfully scheduled notification: \(identifier) for \(date)")
        } catch {
            print("❌ Failed to schedule notification for \(habit.name): \(error)")
        }
    }
    
    // MARK: - Notification Management
    
    func cancelNotificationsForHabit(habitId: UUID) async {
        let center = UNUserNotificationCenter.current()
        let pendingRequests = await center.pendingNotificationRequests()
        
        let identifiersToRemove = pendingRequests
            .filter { $0.identifier.hasPrefix(habitId.uuidString) }
            .map { $0.identifier }
        
        if !identifiersToRemove.isEmpty {
            center.removePendingNotificationRequests(withIdentifiers: identifiersToRemove)
            print("🚫 Cancelled \(identifiersToRemove.count) notifications for habit: \(habitId)")
        } else {
            print("ℹ️ No pending notifications found for habit: \(habitId)")
        }
    }
    
    func cancelAllNotifications() async {
        let center = UNUserNotificationCenter.current()
        center.removeAllPendingNotificationRequests()
    }
    
    func getPendingNotificationsCount() async -> Int {
        let center = UNUserNotificationCenter.current()
        let pendingRequests = await center.pendingNotificationRequests()
        return pendingRequests.count
    }
    
    // MARK: - Helper Methods
    
    private func getNotificationTimesForHabit(_ habit: Habit) -> [Date] {
        // Use the new computed property that supports multiple times
        return habit.notificationTimes
    }
    
    private func getActiveDaysForHabit(_ habit: Habit) -> Set<DayOfWeek> {
        switch habit.frequency {
        case .daily:
            return Set(DayOfWeek.allCases)
        case .weekdays:
            return [.monday, .tuesday, .wednesday, .thursday, .friday]
        case .custom:
            return habit.customDays
        }
    }
    
    private func getNotificationMessage(for habit: Habit) -> String {
        let messages = [
            "Time for your \(habit.name.lowercased()) habit! 💪",
            "Don't forget: \(habit.name) 🌟",
            "Your \(habit.name.lowercased()) reminder is here! ⏰",
            "Keep up the streak! Time for \(habit.name.lowercased()) 🔥",
            "Building habits one step at a time: \(habit.name) 🚀"
        ]
        
        return messages.randomElement() ?? "Time for \(habit.name)!"
    }
    
    // MARK: - Settings Helper
    
    func openNotificationSettings() {
        Task { @MainActor in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        }
    }
    
    // MARK: - Debug Helper
    
    func scheduleTestNotification() async {
        guard isAuthorized else {
            await requestAuthorization()
            return
        }
        
        let center = UNUserNotificationCenter.current()
        let content = UNMutableNotificationContent()
        content.title = "Test Notification"
        content.body = "This is a test notification from habitApp"
        content.sound = .default
        
        // Schedule for 5 seconds from now
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 5, repeats: false)
        let request = UNNotificationRequest(
            identifier: "test-notification-\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await center.add(request)
            print("✅ Test notification scheduled for 5 seconds from now")
        } catch {
            print("❌ Failed to schedule test notification: \(error)")
        }
    }
}

// MARK: - DayOfWeek Extension

extension DayOfWeek {
    static func fromWeekday(_ weekday: Int) -> DayOfWeek {
        // Calendar.current.component(.weekday, from: date) returns 1 for Sunday, 2 for Monday, etc.
        switch weekday {
        case 1: return .sunday
        case 2: return .monday
        case 3: return .tuesday
        case 4: return .wednesday
        case 5: return .thursday
        case 6: return .friday
        case 7: return .saturday
        default: return .sunday
        }
    }
    
    var weekdayNumber: Int {
        switch self {
        case .sunday: return 1
        case .monday: return 2
        case .tuesday: return 3
        case .wednesday: return 4
        case .thursday: return 5
        case .friday: return 6
        case .saturday: return 7
        }
    }
}
