@preconcurrency import Foundation
import SwiftData
import UIKit

// Cache statistics removed - no longer needed with optimized approach

@MainActor
@Observable
final class HabitRepository {
    private let context: ModelContext
    private let notificationManager: NotificationManager
    
    // Cache removed - using optimized Habit model methods instead
    
    init(context: ModelContext, notificationManager: NotificationManager) {
        self.context = context
        self.notificationManager = notificationManager
        setupModelObservation()
    }
    
    // MARK: - Habit CRUD
    
    private func getMaxDisplayOrder() throws -> Int {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { !$0.isArchived },
            sortBy: [SortDescriptor(\.displayOrder, order: .reverse)]
        )
        let habits = try context.fetch(descriptor)
        return habits.first?.displayOrder ?? 0
    }
    
    func fetchHabits() throws -> [Habit] {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { !$0.isArchived },
            sortBy: [SortDescriptor(\.displayOrder), SortDescriptor(\.createdAt)]
        )
        return try context.fetch(descriptor)
    }
    
    func fetchArchivedHabits() throws -> [Habit] {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { $0.isArchived },
            sortBy: [SortDescriptor(\.archivedAt, order: .reverse)]
        )
        return try context.fetch(descriptor)
    }
    
    func createHabit(
        name: String,
        category: HabitCategory,
        timesPerDay: Int,
        frequency: FrequencyType,
        customDays: Set<DayOfWeek> = [],
        reminderEnabled: Bool = false,
        reminderTime: Date? = nil
    ) throws {
        let habit = Habit(
            name: name,
            category: category,
            timesPerDay: timesPerDay,
            frequency: frequency,
            customDays: customDays,
            reminderEnabled: reminderEnabled,
            reminderTime: reminderTime
        )
        
        // Set displayOrder to place new habit at bottom of list
        let maxDisplayOrder = try getMaxDisplayOrder()
        habit.displayOrder = maxDisplayOrder + 1
        
        context.insert(habit)
        try context.save()
        
        // Schedule notifications after successful save
        if habit.reminderEnabled {
            Task {
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        }
        
        // Cache invalidation for new habit
        // Cache removed - SwiftData handles updates automatically // habit.id)
    }
    
    func deleteHabit(_ habit: Habit) throws {
        // Cancel notifications before deleting habit
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        // Cache invalidation before deletion
        // Cache removed - SwiftData handles updates automatically // habit.id)
        
        context.delete(habit)
        try context.save()
    }
    
    // MARK: - Habit Reordering
    
    func reorderHabits(_ habits: [Habit]) throws {
        // Fetch habits by ID to ensure we're working with objects from this context
        let habitIds = habits.map { $0.id }
        
        for (index, habitId) in habitIds.enumerated() {
            let descriptor = FetchDescriptor<Habit>(
                predicate: #Predicate<Habit> { $0.id == habitId }
            )
            
            if let habit = try context.fetch(descriptor).first {
                habit.displayOrder = index + 1 // Start from 1, not 0
            }
        }
        
        try context.save()
    }
    
    func initializeDisplayOrderForExistingHabits() throws {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { !$0.isArchived && $0.displayOrder == 0 },
            sortBy: [SortDescriptor(\.createdAt)]
        )
        let habitsNeedingOrder = try context.fetch(descriptor)
        
        for (index, habit) in habitsNeedingOrder.enumerated() {
            habit.displayOrder = index + 1 // Start from 1 to avoid conflicts
        }
        
        if !habitsNeedingOrder.isEmpty {
            try context.save()
        }
    }
    
    func updateHabit(
        _ habit: Habit,
        name: String,
        category: HabitCategory,
        timesPerDay: Int,
        frequency: FrequencyType,
        customDays: Set<DayOfWeek> = [],
        reminderEnabled: Bool = false,
        reminderTime: Date? = nil
    ) throws {
        habit.name = name
        habit.categoryRawValue = category.rawValue
        habit.timesPerDay = timesPerDay
        habit.frequencyRawValue = frequency.rawValue
        habit.customDaysRawValue = customDays.map { $0.rawValue }
        habit.reminderEnabled = reminderEnabled
        habit.reminderTime = reminderTime
        
        try context.save()
        
        // Reschedule notifications after successful update
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
            if habit.reminderEnabled {
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        }
        
        // Cache invalidation after update
        // Cache removed - SwiftData handles updates automatically // habit.id)
    }
    
    // MARK: - Habit Record Management
    
    func toggleHabitCompletion(_ habit: Habit) throws {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // Find existing record for today
        if let existingRecord = habit.records?.first(where: { calendar.isDate($0.date, inSameDayAs: today) }) {
            // Progressive completion based on timesPerDay
            let currentLevel = Int(existingRecord.completionLevel)
            
            if currentLevel >= habit.timesPerDay {
                // Currently at max, reset to 0
                existingRecord.completionLevel = 0
            } else {
                // Increment by 1
                existingRecord.completionLevel = Int16(currentLevel + 1)
            }
        } else {
            // No record for today, create new one with level 1
            let newRecord = HabitRecord(date: today, completionLevel: 1, habit: habit)
            context.insert(newRecord)
            habit.records?.append(newRecord)
        }
        
        try context.save()
        
        // Cache invalidation after record change
        // Cache removed - SwiftData handles updates automatically // habit.id)
    }
    
    func updateHabitRecordLevel(_ habit: Habit, level: Int) throws {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        if let existingRecord = habit.records?.first(where: { calendar.isDate($0.date, inSameDayAs: today) }) {
            existingRecord.completionLevel = Int16(min(level, habit.timesPerDay))
        } else {
            let newRecord = HabitRecord(date: today, completionLevel: Int16(min(level, habit.timesPerDay)), habit: habit)
            context.insert(newRecord)
            habit.records?.append(newRecord)
        }
        
        try context.save()
        
        // Cache invalidation after record change
        // Cache removed - SwiftData handles updates automatically // habit.id)
    }
    
    // MARK: - Archive Management
    
    func archiveHabit(_ habit: Habit) throws {
        // Cancel notifications when archiving
        Task {
            print("🗄️ Archiving habit: \(habit.name), cancelling notifications...")
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        habit.archive()
        try context.save()
        print("✅ Habit \(habit.name) archived successfully")
    }
    
    func restoreHabit(_ habit: Habit) throws {
        habit.restore()
        try context.save()
        
        // Reschedule notifications when restoring - ONLY if habit has reminders enabled
        if habit.reminderEnabled {
            Task {
                print("🔄 Restoring habit: \(habit.name) with reminders enabled, rescheduling notifications...")
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        } else {
            print("🔄 Restoring habit: \(habit.name) without reminders - no notifications scheduled")
        }
    }
    
    func permanentlyDeleteHabit(_ habit: Habit) throws {
        // Cancel notifications before permanent deletion
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        context.delete(habit)
        try context.save()
    }
    
    // MARK: - Simplified Record Operations (Cache Removed)
    
    func addOrUpdateRecord(for habitId: UUID, date: Date, completionLevel: Int16, note: String? = nil) async throws {
        let normalizedDate = Calendar.current.startOfDay(for: date)
        
        // Find the habit
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate { $0.id == habitId }
        )
        
        guard let habit = try context.fetch(descriptor).first else {
            throw NSError(domain: "HabitRepository", code: 1, userInfo: [NSLocalizedDescriptionKey: "Habit not found"])
        }
        
        // Find existing record or create new one
        if let existingRecord = habit.record(for: normalizedDate) {
            existingRecord.completionLevel = completionLevel
        } else {
            let newRecord = HabitRecord(date: normalizedDate, completionLevel: completionLevel, habit: habit)
            context.insert(newRecord)
            habit.records = (habit.records ?? []) + [newRecord]
        }
        
        try context.save()
    }
    
    func deleteRecord(for habitId: UUID, on date: Date) async throws {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate { $0.id == habitId }
        )
        
        guard let habit = try context.fetch(descriptor).first else { return }
        
        if let record = habit.record(for: date) {
            context.delete(record)
            try context.save()
        }
    }
    
    // MARK: - Simplified Record Access (No Cache Needed)
    
    func getRecord(for habitId: UUID, on date: Date) async -> HabitRecord? {
        do {
            let descriptor = FetchDescriptor<Habit>(
                predicate: #Predicate { $0.id == habitId }
            )
            
            guard let habit = try context.fetch(descriptor).first else { return nil }
            return habit.record(for: date)
        } catch {
            print("Error fetching habit record: \(error)")
            return nil
        }
    }
    
    // MARK: - Simplified Model Management
    
    private func setupModelObservation() {
        // No cache management needed - SwiftData handles updates automatically
    }
}
