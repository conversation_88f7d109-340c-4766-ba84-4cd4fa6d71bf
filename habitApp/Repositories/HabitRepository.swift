@preconcurrency import Foundation
import SwiftData
import UIKit

// MARK: - Cache Statistics

struct CacheStatistics {
    let cachedHabits: Int
    let totalCachedRecords: Int
    let largestCacheSize: Int
    let memoryEstimate: Int // in bytes
    
    var formattedMemoryEstimate: String {
        let kb = Double(memoryEstimate) / 1024.0
        if kb < 1024 {
            return String(format: "%.1f KB", kb)
        } else {
            let mb = kb / 1024.0
            return String(format: "%.1f MB", mb)
        }
    }
}

@MainActor
@Observable
final class HabitRepository {
    private let context: ModelContext
    private let notificationManager: NotificationManager
    
    // NEW: Date-indexed cache for O(1) lookups
    private var recordCache: [UUID: [String: HabitRecord]] = [:]
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter
    }()
    
    init(context: ModelContext, notificationManager: NotificationManager) {
        self.context = context
        self.notificationManager = notificationManager
        setupModelObservation()
    }
    
    // MARK: - Habit CRUD
    
    func fetchHabits() throws -> [Habit] {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { !$0.isArchived },
            sortBy: [SortDescriptor(\.displayOrder), SortDescriptor(\.createdAt)]
        )
        return try context.fetch(descriptor)
    }
    
    func fetchArchivedHabits() throws -> [Habit] {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { $0.isArchived },
            sortBy: [SortDescriptor(\.archivedAt, order: .reverse)]
        )
        return try context.fetch(descriptor)
    }
    
    func createHabit(
        name: String,
        category: HabitCategory,
        timesPerDay: Int,
        frequency: FrequencyType,
        customDays: Set<DayOfWeek> = [],
        reminderEnabled: Bool = false,
        reminderTime: Date? = nil
    ) throws {
        let habit = Habit(
            name: name,
            category: category,
            timesPerDay: timesPerDay,
            frequency: frequency,
            customDays: customDays,
            reminderEnabled: reminderEnabled,
            reminderTime: reminderTime
        )
        
        context.insert(habit)
        try context.save()
        
        // Schedule notifications after successful save
        if habit.reminderEnabled {
            Task {
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        }
        
        // Cache invalidation for new habit
        invalidateCacheAfterChange(habitId: habit.id)
    }
    
    func deleteHabit(_ habit: Habit) throws {
        // Cancel notifications before deleting habit
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        // Cache invalidation before deletion
        invalidateCacheAfterChange(habitId: habit.id)
        
        context.delete(habit)
        try context.save()
    }
    
    // MARK: - Habit Reordering
    
    func reorderHabits(_ habits: [Habit]) throws {
        // Fetch habits by ID to ensure we're working with objects from this context
        let habitIds = habits.map { $0.id }
        
        for (index, habitId) in habitIds.enumerated() {
            let descriptor = FetchDescriptor<Habit>(
                predicate: #Predicate<Habit> { $0.id == habitId }
            )
            
            if let habit = try context.fetch(descriptor).first {
                habit.displayOrder = index + 1 // Start from 1, not 0
            }
        }
        
        try context.save()
    }
    
    func initializeDisplayOrderForExistingHabits() throws {
        let descriptor = FetchDescriptor<Habit>(
            predicate: #Predicate<Habit> { !$0.isArchived && $0.displayOrder == 0 },
            sortBy: [SortDescriptor(\.createdAt)]
        )
        let habitsNeedingOrder = try context.fetch(descriptor)
        
        for (index, habit) in habitsNeedingOrder.enumerated() {
            habit.displayOrder = index + 1 // Start from 1 to avoid conflicts
        }
        
        if !habitsNeedingOrder.isEmpty {
            try context.save()
        }
    }
    
    func updateHabit(
        _ habit: Habit,
        name: String,
        category: HabitCategory,
        timesPerDay: Int,
        frequency: FrequencyType,
        customDays: Set<DayOfWeek> = [],
        reminderEnabled: Bool = false,
        reminderTime: Date? = nil
    ) throws {
        habit.name = name
        habit.categoryRawValue = category.rawValue
        habit.timesPerDay = timesPerDay
        habit.frequencyRawValue = frequency.rawValue
        habit.customDaysRawValue = customDays.map { $0.rawValue }
        habit.reminderEnabled = reminderEnabled
        habit.reminderTime = reminderTime
        
        try context.save()
        
        // Reschedule notifications after successful update
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
            if habit.reminderEnabled {
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        }
        
        // Cache invalidation after update
        invalidateCacheAfterChange(habitId: habit.id)
    }
    
    // MARK: - Habit Record Management
    
    func toggleHabitCompletion(_ habit: Habit) throws {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // Find existing record for today
        if let existingRecord = habit.records?.first(where: { calendar.isDate($0.date, inSameDayAs: today) }) {
            // Progressive completion based on timesPerDay
            let currentLevel = Int(existingRecord.completionLevel)
            
            if currentLevel >= habit.timesPerDay {
                // Currently at max, reset to 0
                existingRecord.completionLevel = 0
            } else {
                // Increment by 1
                existingRecord.completionLevel = Int16(currentLevel + 1)
            }
        } else {
            // No record for today, create new one with level 1
            let newRecord = HabitRecord(date: today, completionLevel: 1, habit: habit)
            context.insert(newRecord)
            habit.records?.append(newRecord)
        }
        
        try context.save()
        
        // Cache invalidation after record change
        invalidateCacheAfterChange(habitId: habit.id)
    }
    
    func updateHabitRecordLevel(_ habit: Habit, level: Int) throws {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        if let existingRecord = habit.records?.first(where: { calendar.isDate($0.date, inSameDayAs: today) }) {
            existingRecord.completionLevel = Int16(min(level, habit.timesPerDay))
        } else {
            let newRecord = HabitRecord(date: today, completionLevel: Int16(min(level, habit.timesPerDay)), habit: habit)
            context.insert(newRecord)
            habit.records?.append(newRecord)
        }
        
        try context.save()
        
        // Cache invalidation after record change
        invalidateCacheAfterChange(habitId: habit.id)
    }
    
    // MARK: - Archive Management
    
    func archiveHabit(_ habit: Habit) throws {
        // Cancel notifications when archiving
        Task {
            print("🗄️ Archiving habit: \(habit.name), cancelling notifications...")
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        habit.archive()
        try context.save()
        print("✅ Habit \(habit.name) archived successfully")
    }
    
    func restoreHabit(_ habit: Habit) throws {
        habit.restore()
        try context.save()
        
        // Reschedule notifications when restoring - ONLY if habit has reminders enabled
        if habit.reminderEnabled {
            Task {
                print("🔄 Restoring habit: \(habit.name) with reminders enabled, rescheduling notifications...")
                await notificationManager.scheduleNotificationsForHabit(habit)
            }
        } else {
            print("🔄 Restoring habit: \(habit.name) without reminders - no notifications scheduled")
        }
    }
    
    func permanentlyDeleteHabit(_ habit: Habit) throws {
        // Cancel notifications before permanent deletion
        Task {
            await notificationManager.cancelNotificationsForHabit(habitId: habit.id)
        }
        
        context.delete(habit)
        try context.save()
    }
    
    // MARK: - Optimized Record Operations
    
    func addOrUpdateRecord(for habitId: UUID, date: Date, completionLevel: Int16, note: String? = nil) async throws {
        let dateKey = formatDateKey(date)
        
        // Get or create cache for this habit
        if recordCache[habitId] == nil {
            await indexRecordsForHabit(habitId)
        }
        
        if let existingRecord = recordCache[habitId]?[dateKey] {
            // Update existing record
            existingRecord.completionLevel = completionLevel
            // Note: HabitRecord doesn't currently have a note field, skipping for now
        } else {
            // Create new record
            let normalizedDate = Calendar.current.startOfDay(for: date)
            let newRecord = HabitRecord(date: normalizedDate, completionLevel: completionLevel)
            
            // Add to SwiftData
            context.insert(newRecord)
            
            // Find and link to habit
            do {
                let descriptor = FetchDescriptor<Habit>(
                    predicate: #Predicate { $0.id == habitId }
                )
                if let habit = try context.fetch(descriptor).first {
                    newRecord.habit = habit
                    habit.records = (habit.records ?? []) + [newRecord]
                }
            } catch {
                print("Error linking record to habit: \(error)")
            }
            
            // Update cache
            if recordCache[habitId] == nil {
                recordCache[habitId] = [:]
            }
            recordCache[habitId]?[dateKey] = newRecord
        }
        
        try context.save()
        
        // Cache is already updated, no need to invalidate
    }
    
    func deleteRecord(for habitId: UUID, on date: Date) async throws {
        let dateKey = formatDateKey(date)
        
        if let record = await getRecord(for: habitId, on: date) {
            context.delete(record)
            recordCache[habitId]?.removeValue(forKey: dateKey)
            try context.save()
        }
    }
    
    // MARK: - Cache Management
    
    private func formatDateKey(_ date: Date) -> String {
        return dateFormatter.string(from: date)
    }
    
    private func indexRecordsForHabit(_ habitId: UUID) async {
        do {
            let descriptor = FetchDescriptor<Habit>(
                predicate: #Predicate { $0.id == habitId }
            )
            
            guard let habit = try context.fetch(descriptor).first else { return }
            
            var indexedRecords: [String: HabitRecord] = [:]
            
            if let records = habit.records {
                for record in records {
                    let dateKey = formatDateKey(record.date)
                    indexedRecords[dateKey] = record
                }
            }
            
            recordCache[habitId] = indexedRecords
        } catch {
            print("Error indexing records for habit \(habitId): \(error)")
        }
    }
    
    func getRecord(for habitId: UUID, on date: Date) async -> HabitRecord? {
        // Check if cache exists
        if recordCache[habitId] == nil {
            await indexRecordsForHabit(habitId)
        }
        
        let dateKey = formatDateKey(date)
        return recordCache[habitId]?[dateKey]
    }
    
    func invalidateCache(for habitId: UUID) {
        recordCache.removeValue(forKey: habitId)
    }
    
    func preloadCacheForHabits(_ habitIds: [UUID]) async {
        await withTaskGroup(of: Void.self) { group in
            for habitId in habitIds {
                group.addTask {
                    await self.indexRecordsForHabit(habitId)
                }
            }
        }
    }
    
    // MARK: - Phase 4: Memory Management & Observation
    
    private func setupModelObservation() {
        // Handle memory pressure notifications
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleMemoryPressure()
            }
        }
    }
    
    // Invalidate cache when habit data changes - called manually from CRUD operations
    private func invalidateCacheAfterChange(habitId: UUID) {
        invalidateCache(for: habitId)
    }
    
    @MainActor
    func handleMemoryPressure() {
        print("🧠 Memory pressure detected, cleaning up caches...")
        
        // Get active habit IDs
        let activeHabitIds: Set<UUID>
        do {
            let activeHabits = try fetchHabits()
            activeHabitIds = Set(activeHabits.map { $0.id })
        } catch {
            print("Error fetching active habits for memory cleanup: \(error)")
            // Fallback: clear all caches
            recordCache.removeAll()
            return
        }
        
        // Clear caches for inactive/archived habits
        let originalCacheCount = recordCache.count
        for (habitId, _) in recordCache {
            if !activeHabitIds.contains(habitId) {
                recordCache.removeValue(forKey: habitId)
            }
        }
        
        let clearedCount = originalCacheCount - recordCache.count
        if clearedCount > 0 {
            print("🧹 Cleared \(clearedCount) inactive habit caches")
        }
        
        // Additional memory optimization: clear very large caches
        for (habitId, cache) in recordCache {
            if cache.count > 1000 { // Arbitrary threshold for "large" cache
                recordCache.removeValue(forKey: habitId)
                print("🗑️ Cleared large cache for habit \(habitId) (\(cache.count) records)")
            }
        }
    }
    
    // MARK: - Cache Statistics & Debugging
    
    func getCacheStatistics() -> CacheStatistics {
        let totalHabits = recordCache.count
        let totalRecords = recordCache.values.reduce(0) { $0 + $1.count }
        let largestCache = recordCache.values.map { $0.count }.max() ?? 0
        
        return CacheStatistics(
            cachedHabits: totalHabits,
            totalCachedRecords: totalRecords,
            largestCacheSize: largestCache,
            memoryEstimate: estimateMemoryUsage()
        )
    }
    
    private func estimateMemoryUsage() -> Int {
        // Rough estimate: each cache entry ~100 bytes
        let totalRecords = recordCache.values.reduce(0) { $0 + $1.count }
        return totalRecords * 100
    }
    
    func clearAllCaches() {
        recordCache.removeAll()
        print("🧹 All caches cleared manually")
    }
    
    // Clean up observers on deinit
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
